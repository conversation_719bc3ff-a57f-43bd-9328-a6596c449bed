[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customer-balances\\route.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customers\\route.ts": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customers\\[id]\\route.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\payments\\route.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\payments\\[id]\\route.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\profile-picture\\route.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\CustomerAvatar.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\CustomerProfileModal.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSkeleton.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\PaymentModal.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductImageZoom.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductQuickActions.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\exportUtils.ts": "52"}, {"size": 6252, "mtime": 1752759696815, "results": "53", "hashOfConfig": "54"}, {"size": 3713, "mtime": 1752667904512, "results": "55", "hashOfConfig": "54"}, {"size": 2769, "mtime": 1752667738579, "results": "56", "hashOfConfig": "54"}, {"size": 2675, "mtime": 1752667748235, "results": "57", "hashOfConfig": "54"}, {"size": 2624, "mtime": 1752667766642, "results": "58", "hashOfConfig": "54"}, {"size": 2413, "mtime": 1752667776200, "results": "59", "hashOfConfig": "54"}, {"size": 6060, "mtime": 1752667359628, "results": "60", "hashOfConfig": "54"}, {"size": 1416, "mtime": 1752667790738, "results": "61", "hashOfConfig": "54"}, {"size": 11603, "mtime": 1752190925916, "results": "62", "hashOfConfig": "54"}, {"size": 1496, "mtime": 1752667805767, "results": "63", "hashOfConfig": "54"}, {"size": 8375, "mtime": 1752667821109, "results": "64", "hashOfConfig": "54"}, {"size": 753, "mtime": 1752667835295, "results": "65", "hashOfConfig": "54"}, {"size": 13054, "mtime": 1752759675978, "results": "66", "hashOfConfig": "54"}, {"size": 42086, "mtime": 1752667892061, "results": "67", "hashOfConfig": "54"}, {"size": 13119, "mtime": 1752668002625, "results": "68", "hashOfConfig": "54"}, {"size": 56392, "mtime": 1752667532295, "results": "69", "hashOfConfig": "54"}, {"size": 32560, "mtime": 1752667568264, "results": "70", "hashOfConfig": "54"}, {"size": 30994, "mtime": 1752510024594, "results": "71", "hashOfConfig": "54"}, {"size": 7440, "mtime": 1752324845264, "results": "72", "hashOfConfig": "54"}, {"size": 63633, "mtime": 1753076557187, "results": "73", "hashOfConfig": "54"}, {"size": 63258, "mtime": 1752667935683, "results": "74", "hashOfConfig": "54"}, {"size": 61481, "mtime": 1752667483151, "results": "75", "hashOfConfig": "54"}, {"size": 1676, "mtime": 1752843704490, "results": "76", "hashOfConfig": "54"}, {"size": 1188, "mtime": 1751950590317, "results": "77", "hashOfConfig": "54"}, {"size": 8950, "mtime": 1752669792087, "results": "78", "hashOfConfig": "54"}, {"size": 42990, "mtime": 1753076641742, "results": "79", "hashOfConfig": "54"}, {"size": 1905, "mtime": 1751950118275, "results": "80", "hashOfConfig": "54"}, {"size": 67668, "mtime": 1753076654954, "results": "81", "hashOfConfig": "54"}, {"size": 18126, "mtime": 1752514964304, "results": "82", "hashOfConfig": "54"}, {"size": 344, "mtime": 1752212829480, "results": "83", "hashOfConfig": "54"}, {"size": 3296, "mtime": 1752212374913, "results": "84", "hashOfConfig": "54"}, {"size": 2521, "mtime": 1751950064561, "results": "85", "hashOfConfig": "54"}, {"size": 10472, "mtime": 1752668016369, "results": "86", "hashOfConfig": "54"}, {"size": 5112, "mtime": 1752668046758, "results": "87", "hashOfConfig": "54"}, {"size": 1025, "mtime": 1752217088143, "results": "88", "hashOfConfig": "54"}, {"size": 4939, "mtime": 1752668033228, "results": "89", "hashOfConfig": "54"}, {"size": 1995, "mtime": 1753016217643, "results": "90", "hashOfConfig": "54"}, {"size": 2234, "mtime": 1752217075185, "results": "91", "hashOfConfig": "54"}, {"size": 5694, "mtime": 1752212408642, "results": "92", "hashOfConfig": "54"}, {"size": 2796, "mtime": 1752771988448, "results": "93", "hashOfConfig": "54"}, {"size": 3649, "mtime": 1753016254016, "results": "94", "hashOfConfig": "54"}, {"size": 3144, "mtime": 1753016277227, "results": "95", "hashOfConfig": "54"}, {"size": 3245, "mtime": 1752771955100, "results": "96", "hashOfConfig": "54"}, {"size": 2768, "mtime": 1752771972100, "results": "97", "hashOfConfig": "54"}, {"size": 3416, "mtime": 1753074614729, "results": "98", "hashOfConfig": "54"}, {"size": 5146, "mtime": 1753076708776, "results": "99", "hashOfConfig": "54"}, {"size": 16056, "mtime": 1753074710953, "results": "100", "hashOfConfig": "54"}, {"size": 4842, "mtime": 1752752836783, "results": "101", "hashOfConfig": "54"}, {"size": 14731, "mtime": 1753076722275, "results": "102", "hashOfConfig": "54"}, {"size": 3084, "mtime": 1753076750225, "results": "103", "hashOfConfig": "54"}, {"size": 3952, "mtime": 1753076611978, "results": "104", "hashOfConfig": "54"}, {"size": 4690, "mtime": 1753075788403, "results": "105", "hashOfConfig": "54"}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10tv68i", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 1, "fixableWarningCount": 3, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts", ["262", "263"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx", ["264", "265"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx", ["266", "267", "268"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx", ["269", "270", "271"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx", ["272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx", ["285", "286", "287"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx", ["288", "289", "290"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx", ["291", "292", "293", "294", "295", "296"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx", ["297", "298"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx", ["299", "300", "301"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx", ["302"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts", ["303", "304", "305", "306", "307"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts", ["308"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts", ["309"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts", ["310", "311", "312", "313", "314"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts", ["315", "316", "317", "318", "319"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customer-balances\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customers\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customers\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\payments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\profile-picture\\route.ts", ["320", "321"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\CustomerAvatar.tsx", ["322", "323", "324", "325", "326"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\CustomerProfileModal.tsx", ["327", "328", "329", "330"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSkeleton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\PaymentModal.tsx", ["331", "332", "333"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductImageZoom.tsx", ["334", "335"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductQuickActions.tsx", ["336", "337", "338", "339", "340"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\exportUtils.ts", ["341"], [], {"ruleId": "342", "severity": 1, "message": "343", "line": 1, "column": 1, "nodeType": "344", "endLine": 1, "endColumn": 59, "fix": "345"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 56, "fix": "347"}, {"ruleId": "342", "severity": 1, "message": "348", "line": 6, "column": 1, "nodeType": "344", "endLine": 6, "endColumn": 39, "fix": "349"}, {"ruleId": "342", "severity": 1, "message": "350", "line": 7, "column": 1, "nodeType": "344", "endLine": 7, "endColumn": 74, "fix": "351"}, {"ruleId": "342", "severity": 1, "message": "352", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 44, "fix": "353"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 6, "column": 1, "nodeType": "344", "endLine": 6, "endColumn": 39, "fix": "354"}, {"ruleId": "355", "severity": 1, "message": "356", "line": 58, "column": 5, "nodeType": "357", "messageId": "358", "endLine": 58, "endColumn": 16, "suggestions": "359"}, {"ruleId": "342", "severity": 1, "message": "360", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 39, "fix": "361"}, {"ruleId": "342", "severity": 1, "message": "362", "line": 5, "column": 1, "nodeType": "344", "endLine": 18, "endColumn": 22, "fix": "363"}, {"ruleId": "342", "severity": 1, "message": "364", "line": 19, "column": 1, "nodeType": "344", "endLine": 19, "endColumn": 39, "fix": "365"}, {"ruleId": "342", "severity": 1, "message": "366", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 66, "fix": "367"}, {"ruleId": "342", "severity": 1, "message": "368", "line": 5, "column": 1, "nodeType": "344", "endLine": 5, "endColumn": 39, "fix": "369"}, {"ruleId": "370", "severity": 1, "message": "371", "line": 203, "column": 6, "nodeType": "372", "endLine": 203, "endColumn": 13, "suggestions": "373"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 241, "column": 27, "nodeType": "376", "messageId": "377", "endLine": 241, "endColumn": 30, "suggestions": "378"}, {"ruleId": "370", "severity": 1, "message": "379", "line": 391, "column": 7, "nodeType": "372", "endLine": 391, "endColumn": 62, "suggestions": "380"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 413, "column": 27, "nodeType": "376", "messageId": "377", "endLine": 413, "endColumn": 30, "suggestions": "381"}, {"ruleId": "370", "severity": 1, "message": "382", "line": 529, "column": 7, "nodeType": "372", "endLine": 529, "endColumn": 42, "suggestions": "383"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 551, "column": 27, "nodeType": "376", "messageId": "377", "endLine": 551, "endColumn": 30, "suggestions": "384"}, {"ruleId": "370", "severity": 1, "message": "382", "line": 631, "column": 7, "nodeType": "372", "endLine": 631, "endColumn": 46, "suggestions": "385"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 665, "column": 29, "nodeType": "376", "messageId": "377", "endLine": 665, "endColumn": 32, "suggestions": "386"}, {"ruleId": "370", "severity": 1, "message": "382", "line": 732, "column": 6, "nodeType": "372", "endLine": 732, "endColumn": 21, "suggestions": "387"}, {"ruleId": "370", "severity": 1, "message": "388", "line": 823, "column": 7, "nodeType": "372", "endLine": 823, "endColumn": 71, "suggestions": "389"}, {"ruleId": "370", "severity": 1, "message": "390", "line": 900, "column": 6, "nodeType": "372", "endLine": 900, "endColumn": 40, "suggestions": "391"}, {"ruleId": "342", "severity": 1, "message": "392", "line": 17, "column": 1, "nodeType": "344", "endLine": 17, "endColumn": 58, "fix": "393"}, {"ruleId": "342", "severity": 1, "message": "394", "line": 18, "column": 1, "nodeType": "344", "endLine": 18, "endColumn": 70, "fix": "395"}, {"ruleId": "342", "severity": 1, "message": "396", "line": 19, "column": 1, "nodeType": "344", "endLine": 19, "endColumn": 48, "fix": "397"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 31, "fix": "398"}, {"ruleId": "342", "severity": 1, "message": "399", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 31, "fix": "400"}, {"ruleId": "342", "severity": 1, "message": "362", "line": 6, "column": 1, "nodeType": "344", "endLine": 6, "endColumn": 50, "fix": "401"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 17, "column": 1, "nodeType": "344", "endLine": 17, "endColumn": 68, "fix": "402"}, {"ruleId": "342", "severity": 1, "message": "403", "line": 17, "column": 1, "nodeType": "344", "endLine": 17, "endColumn": 68, "fix": "404"}, {"ruleId": "342", "severity": 1, "message": "405", "line": 18, "column": 1, "nodeType": "344", "endLine": 18, "endColumn": 103, "fix": "406"}, {"ruleId": "407", "severity": 2, "message": "408", "line": 89, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 89, "endColumn": 17, "fix": "411"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 118, "column": 19, "nodeType": "376", "messageId": "377", "endLine": 118, "endColumn": 22, "suggestions": "412"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 118, "column": 32, "nodeType": "376", "messageId": "377", "endLine": 118, "endColumn": 35, "suggestions": "413"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 44, "fix": "414"}, {"ruleId": "342", "severity": 1, "message": "415", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 44, "fix": "416"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 31, "fix": "417"}, {"ruleId": "342", "severity": 1, "message": "399", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 31, "fix": "418"}, {"ruleId": "342", "severity": 1, "message": "362", "line": 6, "column": 1, "nodeType": "344", "endLine": 12, "endColumn": 22, "fix": "419"}, {"ruleId": "342", "severity": 1, "message": "352", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 44, "fix": "420"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 43, "fix": "421"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 99, "column": 44, "nodeType": "376", "messageId": "377", "endLine": 99, "endColumn": 47, "suggestions": "422"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 124, "column": 21, "nodeType": "376", "messageId": "377", "endLine": 124, "endColumn": 24, "suggestions": "423"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 134, "column": 65, "nodeType": "376", "messageId": "377", "endLine": 134, "endColumn": 68, "suggestions": "424"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 151, "column": 44, "nodeType": "376", "messageId": "377", "endLine": 151, "endColumn": 47, "suggestions": "425"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 1, "column": 1, "nodeType": "344", "endLine": 1, "endColumn": 46, "fix": "426"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 1, "column": 1, "nodeType": "344", "endLine": 1, "endColumn": 53, "fix": "427"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 29, "column": 32, "nodeType": "376", "messageId": "377", "endLine": 29, "endColumn": 35, "suggestions": "428"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 49, "column": 29, "nodeType": "376", "messageId": "377", "endLine": 49, "endColumn": 32, "suggestions": "429"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 58, "column": 34, "nodeType": "376", "messageId": "377", "endLine": 58, "endColumn": 37, "suggestions": "430"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 96, "column": 30, "nodeType": "376", "messageId": "377", "endLine": 96, "endColumn": 33, "suggestions": "431"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 97, "column": 35, "nodeType": "376", "messageId": "377", "endLine": 97, "endColumn": 38, "suggestions": "432"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 65, "fix": "433"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 82, "column": 47, "nodeType": "376", "messageId": "377", "endLine": 82, "endColumn": 50, "suggestions": "434"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 91, "column": 47, "nodeType": "376", "messageId": "377", "endLine": 91, "endColumn": 50, "suggestions": "435"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 191, "column": 46, "nodeType": "376", "messageId": "377", "endLine": 191, "endColumn": 49, "suggestions": "436"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 191, "column": 56, "nodeType": "376", "messageId": "377", "endLine": 191, "endColumn": 59, "suggestions": "437"}, {"ruleId": "342", "severity": 1, "message": "438", "line": 2, "column": 1, "nodeType": "344", "endLine": 2, "endColumn": 46, "fix": "439"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 69, "column": 36, "nodeType": "376", "messageId": "377", "endLine": 69, "endColumn": 39, "suggestions": "440"}, {"ruleId": "342", "severity": 1, "message": "399", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 31, "fix": "441"}, {"ruleId": "342", "severity": 1, "message": "362", "line": 5, "column": 1, "nodeType": "344", "endLine": 5, "endColumn": 44, "fix": "442"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 140, "column": 58, "nodeType": "376", "messageId": "377", "endLine": 140, "endColumn": 61, "suggestions": "443"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 155, "column": 52, "nodeType": "376", "messageId": "377", "endLine": 155, "endColumn": 55, "suggestions": "444"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 172, "column": 93, "nodeType": "376", "messageId": "377", "endLine": 172, "endColumn": 96, "suggestions": "445"}, {"ruleId": "342", "severity": 1, "message": "446", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 52, "fix": "447"}, {"ruleId": "342", "severity": 1, "message": "448", "line": 5, "column": 1, "nodeType": "344", "endLine": 5, "endColumn": 39, "fix": "449"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 6, "column": 1, "nodeType": "344", "endLine": 6, "endColumn": 31, "fix": "450"}, {"ruleId": "355", "severity": 1, "message": "356", "line": 161, "column": 9, "nodeType": "357", "messageId": "358", "endLine": 161, "endColumn": 20, "suggestions": "451"}, {"ruleId": "342", "severity": 1, "message": "352", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 44, "fix": "452"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 5, "column": 1, "nodeType": "344", "endLine": 5, "endColumn": 39, "fix": "453"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 6, "column": 1, "nodeType": "344", "endLine": 6, "endColumn": 49, "fix": "454"}, {"ruleId": "342", "severity": 1, "message": "352", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 33, "fix": "455"}, {"ruleId": "342", "severity": 1, "message": "456", "line": 4, "column": 1, "nodeType": "344", "endLine": 4, "endColumn": 31, "fix": "457"}, {"ruleId": "342", "severity": 1, "message": "352", "line": 3, "column": 1, "nodeType": "344", "endLine": 3, "endColumn": 52, "fix": "458"}, {"ruleId": "342", "severity": 1, "message": "346", "line": 8, "column": 1, "nodeType": "344", "endLine": 8, "endColumn": 39, "fix": "459"}, {"ruleId": "355", "severity": 1, "message": "356", "line": 58, "column": 21, "nodeType": "357", "messageId": "358", "endLine": 58, "endColumn": 32}, {"ruleId": "355", "severity": 1, "message": "356", "line": 76, "column": 21, "nodeType": "357", "messageId": "358", "endLine": 76, "endColumn": 32}, {"ruleId": "355", "severity": 1, "message": "356", "line": 82, "column": 21, "nodeType": "357", "messageId": "358", "endLine": 82, "endColumn": 32}, {"ruleId": "374", "severity": 1, "message": "375", "line": 110, "column": 27, "nodeType": "376", "messageId": "377", "endLine": 110, "endColumn": 30, "suggestions": "460"}, "import/order", "There should be no empty line within import group", "ImportDeclaration", {"range": "461", "text": "462"}, "There should be at least one empty line between import groups", {"range": "463", "text": "464"}, "`framer-motion` import should occur before import of `next/link`", {"range": "465", "text": "466"}, "`lucide-react` import should occur before import of `next/link`", {"range": "467", "text": "468"}, "`react` import should occur after import of `next-themes`", {"range": "469", "text": "470"}, {"range": "471", "text": "464"}, "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["472"], "`next-themes` import should occur before import of `react`", {"range": "473", "text": "474"}, "`lucide-react` import should occur before import of `react`", {"range": "475", "text": "476"}, "`framer-motion` import should occur before import of `react`", {"range": "477", "text": "478"}, "`react` import should occur after import of `lucide-react`", {"range": "479", "text": "480"}, "`next-themes` import should occur after import of `lucide-react`", {"range": "481", "text": "482"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'selectedCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["483"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["484", "485"], "React Hook useMemo has missing dependencies: 'filters.showTrends' and 'getChartTheme'. Either include them or remove the dependency array.", ["486"], ["487", "488"], "React Hook useMemo has a missing dependency: 'getChartTheme'. Either include it or remove the dependency array.", ["489"], ["490", "491"], ["492"], ["493", "494"], ["495"], "React Hook useMemo has missing dependencies: 'chartData.performanceMetrics.efficiency' and 'getChartTheme'. Either include them or remove the dependency array. Mutable values like 'chartData.performanceMetrics.efficiency.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["496"], "React Hook useMemo has an unnecessary dependency: 'stats.totalDebtAmount'. Either exclude it or remove the dependency array.", ["497"], "`./CustomerProfileModal` import should occur before import of `./DebtModal`", {"range": "498", "text": "499"}, "`./CustomerAvatar` import should occur before import of `./DebtModal`", {"range": "500", "text": "501"}, "`./LoadingSkeleton` import should occur before import of `./PaymentModal`", {"range": "502", "text": "503"}, {"range": "504", "text": "462"}, "`next/image` import should occur before import of `react`", {"range": "505", "text": "506"}, {"range": "507", "text": "508"}, {"range": "509", "text": "464"}, "`./ProductImageZoom` import should occur before import of `./ProductModal`", {"range": "510", "text": "511"}, "`@/utils/exportUtils` import should occur before import of `./LoadingSkeleton`", {"range": "512", "text": "513"}, "prefer-const", "'filtered' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "514", "text": "515"}, ["516", "517"], ["518", "519"], {"range": "520", "text": "464"}, "`next/navigation` import should occur before import of `react`", {"range": "521", "text": "522"}, {"range": "523", "text": "462"}, {"range": "524", "text": "525"}, {"range": "526", "text": "527"}, {"range": "528", "text": "529"}, {"range": "530", "text": "464"}, ["531", "532"], ["533", "534"], ["535", "536"], ["537", "538"], {"range": "539", "text": "464"}, {"range": "540", "text": "464"}, ["541", "542"], ["543", "544"], ["545", "546"], ["547", "548"], ["549", "550"], {"range": "551", "text": "464"}, ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], "`cloudinary` import should occur before import of `next/server`", {"range": "560", "text": "561"}, ["562", "563"], {"range": "564", "text": "565"}, {"range": "566", "text": "567"}, ["568", "569"], ["570", "571"], ["572", "573"], "`react` import should occur after import of `next/image`", {"range": "574", "text": "575"}, "`next-themes` import should occur after import of `next/image`", {"range": "576", "text": "577"}, {"range": "578", "text": "464"}, ["579"], {"range": "580", "text": "581"}, {"range": "582", "text": "464"}, {"range": "583", "text": "464"}, {"range": "584", "text": "585"}, "`next/image` import should occur after import of `lucide-react`", {"range": "586", "text": "587"}, {"range": "588", "text": "589"}, {"range": "590", "text": "464"}, ["591", "592"], [59, 60], "", [115, 115], "\n", [14, 170], "import { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n", [14, 244], "import { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\n", [14, 218], "import { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [217, 217], {"fix": "593", "messageId": "594", "data": "595", "desc": "596"}, [14, 105], "import { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n", [14, 264], "import {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 303], "import { motion } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\n", [14, 398], "import ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useEffect, useState, useMemo, useCallback } from 'react'\n", [125, 398], "import {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n", {"desc": "597", "fix": "598"}, {"messageId": "599", "fix": "600", "desc": "601"}, {"messageId": "602", "fix": "603", "desc": "604"}, {"desc": "605", "fix": "606"}, {"messageId": "599", "fix": "607", "desc": "601"}, {"messageId": "602", "fix": "608", "desc": "604"}, {"desc": "609", "fix": "610"}, {"messageId": "599", "fix": "611", "desc": "601"}, {"messageId": "602", "fix": "612", "desc": "604"}, {"desc": "613", "fix": "614"}, {"messageId": "599", "fix": "615", "desc": "601"}, {"messageId": "602", "fix": "616", "desc": "604"}, {"desc": "617", "fix": "618"}, {"desc": "619", "fix": "620"}, {"desc": "621", "fix": "622"}, [576, 712], "import CustomerProfileModal from './CustomerProfileModal'\nimport DebtModal from './DebtModal'\nimport PaymentModal from './PaymentModal'\n", [576, 782], "import CustomerAvatar, { useCustomerProfile } from './CustomerAvatar'\nimport DebtModal from './DebtModal'\nimport PaymentModal from './PaymentModal'\nimport CustomerProfileModal from './CustomerProfileModal'\n", [612, 830], "import LoadingSkeleton from './LoadingSkeleton'\nimport PaymentModal from './PaymentModal'\nimport CustomerProfileModal from './CustomerProfileModal'\nimport CustomerAvatar, { useCustomerProfile } from './CustomerAvatar'\n", [89, 90], [14, 89], "import Image from 'next/image'\nimport { useState, useEffect } from 'react'\n", [14, 140], "import { X, Upload, Package } from 'lucide-react'\nimport { useState, useEffect } from 'react'\nimport Image from 'next/image'\n\n", [654, 654], [489, 655], "import ProductImageZoom, { useImageZoom } from './ProductImageZoom'\nimport ProductModal from './ProductModal'\nimport ProductQuickActions from './ProductQuickActions'\n", [441, 758], "import { exportProductsToCSV, exportProductsToJSON, exportAnalyticsReport } from '@/utils/exportUtils'\nimport LoadingSkeleton from './LoadingSkeleton'\nimport ProductModal from './ProductModal'\nimport ProductQuickActions from './ProductQuickActions'\nimport ProductImageZoom, { useImageZoom } from './ProductImageZoom'\n", [3067, 4102], "const filtered = products.filter(product => {\n      // Text search\n      const matchesSearch = !searchTerm ||\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.category.toLowerCase().includes(searchTerm.toLowerCase())\n\n      // Category filter\n      const matchesCategory = !filters.category || product.category === filters.category\n\n      // Price range filter\n      const matchesPrice = product.price >= filters.priceRange.min &&\n        product.price <= filters.priceRange.max\n\n      // Stock range filter\n      const matchesStock = product.stock_quantity >= filters.stockRange.min &&\n        product.stock_quantity <= filters.stockRange.max\n\n      // Low stock filter\n      const matchesLowStock = !filters.lowStock || product.stock_quantity < 10\n\n      // Out of stock filter\n      const matchesOutOfStock = !filters.outOfStock || product.stock_quantity === 0\n\n      return matchesSearch && matchesCategory && matchesPrice &&\n        matchesStock && matchesLowStock && matchesOutOfStock\n    })", {"messageId": "599", "fix": "623", "desc": "601"}, {"messageId": "602", "fix": "624", "desc": "604"}, {"messageId": "599", "fix": "625", "desc": "601"}, {"messageId": "602", "fix": "626", "desc": "604"}, [91, 91], [14, 92], "import { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n", [86, 87], [14, 86], "import Image from 'next/image'\nimport { useState, useRef } from 'react'\n", [14, 414], "import {\n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Camera, Eye, EyeOff, Key, Smartphone, Globe,\n  Mail, Phone, Trash2, Plus, Edit3, Check, AlertTriangle,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive\n} from 'lucide-react'\nimport { useState, useRef } from 'react'\nimport Image from 'next/image'\n\n", [14, 213], "import {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight,\n  Bot\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [114, 114], {"messageId": "599", "fix": "627", "desc": "601"}, {"messageId": "602", "fix": "628", "desc": "604"}, {"messageId": "599", "fix": "629", "desc": "601"}, {"messageId": "602", "fix": "630", "desc": "604"}, {"messageId": "599", "fix": "631", "desc": "601"}, {"messageId": "602", "fix": "632", "desc": "604"}, {"messageId": "599", "fix": "633", "desc": "601"}, {"messageId": "602", "fix": "634", "desc": "604"}, [45, 45], [52, 52], {"messageId": "599", "fix": "635", "desc": "601"}, {"messageId": "602", "fix": "636", "desc": "604"}, {"messageId": "599", "fix": "637", "desc": "601"}, {"messageId": "602", "fix": "638", "desc": "604"}, {"messageId": "599", "fix": "639", "desc": "601"}, {"messageId": "602", "fix": "640", "desc": "604"}, {"messageId": "599", "fix": "641", "desc": "601"}, {"messageId": "602", "fix": "642", "desc": "604"}, {"messageId": "599", "fix": "643", "desc": "601"}, {"messageId": "602", "fix": "644", "desc": "604"}, [93, 93], {"messageId": "599", "fix": "645", "desc": "601"}, {"messageId": "602", "fix": "646", "desc": "604"}, {"messageId": "599", "fix": "647", "desc": "601"}, {"messageId": "602", "fix": "648", "desc": "604"}, {"messageId": "599", "fix": "649", "desc": "601"}, {"messageId": "602", "fix": "650", "desc": "604"}, {"messageId": "599", "fix": "651", "desc": "601"}, {"messageId": "602", "fix": "652", "desc": "604"}, [0, 102], "import { v2 as cloudinary } from 'cloudinary'\nimport { NextRequest, NextResponse } from 'next/server'\n", {"messageId": "599", "fix": "653", "desc": "601"}, {"messageId": "602", "fix": "654", "desc": "604"}, [14, 78], "import Image from 'next/image'\nimport { useState } from 'react'\n", [14, 122], "import { User, Camera } from 'lucide-react'\nimport { useState } from 'react'\nimport Image from 'next/image'\n", {"messageId": "599", "fix": "655", "desc": "601"}, {"messageId": "602", "fix": "656", "desc": "604"}, {"messageId": "599", "fix": "657", "desc": "601"}, {"messageId": "602", "fix": "658", "desc": "604"}, {"messageId": "599", "fix": "659", "desc": "601"}, {"messageId": "602", "fix": "660", "desc": "604"}, [14, 216], "import { X, User, Camera, Phone, MapPin, FileText, Trash2 } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport Image from 'next/image'\nimport { useState, useEffect, useRef } from 'react'\n", [146, 216], "import Image from 'next/image'\nimport { useTheme } from 'next-themes'\n", [215, 215], {"fix": "661", "messageId": "594", "data": "662", "desc": "596"}, [14, 186], "import { X, DollarSign, CreditCard, Calendar, FileText, Calculator } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [185, 185], [234, 234], [14, 159], "import Image from 'next/image'\nimport { X, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState } from 'react'\n", [47, 120], "import { X, Package } from 'lucide-react'\nimport Image from 'next/image'\n", [14, 206], "import {\n  MoreVertical, Edit, Trash2, Eye, Copy, Star,\n  TrendingUp, Download\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n", [205, 205], {"messageId": "599", "fix": "663", "desc": "601"}, {"messageId": "602", "fix": "664", "desc": "604"}, {"range": "665", "text": "462"}, "removeConsole", {"propertyName": "666"}, "Remove the console.log().", "Update the dependencies array to be: [selectedCategory, stats]", {"range": "667", "text": "668"}, "suggestUnknown", {"range": "669", "text": "670"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "671", "text": "672"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", {"range": "673", "text": "674"}, {"range": "675", "text": "670"}, {"range": "676", "text": "672"}, "Update the dependencies array to be: [chartData.debtData, getChartTheme, resolvedTheme]", {"range": "677", "text": "678"}, {"range": "679", "text": "670"}, {"range": "680", "text": "672"}, "Update the dependencies array to be: [chartData.categoryData, getChartTheme, resolvedTheme]", {"range": "681", "text": "682"}, {"range": "683", "text": "670"}, {"range": "684", "text": "672"}, "Update the dependencies array to be: [getChartTheme, resolvedTheme]", {"range": "685", "text": "686"}, "Update the dependencies array to be: [chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", {"range": "687", "text": "688"}, "Update the dependencies array to be: [chartData]", {"range": "689", "text": "690"}, {"range": "691", "text": "670"}, {"range": "692", "text": "672"}, {"range": "693", "text": "670"}, {"range": "694", "text": "672"}, {"range": "695", "text": "670"}, {"range": "696", "text": "672"}, {"range": "697", "text": "670"}, {"range": "698", "text": "672"}, {"range": "699", "text": "670"}, {"range": "700", "text": "672"}, {"range": "701", "text": "670"}, {"range": "702", "text": "672"}, {"range": "703", "text": "670"}, {"range": "704", "text": "672"}, {"range": "705", "text": "670"}, {"range": "706", "text": "672"}, {"range": "707", "text": "670"}, {"range": "708", "text": "672"}, {"range": "709", "text": "670"}, {"range": "710", "text": "672"}, {"range": "711", "text": "670"}, {"range": "712", "text": "672"}, {"range": "713", "text": "670"}, {"range": "714", "text": "672"}, {"range": "715", "text": "670"}, {"range": "716", "text": "672"}, {"range": "717", "text": "670"}, {"range": "718", "text": "672"}, {"range": "719", "text": "670"}, {"range": "720", "text": "672"}, {"range": "721", "text": "670"}, {"range": "722", "text": "672"}, {"range": "723", "text": "670"}, {"range": "724", "text": "672"}, {"range": "725", "text": "670"}, {"range": "726", "text": "672"}, {"range": "727", "text": "670"}, {"range": "728", "text": "672"}, {"range": "729", "text": "462"}, {"propertyName": "666"}, {"range": "730", "text": "670"}, {"range": "731", "text": "672"}, [1430, 1472], "log", [6482, 6489], "[selected<PERSON>ategory, stats]", [7642, 7645], "unknown", [7642, 7645], "never", [11678, 11733], "[chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", [12390, 12393], [12390, 12393], [15425, 15460], "[chartData.debtData, getChartTheme, resolvedTheme]", [16120, 16123], [16120, 16123], [18522, 18561], "[chartData.categoryData, getChartTheme, resolvedTheme]", [19638, 19641], [19638, 19641], [21439, 21454], "[getChartTheme, resolvedTheme]", [23570, 23634], "[chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", [27272, 27306], "[chartData]", [4167, 4170], [4167, 4170], [4180, 4183], [4180, 4183], [2131, 2134], [2131, 2134], [2784, 2787], [2784, 2787], [3038, 3041], [3038, 3041], [3467, 3470], [3467, 3470], [633, 636], [633, 636], [1044, 1047], [1044, 1047], [1211, 1214], [1211, 1214], [1967, 1970], [1967, 1970], [2027, 2030], [2027, 2030], [2410, 2413], [2410, 2413], [2612, 2615], [2612, 2615], [5337, 5340], [5337, 5340], [5347, 5350], [5347, 5350], [2103, 2106], [2103, 2106], [3855, 3858], [3855, 3858], [4464, 4467], [4464, 4467], [4935, 4938], [4935, 4938], [4754, 4805], [3051, 3054], [3051, 3054]]