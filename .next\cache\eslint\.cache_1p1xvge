[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts": "39"}, {"size": 5708, "mtime": 1752515097450, "results": "40", "hashOfConfig": "41"}, {"size": 3713, "mtime": 1752667904512, "results": "42", "hashOfConfig": "41"}, {"size": 2769, "mtime": 1752667738579, "results": "43", "hashOfConfig": "41"}, {"size": 2675, "mtime": 1752667748235, "results": "44", "hashOfConfig": "41"}, {"size": 2624, "mtime": 1752667766642, "results": "45", "hashOfConfig": "41"}, {"size": 2413, "mtime": 1752667776200, "results": "46", "hashOfConfig": "41"}, {"size": 6060, "mtime": 1752667359628, "results": "47", "hashOfConfig": "41"}, {"size": 1416, "mtime": 1752667790738, "results": "48", "hashOfConfig": "41"}, {"size": 11603, "mtime": 1752190925916, "results": "49", "hashOfConfig": "41"}, {"size": 1496, "mtime": 1752667805767, "results": "50", "hashOfConfig": "41"}, {"size": 8375, "mtime": 1752667821109, "results": "51", "hashOfConfig": "41"}, {"size": 753, "mtime": 1752667835295, "results": "52", "hashOfConfig": "41"}, {"size": 10479, "mtime": 1752667986303, "results": "53", "hashOfConfig": "41"}, {"size": 42086, "mtime": 1752667892061, "results": "54", "hashOfConfig": "41"}, {"size": 13119, "mtime": 1752668002625, "results": "55", "hashOfConfig": "41"}, {"size": 56392, "mtime": 1752667532295, "results": "56", "hashOfConfig": "41"}, {"size": 32560, "mtime": 1752667568264, "results": "57", "hashOfConfig": "41"}, {"size": 30994, "mtime": 1752510024594, "results": "58", "hashOfConfig": "41"}, {"size": 7440, "mtime": 1752324845264, "results": "59", "hashOfConfig": "41"}, {"size": 7823, "mtime": 1752061478940, "results": "60", "hashOfConfig": "41"}, {"size": 63258, "mtime": 1752667935683, "results": "61", "hashOfConfig": "41"}, {"size": 61481, "mtime": 1752667483151, "results": "62", "hashOfConfig": "41"}, {"size": 1308, "mtime": 1752515037632, "results": "63", "hashOfConfig": "41"}, {"size": 1188, "mtime": 1751950590317, "results": "64", "hashOfConfig": "41"}, {"size": 8950, "mtime": 1752669792087, "results": "65", "hashOfConfig": "41"}, {"size": 9241, "mtime": 1752669816194, "results": "66", "hashOfConfig": "41"}, {"size": 1905, "mtime": 1751950118275, "results": "67", "hashOfConfig": "41"}, {"size": 67520, "mtime": 1752669933723, "results": "68", "hashOfConfig": "41"}, {"size": 18126, "mtime": 1752514964304, "results": "69", "hashOfConfig": "41"}, {"size": 344, "mtime": 1752212829480, "results": "70", "hashOfConfig": "41"}, {"size": 3296, "mtime": 1752212374913, "results": "71", "hashOfConfig": "41"}, {"size": 2521, "mtime": 1751950064561, "results": "72", "hashOfConfig": "41"}, {"size": 10472, "mtime": 1752668016369, "results": "73", "hashOfConfig": "41"}, {"size": 5112, "mtime": 1752668046758, "results": "74", "hashOfConfig": "41"}, {"size": 1025, "mtime": 1752217088143, "results": "75", "hashOfConfig": "41"}, {"size": 4939, "mtime": 1752668033228, "results": "76", "hashOfConfig": "41"}, {"size": 1266, "mtime": 1752212686019, "results": "77", "hashOfConfig": "41"}, {"size": 2234, "mtime": 1752217075185, "results": "78", "hashOfConfig": "41"}, {"size": 5694, "mtime": 1752212408642, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10tv68i", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts", ["197", "198"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx", ["199", "200"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx", ["201", "202"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx", ["203", "204", "205"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx", ["206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx", ["219", "220", "221", "222", "223", "224", "225"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx", ["226", "227", "228"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx", ["229", "230", "231", "232", "233"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx", ["234", "235"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx", ["236", "237", "238"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx", ["239"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts", ["240", "241", "242", "243", "244"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts", ["245"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts", ["246"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts", ["247", "248", "249", "250", "251"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts", ["252", "253", "254", "255", "256"], [], {"ruleId": "257", "severity": 1, "message": "258", "line": 1, "column": 1, "nodeType": "259", "endLine": 1, "endColumn": 59, "fix": "260"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 56, "fix": "262"}, {"ruleId": "257", "severity": 1, "message": "263", "line": 6, "column": 1, "nodeType": "259", "endLine": 6, "endColumn": 39, "fix": "264"}, {"ruleId": "257", "severity": 1, "message": "265", "line": 7, "column": 1, "nodeType": "259", "endLine": 7, "endColumn": 74, "fix": "266"}, {"ruleId": "257", "severity": 1, "message": "267", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 44, "fix": "268"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 6, "column": 1, "nodeType": "259", "endLine": 6, "endColumn": 39, "fix": "269"}, {"ruleId": "257", "severity": 1, "message": "270", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 39, "fix": "271"}, {"ruleId": "257", "severity": 1, "message": "272", "line": 5, "column": 1, "nodeType": "259", "endLine": 18, "endColumn": 22, "fix": "273"}, {"ruleId": "257", "severity": 1, "message": "274", "line": 19, "column": 1, "nodeType": "259", "endLine": 19, "endColumn": 39, "fix": "275"}, {"ruleId": "257", "severity": 1, "message": "276", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 66, "fix": "277"}, {"ruleId": "257", "severity": 1, "message": "278", "line": 5, "column": 1, "nodeType": "259", "endLine": 5, "endColumn": 39, "fix": "279"}, {"ruleId": "280", "severity": 1, "message": "281", "line": 203, "column": 6, "nodeType": "282", "endLine": 203, "endColumn": 13, "suggestions": "283"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 241, "column": 27, "nodeType": "286", "messageId": "287", "endLine": 241, "endColumn": 30, "suggestions": "288"}, {"ruleId": "280", "severity": 1, "message": "289", "line": 391, "column": 7, "nodeType": "282", "endLine": 391, "endColumn": 62, "suggestions": "290"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 413, "column": 27, "nodeType": "286", "messageId": "287", "endLine": 413, "endColumn": 30, "suggestions": "291"}, {"ruleId": "280", "severity": 1, "message": "292", "line": 529, "column": 7, "nodeType": "282", "endLine": 529, "endColumn": 42, "suggestions": "293"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 551, "column": 27, "nodeType": "286", "messageId": "287", "endLine": 551, "endColumn": 30, "suggestions": "294"}, {"ruleId": "280", "severity": 1, "message": "292", "line": 631, "column": 7, "nodeType": "282", "endLine": 631, "endColumn": 46, "suggestions": "295"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 665, "column": 29, "nodeType": "286", "messageId": "287", "endLine": 665, "endColumn": 32, "suggestions": "296"}, {"ruleId": "280", "severity": 1, "message": "292", "line": 732, "column": 6, "nodeType": "282", "endLine": 732, "endColumn": 21, "suggestions": "297"}, {"ruleId": "280", "severity": 1, "message": "298", "line": 823, "column": 7, "nodeType": "282", "endLine": 823, "endColumn": 71, "suggestions": "299"}, {"ruleId": "280", "severity": 1, "message": "300", "line": 900, "column": 6, "nodeType": "282", "endLine": 900, "endColumn": 40, "suggestions": "301"}, {"ruleId": "257", "severity": 1, "message": "272", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 75, "fix": "302"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 5, "column": 1, "nodeType": "259", "endLine": 5, "endColumn": 39, "fix": "303"}, {"ruleId": "257", "severity": 1, "message": "270", "line": 5, "column": 1, "nodeType": "259", "endLine": 5, "endColumn": 39, "fix": "304"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 6, "column": 1, "nodeType": "259", "endLine": 6, "endColumn": 36, "fix": "305"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 7, "column": 1, "nodeType": "259", "endLine": 7, "endColumn": 46, "fix": "306"}, {"ruleId": "257", "severity": 1, "message": "307", "line": 7, "column": 1, "nodeType": "259", "endLine": 7, "endColumn": 46, "fix": "308"}, {"ruleId": "257", "severity": 1, "message": "309", "line": 8, "column": 1, "nodeType": "259", "endLine": 8, "endColumn": 34, "fix": "310"}, {"ruleId": "257", "severity": 1, "message": "258", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 31, "fix": "311"}, {"ruleId": "257", "severity": 1, "message": "312", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 31, "fix": "313"}, {"ruleId": "257", "severity": 1, "message": "272", "line": 6, "column": 1, "nodeType": "259", "endLine": 6, "endColumn": 50, "fix": "314"}, {"ruleId": "257", "severity": 1, "message": "267", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 44, "fix": "315"}, {"ruleId": "257", "severity": 1, "message": "258", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 31, "fix": "316"}, {"ruleId": "257", "severity": 1, "message": "317", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 31, "fix": "318"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 9, "column": 1, "nodeType": "259", "endLine": 9, "endColumn": 42, "fix": "319"}, {"ruleId": "257", "severity": 1, "message": "320", "line": 9, "column": 1, "nodeType": "259", "endLine": 9, "endColumn": 42, "fix": "321"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 44, "fix": "322"}, {"ruleId": "257", "severity": 1, "message": "323", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 44, "fix": "324"}, {"ruleId": "257", "severity": 1, "message": "258", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 31, "fix": "325"}, {"ruleId": "257", "severity": 1, "message": "312", "line": 4, "column": 1, "nodeType": "259", "endLine": 4, "endColumn": 31, "fix": "326"}, {"ruleId": "257", "severity": 1, "message": "272", "line": 6, "column": 1, "nodeType": "259", "endLine": 12, "endColumn": 22, "fix": "327"}, {"ruleId": "257", "severity": 1, "message": "267", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 44, "fix": "328"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 43, "fix": "329"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 99, "column": 44, "nodeType": "286", "messageId": "287", "endLine": 99, "endColumn": 47, "suggestions": "330"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 124, "column": 21, "nodeType": "286", "messageId": "287", "endLine": 124, "endColumn": 24, "suggestions": "331"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 134, "column": 65, "nodeType": "286", "messageId": "287", "endLine": 134, "endColumn": 68, "suggestions": "332"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 151, "column": 44, "nodeType": "286", "messageId": "287", "endLine": 151, "endColumn": 47, "suggestions": "333"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 1, "column": 1, "nodeType": "259", "endLine": 1, "endColumn": 46, "fix": "334"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 1, "column": 1, "nodeType": "259", "endLine": 1, "endColumn": 53, "fix": "335"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 29, "column": 32, "nodeType": "286", "messageId": "287", "endLine": 29, "endColumn": 35, "suggestions": "336"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 49, "column": 29, "nodeType": "286", "messageId": "287", "endLine": 49, "endColumn": 32, "suggestions": "337"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 58, "column": 34, "nodeType": "286", "messageId": "287", "endLine": 58, "endColumn": 37, "suggestions": "338"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 96, "column": 30, "nodeType": "286", "messageId": "287", "endLine": 96, "endColumn": 33, "suggestions": "339"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 97, "column": 35, "nodeType": "286", "messageId": "287", "endLine": 97, "endColumn": 38, "suggestions": "340"}, {"ruleId": "257", "severity": 1, "message": "261", "line": 3, "column": 1, "nodeType": "259", "endLine": 3, "endColumn": 65, "fix": "341"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 82, "column": 47, "nodeType": "286", "messageId": "287", "endLine": 82, "endColumn": 50, "suggestions": "342"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 91, "column": 47, "nodeType": "286", "messageId": "287", "endLine": 91, "endColumn": 50, "suggestions": "343"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 191, "column": 46, "nodeType": "286", "messageId": "287", "endLine": 191, "endColumn": 49, "suggestions": "344"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 191, "column": 56, "nodeType": "286", "messageId": "287", "endLine": 191, "endColumn": 59, "suggestions": "345"}, "import/order", "There should be no empty line within import group", "ImportDeclaration", {"range": "346", "text": "347"}, "There should be at least one empty line between import groups", {"range": "348", "text": "349"}, "`framer-motion` import should occur before import of `next/link`", {"range": "350", "text": "351"}, "`lucide-react` import should occur before import of `next/link`", {"range": "352", "text": "353"}, "`react` import should occur after import of `next-themes`", {"range": "354", "text": "355"}, {"range": "356", "text": "349"}, "`next-themes` import should occur before import of `react`", {"range": "357", "text": "358"}, "`lucide-react` import should occur before import of `react`", {"range": "359", "text": "360"}, "`framer-motion` import should occur before import of `react`", {"range": "361", "text": "362"}, "`react` import should occur after import of `lucide-react`", {"range": "363", "text": "364"}, "`next-themes` import should occur after import of `lucide-react`", {"range": "365", "text": "366"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'selectedCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["367"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["368", "369"], "React Hook useMemo has missing dependencies: 'filters.showTrends' and 'getChartTheme'. Either include them or remove the dependency array.", ["370"], ["371", "372"], "React Hook useMemo has a missing dependency: 'getChartTheme'. Either include it or remove the dependency array.", ["373"], ["374", "375"], ["376"], ["377", "378"], ["379"], "React Hook useMemo has missing dependencies: 'chartData.performanceMetrics.efficiency' and 'getChartTheme'. Either include them or remove the dependency array. Mutable values like 'chartData.performanceMetrics.efficiency.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["380"], "React Hook useMemo has an unnecessary dependency: 'stats.totalDebtAmount'. Either exclude it or remove the dependency array.", ["381"], {"range": "382", "text": "383"}, {"range": "384", "text": "349"}, {"range": "385", "text": "386"}, {"range": "387", "text": "349"}, {"range": "388", "text": "349"}, "`@/lib/supabase` import should occur before import of `./DebtModal`", {"range": "389", "text": "390"}, "`date-fns` import should occur before import of `react`", {"range": "391", "text": "392"}, {"range": "393", "text": "347"}, "`next/image` import should occur before import of `react`", {"range": "394", "text": "395"}, {"range": "396", "text": "397"}, {"range": "398", "text": "399"}, {"range": "400", "text": "347"}, "`next/image` import should occur after import of `lucide-react`", {"range": "401", "text": "402"}, {"range": "403", "text": "349"}, "`./ProductModal` import should occur after import of `@/lib/supabase`", {"range": "404", "text": "405"}, {"range": "406", "text": "349"}, "`next/navigation` import should occur before import of `react`", {"range": "407", "text": "408"}, {"range": "409", "text": "347"}, {"range": "410", "text": "411"}, {"range": "412", "text": "413"}, {"range": "414", "text": "415"}, {"range": "416", "text": "349"}, ["417", "418"], ["419", "420"], ["421", "422"], ["423", "424"], {"range": "425", "text": "349"}, {"range": "426", "text": "349"}, ["427", "428"], ["429", "430"], ["431", "432"], ["433", "434"], ["435", "436"], {"range": "437", "text": "349"}, ["438", "439"], ["440", "441"], ["442", "443"], ["444", "445"], [59, 60], "", [115, 115], "\n", [14, 170], "import { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n", [14, 244], "import { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\n", [14, 218], "import { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [217, 217], [14, 105], "import { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n", [14, 264], "import {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 303], "import { motion } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\n", [14, 398], "import ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useEffect, useState, useMemo, useCallback } from 'react'\n", [125, 398], "import {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n", {"desc": "446", "fix": "447"}, {"messageId": "448", "fix": "449", "desc": "450"}, {"messageId": "451", "fix": "452", "desc": "453"}, {"desc": "454", "fix": "455"}, {"messageId": "448", "fix": "456", "desc": "450"}, {"messageId": "451", "fix": "457", "desc": "453"}, {"desc": "458", "fix": "459"}, {"messageId": "448", "fix": "460", "desc": "450"}, {"messageId": "451", "fix": "461", "desc": "453"}, {"desc": "462", "fix": "463"}, {"messageId": "448", "fix": "464", "desc": "450"}, {"messageId": "451", "fix": "465", "desc": "453"}, {"desc": "466", "fix": "467"}, {"desc": "468", "fix": "469"}, {"desc": "470", "fix": "471"}, [14, 133], "import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [171, 171], [14, 172], "import { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\n", [207, 207], [253, 253], [172, 254], "import { CustomerDebt } from '@/lib/supabase'\nimport DebtModal from './DebtModal'\n", [14, 288], "import { format } from 'date-fns'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\n", [89, 90], [14, 89], "import Image from 'next/image'\nimport { useState, useEffect } from 'react'\n", [14, 140], "import { X, Upload, Package } from 'lucide-react'\nimport { useState, useEffect } from 'react'\nimport Image from 'next/image'\n\n", [14, 196], "import Image from 'next/image'\n\nimport { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [89, 90], [58, 157], "\nimport { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport Image from 'next/image'\n", [238, 238], [197, 300], "import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\nimport ProductModal from './ProductModal'\n", [91, 91], [14, 92], "import { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n", [86, 87], [14, 86], "import Image from 'next/image'\nimport { useState, useRef } from 'react'\n", [14, 414], "import {\n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Camera, Eye, EyeOff, Key, Smartphone, Globe,\n  Mail, Phone, Trash2, Plus, Edit3, Check, AlertTriangle,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive\n} from 'lucide-react'\nimport { useState, useRef } from 'react'\nimport Image from 'next/image'\n\n", [14, 213], "import {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight,\n  Bot\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [114, 114], {"messageId": "448", "fix": "472", "desc": "450"}, {"messageId": "451", "fix": "473", "desc": "453"}, {"messageId": "448", "fix": "474", "desc": "450"}, {"messageId": "451", "fix": "475", "desc": "453"}, {"messageId": "448", "fix": "476", "desc": "450"}, {"messageId": "451", "fix": "477", "desc": "453"}, {"messageId": "448", "fix": "478", "desc": "450"}, {"messageId": "451", "fix": "479", "desc": "453"}, [45, 45], [52, 52], {"messageId": "448", "fix": "480", "desc": "450"}, {"messageId": "451", "fix": "481", "desc": "453"}, {"messageId": "448", "fix": "482", "desc": "450"}, {"messageId": "451", "fix": "483", "desc": "453"}, {"messageId": "448", "fix": "484", "desc": "450"}, {"messageId": "451", "fix": "485", "desc": "453"}, {"messageId": "448", "fix": "486", "desc": "450"}, {"messageId": "451", "fix": "487", "desc": "453"}, {"messageId": "448", "fix": "488", "desc": "450"}, {"messageId": "451", "fix": "489", "desc": "453"}, [93, 93], {"messageId": "448", "fix": "490", "desc": "450"}, {"messageId": "451", "fix": "491", "desc": "453"}, {"messageId": "448", "fix": "492", "desc": "450"}, {"messageId": "451", "fix": "493", "desc": "453"}, {"messageId": "448", "fix": "494", "desc": "450"}, {"messageId": "451", "fix": "495", "desc": "453"}, {"messageId": "448", "fix": "496", "desc": "450"}, {"messageId": "451", "fix": "497", "desc": "453"}, "Update the dependencies array to be: [selectedCategory, stats]", {"range": "498", "text": "499"}, "suggestUnknown", {"range": "500", "text": "501"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "502", "text": "503"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", {"range": "504", "text": "505"}, {"range": "506", "text": "501"}, {"range": "507", "text": "503"}, "Update the dependencies array to be: [chartData.debtData, getChartTheme, resolvedTheme]", {"range": "508", "text": "509"}, {"range": "510", "text": "501"}, {"range": "511", "text": "503"}, "Update the dependencies array to be: [chartData.categoryData, getChartTheme, resolvedTheme]", {"range": "512", "text": "513"}, {"range": "514", "text": "501"}, {"range": "515", "text": "503"}, "Update the dependencies array to be: [getChartTheme, resolvedTheme]", {"range": "516", "text": "517"}, "Update the dependencies array to be: [chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", {"range": "518", "text": "519"}, "Update the dependencies array to be: [chartData]", {"range": "520", "text": "521"}, {"range": "522", "text": "501"}, {"range": "523", "text": "503"}, {"range": "524", "text": "501"}, {"range": "525", "text": "503"}, {"range": "526", "text": "501"}, {"range": "527", "text": "503"}, {"range": "528", "text": "501"}, {"range": "529", "text": "503"}, {"range": "530", "text": "501"}, {"range": "531", "text": "503"}, {"range": "532", "text": "501"}, {"range": "533", "text": "503"}, {"range": "534", "text": "501"}, {"range": "535", "text": "503"}, {"range": "536", "text": "501"}, {"range": "537", "text": "503"}, {"range": "538", "text": "501"}, {"range": "539", "text": "503"}, {"range": "540", "text": "501"}, {"range": "541", "text": "503"}, {"range": "542", "text": "501"}, {"range": "543", "text": "503"}, {"range": "544", "text": "501"}, {"range": "545", "text": "503"}, {"range": "546", "text": "501"}, {"range": "547", "text": "503"}, [6482, 6489], "[selected<PERSON>ategory, stats]", [7642, 7645], "unknown", [7642, 7645], "never", [11678, 11733], "[chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", [12390, 12393], [12390, 12393], [15425, 15460], "[chartData.debtData, getChartTheme, resolvedTheme]", [16120, 16123], [16120, 16123], [18522, 18561], "[chartData.categoryData, getChartTheme, resolvedTheme]", [19638, 19641], [19638, 19641], [21439, 21454], "[getChartTheme, resolvedTheme]", [23570, 23634], "[chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", [27272, 27306], "[chartData]", [2131, 2134], [2131, 2134], [2784, 2787], [2784, 2787], [3038, 3041], [3038, 3041], [3467, 3470], [3467, 3470], [633, 636], [633, 636], [1044, 1047], [1044, 1047], [1211, 1214], [1211, 1214], [1967, 1970], [1967, 1970], [2027, 2030], [2027, 2030], [2410, 2413], [2410, 2413], [2612, 2615], [2612, 2615], [5337, 5340], [5337, 5340], [5347, 5350], [5347, 5350]]