'use client'

import { useState, useEffect } from 'react'
import { X, DollarSign, CreditCard, Calendar, FileText, Calculator } from 'lucide-react'
import { useTheme } from 'next-themes'
import { CustomerPayment } from '@/lib/supabase'
import CustomerAvatar from './CustomerAvatar'

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  customerName?: string
  customerFamilyName?: string
  currentBalance?: number
  payment?: CustomerPayment | null
  profilePictureUrl?: string
}

const PAYMENT_METHODS = [
  'Cash',
  'GCash',
  'PayMaya',
  'Bank Transfer',
  'Credit Card',
  'Debit Card',
  'Other'
]

export default function PaymentModal({
  isOpen,
  onClose,
  customerName = '',
  customerFamilyName = '',
  currentBalance = 0,
  payment,
  profilePictureUrl
}: PaymentModalProps) {
  const { resolvedTheme } = useTheme()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_family_name: '',
    payment_amount: '',
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: 'Cash',
    notes: ''
  })

  useEffect(() => {
    if (isOpen) {
      if (payment) {
        // Editing existing payment
        setFormData({
          customer_name: payment.customer_name,
          customer_family_name: payment.customer_family_name,
          payment_amount: payment.payment_amount.toString(),
          payment_date: payment.payment_date,
          payment_method: payment.payment_method,
          notes: payment.notes || ''
        })
      } else {
        // New payment with pre-filled customer info
        setFormData({
          customer_name: customerName,
          customer_family_name: customerFamilyName,
          payment_amount: '',
          payment_date: new Date().toISOString().split('T')[0],
          payment_method: 'Cash',
          notes: ''
        })
      }
    }
  }, [isOpen, payment, customerName, customerFamilyName])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const paymentData = {
        ...formData,
        payment_amount: parseFloat(formData.payment_amount)
      }

      const url = payment ? `/api/payments/${payment.id}` : '/api/payments'
      const method = payment ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData),
      })

      if (response.ok) {
        onClose()
      } else {
        console.error('Error saving payment record')
      }
    } catch (error) {
      console.error('Error saving payment record:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePayFullBalance = () => {
    setFormData(prev => ({
      ...prev,
      payment_amount: currentBalance.toString()
    }))
  }

  const remainingAfterPayment = currentBalance - parseFloat(formData.payment_amount || '0')

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className="w-full max-w-md rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        {/* Header */}
        <div
          className="flex justify-between items-center p-6 border-b"
          style={{
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}
        >
          <div className="flex items-center gap-3">
            {customerName && customerFamilyName ? (
              <CustomerAvatar
                customerName={customerName}
                customerFamilyName={customerFamilyName}
                profilePictureUrl={profilePictureUrl}
                size="md"
                showEditButton={false}
              />
            ) : (
              <div className="p-2 rounded-lg bg-green-100">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
            )}
            <div>
              <h2
                className="text-xl font-semibold"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                {payment ? 'Edit Payment' : 'Record Payment'}
              </h2>
              {customerName && customerFamilyName && (
                <p
                  className="text-sm"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  {customerName} {customerFamilyName}
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" style={{
              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
            }} />
          </button>
        </div>

        {/* Balance Info */}
        {!payment && currentBalance > 0 && (
          <div
            className="p-4 m-6 rounded-lg border"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e2e8f0'
            }}
          >
            <div className="flex items-center justify-between mb-3">
              <span
                className="text-sm font-medium"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                Current Balance
              </span>
              <span className="text-lg font-bold text-red-600">
                ₱{currentBalance.toFixed(2)}
              </span>
            </div>
            
            {formData.payment_amount && (
              <div className="flex items-center justify-between mb-3">
                <span
                  className="text-sm"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  After Payment
                </span>
                <span className={`text-sm font-medium ${
                  remainingAfterPayment <= 0 ? 'text-green-600' : 'text-orange-600'
                }`}>
                  ₱{remainingAfterPayment.toFixed(2)}
                </span>
              </div>
            )}
            
            <button
              type="button"
              onClick={handlePayFullBalance}
              className="w-full px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm font-medium"
            >
              <Calculator className="h-4 w-4 inline mr-2" />
              Pay Full Balance
            </button>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Customer Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                First Name *
              </label>
              <input
                type="text"
                value={formData.customer_name}
                onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}
                className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                required
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                Last Name *
              </label>
              <input
                type="text"
                value={formData.customer_family_name}
                onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}
                className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                required
              />
            </div>
          </div>

          {/* Payment Amount */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              Payment Amount (₱) *
            </label>
            <input
              type="number"
              step="0.01"
              min="0.01"
              value={formData.payment_amount}
              onChange={(e) => setFormData({ ...formData, payment_amount: e.target.value })}
              className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="0.00"
              required
            />
          </div>

          {/* Payment Date and Method */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <Calendar className="h-4 w-4 inline mr-1" />
                Payment Date
              </label>
              <input
                type="date"
                value={formData.payment_date}
                onChange={(e) => setFormData({ ...formData, payment_date: e.target.value })}
                className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                required
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <CreditCard className="h-4 w-4 inline mr-1" />
                Payment Method
              </label>
              <select
                value={formData.payment_method}
                onChange={(e) => setFormData({ ...formData, payment_method: e.target.value })}
                className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                {PAYMENT_METHODS.map(method => (
                  <option key={method} value={method}>{method}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              <FileText className="h-4 w-4 inline mr-1" />
              Notes (Optional)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 resize-none"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="Add any additional notes about this payment..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border rounded-lg font-medium transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : payment ? 'Update Payment' : 'Record Payment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
