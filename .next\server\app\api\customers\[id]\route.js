(()=>{var e={};e.id=77,e.ids=[77],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,t,r)=>{"use strict";r.d(t,{$W:()=>i});var s=r(72289),o=r(35282);let a=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),n=function(){try{return a.parse(process.env)}catch(e){if(e instanceof o.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===n.NODE_ENV,isProduction:"production"===n.NODE_ENV,isTest:"test"===n.NODE_ENV,database:{url:n.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:n.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:n.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:n.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:n.CLOUDINARY_API_KEY,apiSecret:n.CLOUDINARY_API_SECRET},ai:{geminiApiKey:n.GEMINI_API_KEY},auth:{secret:n.NEXTAUTH_SECRET,url:n.NEXTAUTH_URL},debug:n.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:p,NEXT_PUBLIC_SUPABASE_ANON_KEY:l,SUPABASE_SERVICE_ROLE_KEY:c,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:m,NEXTAUTH_URL:N,DEBUG:x,GEMINI_API_KEY:f}=n},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(66437),o=r(34960);let a=(0,s.UU)(o.$W.database.url,o.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>N,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>p,PUT:()=>l});var o=r(96559),a=r(48088),n=r(37719),i=r(32190),u=r(56621);async function p(e,{params:t}){try{let{id:e}=await t,{data:r,error:s}=await u.N.from("customers").select("*").eq("id",e).single();if(s)return i.NextResponse.json({error:s.message},{status:500});if(!r)return i.NextResponse.json({error:"Customer not found"},{status:404});return i.NextResponse.json({customer:r})}catch{return i.NextResponse.json({error:"Failed to fetch customer"},{status:500})}}async function l(e,{params:t}){try{let{id:r}=await t,{customer_name:s,customer_family_name:o,profile_picture_url:a,profile_picture_public_id:n,phone_number:p,address:l,notes:c}=await e.json();if(!s||!o)return i.NextResponse.json({error:"Customer name and family name are required"},{status:400});let{data:d,error:E}=await u.N.from("customers").update({customer_name:s,customer_family_name:o,profile_picture_url:a||null,profile_picture_public_id:n||null,phone_number:p||null,address:l||null,notes:c||null}).eq("id",r).select().single();if(E)return i.NextResponse.json({error:E.message},{status:500});return i.NextResponse.json({customer:d})}catch{return i.NextResponse.json({error:"Failed to update customer"},{status:500})}}async function c(e,{params:t}){try{let{id:e}=await t,{data:r}=await u.N.from("customer_debts").select("id").eq("customer_name",(await u.N.from("customers").select("customer_name").eq("id",e).single()).data?.customer_name).limit(1);if(r&&r.length>0)return i.NextResponse.json({error:"Cannot delete customer with existing debt records"},{status:400});let{error:s}=await u.N.from("customers").delete().eq("id",e);if(s)return i.NextResponse.json({error:s.message},{status:500});return i.NextResponse.json({message:"Customer deleted successfully"})}catch{return i.NextResponse.json({error:"Failed to delete customer"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/customers/[id]/route",pathname:"/api/customers/[id]",filename:"route",bundlePath:"app/api/customers/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customers\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:_,serverHooks:m}=d;function N(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:_})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,289,437],()=>r(74012));module.exports=s})();