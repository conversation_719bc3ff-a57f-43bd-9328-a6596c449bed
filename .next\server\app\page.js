(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14012:(e,t,r)=>{Promise.resolve().then(r.bind(r,69500)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,19864))},14138:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var n=r(60687),s=r(10218);function o({children:e,...t}){return(0,n.jsx)(s.N,{...t,children:e})}},16189:(e,t,r)=>{"use strict";var n=r(65773);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19864:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>s});var n=r(12907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx","SettingsProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx","useSettings")},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx","default")},22362:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>a,t:()=>d});var n=r(60687),s=r(43210);let o=(0,s.createContext)(void 0),i={store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",website:"https://revantadstore.com",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"},operatingDays:["monday","tuesday","wednesday","thursday","friday","saturday"],businessRegistration:{registrationNumber:"REG-2024-001",taxId:"TAX-*********",businessType:"Retail",registrationDate:"2024-01-01"},locations:[{id:1,name:"Main Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",isMain:!0}],branding:{logo:null,primaryColor:"#22c55e",secondaryColor:"#facc15",slogan:"Your Neighborhood Store"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner",avatar:null,bio:"Experienced store owner managing Revantad Store operations.",dateOfBirth:"1990-01-01",address:"123 Barangay Street, Manila, Philippines",emergencyContact:{name:"Emergency Contact",phone:"+63 ************",relationship:"Family"},preferences:{language:"en",timezone:"Asia/Manila",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"}},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,channels:{email:"<EMAIL>",sms:"+63 ************",webhook:""},customRules:[{id:1,name:"Critical Stock Alert",condition:"stock < 5",action:"email + sms",enabled:!0}],templates:{lowStock:"Product {{productName}} is running low ({{currentStock}} remaining)",newDebt:"New debt recorded for {{customerName}}: ₱{{amount}}",paymentReceived:"Payment received from {{customerName}}: ₱{{amount}}"}},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5",currentPassword:"",newPassword:"",confirmPassword:"",apiKeys:[{id:1,name:"Main API Key",key:"sk_live_***************",created:"2024-01-01",lastUsed:"2024-01-20",permissions:["read","write"]}],loginHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",ip:"***********",device:"Chrome on Windows",location:"Manila, Philippines",success:!0}],passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!0}},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US",colorScheme:{primary:"#22c55e",secondary:"#facc15",accent:"#3b82f6",background:"#ffffff",surface:"#f8fafc"},layout:{sidebarPosition:"left",density:"comfortable",showAnimations:!0,compactMode:!1},typography:{fontFamily:"Inter",fontSize:"medium",fontWeight:"normal"}},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z",cloudStorage:{provider:"local",bucket:"",accessKey:"",secretKey:""},backupHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",size:"2.5 MB",status:"completed",type:"automatic"},{id:2,timestamp:"2024-01-19T10:30:00Z",size:"2.4 MB",status:"completed",type:"automatic"}],verification:{enabled:!0,lastVerified:"2024-01-20T10:35:00Z",status:"verified"}}};function a({children:e}){let[t,r]=(0,s.useState)(i),[a,d]=(0,s.useState)(!1),[l,c]=(0,s.useState)(!1),u=async()=>{d(!0);try{await new Promise(e=>setTimeout(e,1e3)),localStorage.setItem("revantad-settings",JSON.stringify(t)),c(!1),"dark"===t.appearance.theme?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),console.warn("Settings saved successfully:",t)}catch(e){throw console.error("Error saving settings:",e),e}finally{d(!1)}};return(0,n.jsx)(o.Provider,{value:{settings:t,updateSettings:(e,t)=>{r(r=>({...r,[e]:{...r[e],...t}})),c(!0)},saveSettings:u,resetSettings:e=>{e?r(t=>({...t,[e]:i[e]})):r(i),c(!0)},isLoading:a,hasUnsavedChanges:l},children:e})}function d(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},27107:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx","useAuth");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx","AuthProvider")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41179:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},52977:(e,t,r)=>{Promise.resolve().then(r.bind(r,75694))},61135:()=>{},62705:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>a});var n=r(60687),s=r(43210);let o=(0,s.createContext)(void 0);function i(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function a({children:e}){let[t,r]=(0,s.useState)(null),[i,a]=(0,s.useState)(!0),d=async(e,t)=>{a(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==t)return a(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return r(e),localStorage.setItem("revantad_user",JSON.stringify(e)),a(!1),!0}}catch(e){return console.error("Login error:",e),a(!1),!1}};return(0,n.jsx)(o.Provider,{value:{user:t,login:d,logout:()=>{r(null),localStorage.removeItem("revantad_user")},isLoading:i,isAuthenticated:!!t},children:e})}},67694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var n=r(65239),s=r(48088),o=r(88170),i=r.n(o),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},69500:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx","ThemeProvider")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72156:(e,t,r)=>{Promise.resolve().then(r.bind(r,14138)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,22362))},75694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(60687),s=r(16189);function o(){return(0,s.useRouter)(),(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,n.jsx)("h1",{className:"text-2xl font-bold text-gradient mb-2",children:"Revantad Store"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Redirecting to landing page..."})]})})}r(43210)},79551:e=>{"use strict";e.exports=require("url")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var n=r(37413),s=r(35759),o=r.n(s),i=r(12552),a=r.n(i);r(61135);var d=r(69500),l=r(29131),c=r(19864);let u={title:"Revantad Store - Professional Admin Dashboard",description:"Modern admin dashboard for managing your Revantad Store with product lists, customer debt tracking, and business analytics",keywords:"sari-sari store, admin dashboard, product management, customer debt, business analytics, Philippines"};function m({children:e}){return(0,n.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,n.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,children:(0,n.jsx)(d.ThemeProvider,{attribute:"class",defaultTheme:"light",enableSystem:!1,storageKey:"revantad-theme",themes:["light","dark"],children:(0,n.jsx)(l.AuthProvider,{children:(0,n.jsx)(c.SettingsProvider,{children:e})})})})})}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,215,658],()=>r(67694));module.exports=n})();