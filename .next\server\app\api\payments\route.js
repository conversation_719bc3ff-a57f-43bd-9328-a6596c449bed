(()=>{var e={};e.id=131,e.ids=[131],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,t,r)=>{"use strict";r.d(t,{$W:()=>i});var s=r(72289),n=r(35282);let o=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),a=function(){try{return o.parse(process.env)}catch(e){if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===a.NODE_ENV,isProduction:"production"===a.NODE_ENV,isTest:"test"===a.NODE_ENV,database:{url:a.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:a.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:a.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:a.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:a.CLOUDINARY_API_KEY,apiSecret:a.CLOUDINARY_API_SECRET},ai:{geminiApiKey:a.GEMINI_API_KEY},auth:{secret:a.NEXTAUTH_SECRET,url:a.NEXTAUTH_URL},debug:a.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:c,NEXT_PUBLIC_SUPABASE_ANON_KEY:l,SUPABASE_SERVICE_ROLE_KEY:p,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:m,NEXTAUTH_URL:R,DEBUG:A,GEMINI_API_KEY:N}=a},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45838:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>E,serverHooks:()=>R,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p,OPTIONS:()=>l,POST:()=>d});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(53171),c=r(56621);async function l(){return(0,u.Y8)()}let p=(0,u.FB)(async e=>{let{searchParams:t}=new URL(e.url),{page:r,limit:s,offset:n}=(0,u.u3)(t),o=t.get("search"),a=t.get("dateFrom"),i=t.get("dateTo"),l=t.get("customer"),p=c.N.from("customer_payments").select("*",{count:"exact"}).order("created_at",{ascending:!1}).range(n,n+s-1);if(o&&(p=p.or(`customer_name.ilike.%${o}%,customer_family_name.ilike.%${o}%,notes.ilike.%${o}%`)),a&&(p=p.gte("payment_date",a)),i&&(p=p.lte("payment_date",i)),l){let[e,t]=l.split(" ");p=t?p.eq("customer_name",e).eq("customer_family_name",t):p.or(`customer_name.ilike.%${l}%,customer_family_name.ilike.%${l}%`)}let{data:d,error:E,count:_}=await p;return E?(0,u.ie)(E):(0,u.r6)({payments:d,pagination:{page:r,limit:s,total:_||0,totalPages:Math.ceil((_||0)/s)}})});async function d(e){try{let{customer_name:t,customer_family_name:r,payment_amount:s,payment_date:n,payment_method:o,notes:a}=await e.json();if(!t||!r||!s||s<=0)return i.NextResponse.json({error:"Missing required fields or invalid payment amount"},{status:400});let{data:u,error:l}=await c.N.from("customer_payments").insert([{customer_name:t,customer_family_name:r,payment_amount:parseFloat(s),payment_date:n||new Date().toISOString().split("T")[0],payment_method:o||"Cash",notes:a||null}]).select().single();if(l)return i.NextResponse.json({error:l.message},{status:500});return i.NextResponse.json({payment:u},{status:201})}catch(e){return console.error("Error creating payment:",e),i.NextResponse.json({error:"Failed to create payment record"},{status:500})}}let E=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\payments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:m,serverHooks:R}=E;function A(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:m})}},47990:()=>{},53171:(e,t,r)=>{"use strict";r.d(t,{FB:()=>u,Rv:()=>l,Y8:()=>_,ie:()=>p,r6:()=>a,sv:()=>c,u3:()=>d,yj:()=>i});var s=r(32190);let n={OK:200,BAD_REQUEST:400,CONFLICT:409,INTERNAL_SERVER_ERROR:500};class o extends Error{constructor(e,t=n.INTERNAL_SERVER_ERROR,r){super(e),this.message=e,this.statusCode=t,this.code=r,this.name="ApiError"}}function a(e,t,r=n.OK){let o={success:!0,data:e};return t&&(o.message=t),s.NextResponse.json(o,{status:r})}function i(e,t=n.INTERNAL_SERVER_ERROR,r){return s.NextResponse.json({success:!1,error:e,code:r},{status:t})}function u(e){return async(...t)=>{try{return await e(...t)}catch(e){if(console.error("API Error:",e),e instanceof o)return i(e.message,e.statusCode,e.code);if(e instanceof Error)return i(e.message);return i("An unexpected error occurred")}}}async function c(e,t){try{let r=await e.json();return t(r)}catch{throw new o("Invalid request body",n.BAD_REQUEST)}}function l(e,t){let r=t.filter(t=>void 0===e[t]||null===e[t]||""===e[t]);if(r.length>0)throw new o(`Missing required fields: ${r.join(", ")}`,n.BAD_REQUEST)}function p(e){if(console.error("Database error:",e),"23505"===e.code)throw new o("Resource already exists",n.CONFLICT);if("23503"===e.code)throw new o("Referenced resource not found",n.BAD_REQUEST);if("23502"===e.code)throw new o("Required field is missing",n.BAD_REQUEST);throw new o("Database operation failed",n.INTERNAL_SERVER_ERROR)}function d(e,t={}){let{maxLimit:r=100}=t,s=Math.max(1,parseInt(e.get("page")||"1",10)),n=Math.min(r,Math.max(1,parseInt(e.get("limit")||"10",10)));return{page:s,limit:n,offset:(s-1)*n}}let E={"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"};function _(){return new s.NextResponse(null,{status:200,headers:E})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(66437),n=r(34960);let o=(0,s.UU)(n.$W.database.url,n.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,289,437],()=>r(45838));module.exports=s})();