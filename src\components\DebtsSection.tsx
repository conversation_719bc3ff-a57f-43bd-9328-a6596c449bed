'use client'

import { format, differenceInDays } from 'date-fns'
import {
  Plus, Edit, Trash2, Search, Users, Calendar, Filter, SortAsc, SortDesc,
  Grid, List, Download, CheckSquare, Square, Minus, ChevronDown, BarChart3,
  RefreshCw, Clock, DollarSign, FileText, Calculator, Package, AlertTriangle
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect, useMemo, useCallback } from 'react'

import { CustomerDebt, CustomerBalance, CustomerPayment, Customer } from '@/lib/supabase'
import { exportDebtsToCSV } from '@/utils/exportUtils'

import DebtModal from './DebtModal'
import PaymentModal from './PaymentModal'
import CustomerProfileModal from './CustomerProfileModal'
import CustomerAvatar, { useCustomerProfile } from './CustomerAvatar'
import LoadingSkeleton from './LoadingSkeleton'

interface DebtsSectionProps {
  onStatsUpdate: () => void
}

// Enhanced filter and sort types for debts
type DebtSortOption = 'customer_name' | 'total_amount' | 'debt_date' | 'created_at' | 'product_name'
type SortDirection = 'asc' | 'desc'
type ViewMode = 'grid' | 'list' | 'customer'
type DebtStatus = 'all' | 'recent' | 'overdue' | 'high-amount'

interface DebtFilterOptions {
  search: string
  dateRange: { from: string; to: string }
  amountRange: { min: number; max: number }
  status: DebtStatus
  customer: string
}

interface CustomerSummary {
  customerName: string
  totalDebts: number
  totalAmount: number
  totalPayments: number
  remainingBalance: number
  oldestDebt: string
  recentDebt: string
  debts: CustomerDebt[]
}

export default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {
  const { resolvedTheme } = useTheme()
  const [debts, setDebts] = useState<CustomerDebt[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)

  // Enhanced features state
  const [viewMode, setViewMode] = useState<ViewMode>('customer')
  const [sortBy, setSortBy] = useState<DebtSortOption>('debt_date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [selectedDebts, setSelectedDebts] = useState<Set<string>>(new Set())
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<DebtFilterOptions>({
    search: '',
    dateRange: { from: '', to: '' },
    amountRange: { min: 0, max: 50000 },
    status: 'all',
    customer: ''
  })
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Payment functionality
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [selectedCustomerForPayment, setSelectedCustomerForPayment] = useState<{
    name: string
    familyName: string
    balance: number
  } | null>(null)
  const [customerBalances, setCustomerBalances] = useState<Map<string, CustomerBalance>>(new Map())
  const [editingPayment, setEditingPayment] = useState<CustomerPayment | null>(null)

  // Customer profile functionality
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false)
  const [selectedCustomerForProfile, setSelectedCustomerForProfile] = useState<{
    name: string
    familyName: string
    customer?: Customer
  } | null>(null)
  const [avatarRefreshKey, setAvatarRefreshKey] = useState(0)
  const [customerProfiles, setCustomerProfiles] = useState<Map<string, Customer>>(new Map())
  const { getCustomerProfile, updateCustomerProfile } = useCustomerProfile()

  useEffect(() => {
    fetchDebts()
    fetchCustomerBalances()
    fetchCustomerProfiles()
  }, [])

  const fetchCustomerBalances = async () => {
    try {
      const response = await fetch('/api/customer-balances')
      if (response.ok) {
        const data = await response.json()
        const balanceMap = new Map<string, CustomerBalance>()
        // Check if balances array exists and is an array
        if (data.balances && Array.isArray(data.balances)) {
          data.balances.forEach((balance: CustomerBalance) => {
            const key = `${balance.customer_name} ${balance.customer_family_name}`
            balanceMap.set(key, balance)
          })
        }
        setCustomerBalances(balanceMap)
      }
    } catch (error) {
      console.error('Error fetching customer balances:', error)
      // Set empty map on error to prevent crashes
      setCustomerBalances(new Map())
    }
  }

  const fetchCustomerProfiles = async () => {
    try {
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        const profileMap = new Map<string, Customer>()
        // Check if customers array exists and is an array
        if (data.customers && Array.isArray(data.customers)) {
          data.customers.forEach((customer: Customer) => {
            const key = `${customer.customer_name} ${customer.customer_family_name}`
            profileMap.set(key, customer)
          })
        }
        setCustomerProfiles(profileMap)
      }
    } catch (error) {
      console.error('Error fetching customer profiles:', error)
      // Set empty map on error to prevent crashes
      setCustomerProfiles(new Map())
    }
  }

  const fetchDebts = async () => {
    try {
      // Using console.warn for informational messages (ESLint compliant)
      console.warn('🔄 Fetching debts...')
      const response = await fetch('/api/debts')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.warn('💳 Debts API response:', data)

      // Handle new API structure: { success: true, data: { debts: [...] } }
      if (data.success && data.data && data.data.debts) {
        setDebts(data.data.debts)
        console.warn('✅ Debts loaded (new structure):', data.data.debts.length, 'items')
      }
      // Handle old API structure: { debts: [...] }
      else if (data.debts) {
        setDebts(data.debts)
        console.warn('✅ Debts loaded (old structure):', data.debts.length, 'items')
      }
      // Handle direct array
      else if (Array.isArray(data)) {
        setDebts(data)
        console.warn('✅ Debts loaded (direct array):', data.length, 'items')
      }
      else {
        console.warn('⚠️ Unexpected API response structure:', data)
        setDebts([])
      }
    } catch (error) {
      console.error('❌ Error fetching debts:', error)
      setDebts([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this debt record?')) return

    try {
      const response = await fetch(`/api/debts/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setDebts(debts.filter(d => d.id !== id))
        onStatsUpdate()
      }
    } catch (error) {
      console.error('Error deleting debt:', error)
    }
  }

  const handleEdit = (debt: CustomerDebt) => {
    setEditingDebt(debt)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setEditingDebt(null)
    fetchDebts()
    fetchCustomerBalances()
    onStatsUpdate()
  }

  const handlePaymentModalClose = () => {
    setIsPaymentModalOpen(false)
    setSelectedCustomerForPayment(null)
    setEditingPayment(null)
    fetchCustomerBalances()
    onStatsUpdate()
  }

  const handleMakePayment = (customerName: string, customerFamilyName: string) => {
    const customerKey = `${customerName} ${customerFamilyName}`
    const balance = customerBalances.get(customerKey)

    setSelectedCustomerForPayment({
      name: customerName,
      familyName: customerFamilyName,
      balance: balance?.remaining_balance || 0
    })
    setIsPaymentModalOpen(true)
  }

  const handleProfileModalClose = () => {
    setIsProfileModalOpen(false)
    setSelectedCustomerForProfile(null)
    // Refresh customer data to show updated profiles
    fetchCustomerBalances()
    fetchCustomerProfiles()
  }

  const handleProfileUpdated = (updatedCustomer: Customer) => {
    // Update the customer profile in the local state
    updateCustomerProfile(updatedCustomer.customer_name, updatedCustomer.customer_family_name, updatedCustomer)

    // Update local profiles map
    const key = `${updatedCustomer.customer_name} ${updatedCustomer.customer_family_name}`
    setCustomerProfiles(prev => new Map(prev.set(key, updatedCustomer)))

    // Force avatar refresh by incrementing the key
    setAvatarRefreshKey(prev => prev + 1)

    // Refresh the entire customer data
    fetchCustomerBalances()
    fetchCustomerProfiles()
  }

  const handleEditProfile = async (customerName: string, customerFamilyName: string) => {
    const customer = await getCustomerProfile(customerName, customerFamilyName)
    setSelectedCustomerForProfile({
      name: customerName,
      familyName: customerFamilyName,
      customer
    })
    setIsProfileModalOpen(true)
  }

  // Advanced search suggestions
  const generateSearchSuggestions = useCallback((term: string) => {
    if (!term || term.length < 2) {
      setSearchSuggestions([])
      return
    }

    const suggestions = debts
      .filter(debt => {
        const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()
        const productName = debt.product_name.toLowerCase()
        return customerName.includes(term.toLowerCase()) || productName.includes(term.toLowerCase())
      })
      .map(debt => `${debt.customer_name} ${debt.customer_family_name}`)
      .slice(0, 5)

    setSearchSuggestions([...new Set(suggestions)])
  }, [debts])

  // Advanced filtering and sorting logic
  const filteredAndSortedDebts = useMemo(() => {
    const filtered = debts.filter(debt => {
      // Text search
      const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()
      const productName = debt.product_name.toLowerCase()
      const searchText = (filters.search || searchTerm).toLowerCase()
      const matchesSearch = !searchText ||
        customerName.includes(searchText) ||
        productName.includes(searchText)

      // Date range filter
      const debtDate = new Date(debt.debt_date)
      const matchesDateRange = (!filters.dateRange.from || debtDate >= new Date(filters.dateRange.from)) &&
        (!filters.dateRange.to || debtDate <= new Date(filters.dateRange.to))

      // Amount range filter
      const matchesAmount = debt.total_amount >= filters.amountRange.min &&
        debt.total_amount <= filters.amountRange.max

      // Status filter
      let matchesStatus = true
      if (filters.status !== 'all') {
        const daysDiff = differenceInDays(new Date(), debtDate)
        switch (filters.status) {
          case 'recent':
            matchesStatus = daysDiff <= 7
            break
          case 'overdue':
            matchesStatus = daysDiff > 30
            break
          case 'high-amount':
            matchesStatus = debt.total_amount > 1000
            break
        }
      }

      // Customer filter
      const matchesCustomer = !filters.customer ||
        customerName.includes(filters.customer.toLowerCase())

      return matchesSearch && matchesDateRange && matchesAmount && matchesStatus && matchesCustomer
    })

    // Sorting
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number

      switch (sortBy) {
        case 'customer_name':
          aValue = `${a.customer_name} ${a.customer_family_name}`.toLowerCase()
          bValue = `${b.customer_name} ${b.customer_family_name}`.toLowerCase()
          break
        case 'total_amount':
          aValue = a.total_amount
          bValue = b.total_amount
          break
        case 'debt_date':
          aValue = new Date(a.debt_date).getTime()
          bValue = new Date(b.debt_date).getTime()
          break
        case 'product_name':
          aValue = a.product_name.toLowerCase()
          bValue = b.product_name.toLowerCase()
          break
        case 'created_at':
          aValue = new Date(a.created_at).getTime()
          bValue = new Date(b.created_at).getTime()
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [debts, searchTerm, filters, sortBy, sortDirection])

  // Customer summary for customer view mode
  const customerSummaries = useMemo(() => {
    const summaries: CustomerSummary[] = []
    const customerMap = new Map<string, CustomerDebt[]>()

    filteredAndSortedDebts.forEach(debt => {
      const customerKey = `${debt.customer_name} ${debt.customer_family_name}`
      if (!customerMap.has(customerKey)) {
        customerMap.set(customerKey, [])
      }
      customerMap.get(customerKey)!.push(debt)
    })

    customerMap.forEach((debts, customerName) => {
      const totalAmount = debts.reduce((sum, debt) => sum + debt.total_amount, 0)
      const sortedDebts = debts.sort((a, b) => new Date(a.debt_date).getTime() - new Date(b.debt_date).getTime())

      // Get balance information
      const balance = customerBalances.get(customerName)
      const totalPayments = balance?.total_payments || 0
      const remainingBalance = balance?.remaining_balance || totalAmount

      summaries.push({
        customerName,
        totalDebts: debts.length,
        totalAmount,
        totalPayments,
        remainingBalance,
        oldestDebt: sortedDebts[0]?.debt_date || '',
        recentDebt: sortedDebts[sortedDebts.length - 1]?.debt_date || '',
        debts: sortedDebts
      })
    })

    return summaries.sort((a, b) => b.totalAmount - a.totalAmount)
  }, [filteredAndSortedDebts, customerBalances])

  // Bulk operations handlers
  const handleSelectAll = () => {
    if (selectedDebts.size === filteredAndSortedDebts.length) {
      setSelectedDebts(new Set())
    } else {
      setSelectedDebts(new Set(filteredAndSortedDebts.map(d => d.id)))
    }
  }

  const handleSelectDebt = (debtId: string) => {
    const newSelected = new Set(selectedDebts)
    if (newSelected.has(debtId)) {
      newSelected.delete(debtId)
    } else {
      newSelected.add(debtId)
    }
    setSelectedDebts(newSelected)
  }

  const handleBulkDelete = async () => {
    if (selectedDebts.size === 0) return

    const confirmMessage = `Are you sure you want to delete ${selectedDebts.size} debt record(s)?`
    if (!confirm(confirmMessage)) return

    try {
      const deletePromises = Array.from(selectedDebts).map(id =>
        fetch(`/api/debts/${id}`, { method: 'DELETE' })
      )

      await Promise.all(deletePromises)
      setSelectedDebts(new Set())
      fetchDebts()
      onStatsUpdate()
    } catch (error) {
      console.error('Error deleting debt records:', error)
    }
  }

  // Search handlers
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    setFilters(prev => ({ ...prev, search: value }))
    generateSearchSuggestions(value)
    setShowSuggestions(value.length >= 2)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setSearchTerm(suggestion)
    setFilters(prev => ({ ...prev, search: suggestion }))
    setShowSuggestions(false)
  }



  // Export handlers
  const handleExportCSV = () => {
    exportDebtsToCSV(filteredAndSortedDebts, `customer_debts_${new Date().toISOString().split('T')[0]}`)
  }

  const handleExportJSON = () => {
    const jsonContent = JSON.stringify(filteredAndSortedDebts, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `customer_debts_${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleExportAnalytics = () => {
    const report = {
      summary: {
        totalDebts: filteredAndSortedDebts.length,
        totalAmount: filteredAndSortedDebts.reduce((sum, debt) => sum + debt.total_amount, 0),
        uniqueCustomers: new Set(filteredAndSortedDebts.map(d => `${d.customer_name} ${d.customer_family_name}`)).size,
        averageDebtAmount: filteredAndSortedDebts.length > 0
          ? filteredAndSortedDebts.reduce((sum, debt) => sum + debt.total_amount, 0) / filteredAndSortedDebts.length
          : 0
      },
      customerBreakdown: customerSummaries,
      generatedAt: new Date().toISOString()
    }

    const jsonContent = JSON.stringify(report, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `debt_analytics_${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }



  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex justify-between items-center">
          <div
            className="w-64 h-10 rounded-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
              backgroundImage: resolvedTheme === 'dark'
                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
              backgroundSize: '200% 100%',
              animation: 'shimmer 2s infinite'
            }}
          />
          <div
            className="w-48 h-10 rounded-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
              backgroundImage: resolvedTheme === 'dark'
                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
              backgroundSize: '200% 100%',
              animation: 'shimmer 2s infinite'
            }}
          />
        </div>

        {/* Debts List Skeleton */}
        <LoadingSkeleton type="debts" count={4} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Advanced Controls */}
      <div className="space-y-4">
        {/* Top Row - Search and Actions */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          {/* Search Section */}
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                }}
              />
              <input
                type="text"
                placeholder="Search customers, products, or amounts..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                onFocus={() => setShowSuggestions(searchTerm.length >= 2)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                className="w-full pl-10 pr-4 py-2.5 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-sm"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              />

              {/* Search Suggestions */}
              {showSuggestions && searchSuggestions.length > 0 && (
                <div
                  className="absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg border z-50 max-h-48 overflow-y-auto"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'
                  }}
                >
                  {searchSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <Users className="h-4 w-4 inline mr-2 text-green-600" />
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Filters */}
            <div className="flex gap-2">
              <button
                onClick={() => setFilters(prev => ({ ...prev, status: prev.status === 'recent' ? 'all' : 'recent' }))}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  filters.status === 'recent'
                    ? 'bg-blue-100 text-blue-700 border-blue-300'
                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                } border`}
              >
                <Clock className="h-4 w-4 inline mr-1" />
                Recent
              </button>
              <button
                onClick={() => setFilters(prev => ({ ...prev, status: prev.status === 'overdue' ? 'all' : 'overdue' }))}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  filters.status === 'overdue'
                    ? 'bg-red-100 text-red-700 border-red-300'
                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                } border`}
              >
                <AlertTriangle className="h-4 w-4 inline mr-1" />
                Overdue
              </button>
              <button
                onClick={() => setFilters(prev => ({ ...prev, status: prev.status === 'high-amount' ? 'all' : 'high-amount' }))}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  filters.status === 'high-amount'
                    ? 'bg-orange-100 text-orange-700 border-orange-300'
                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                } border`}
              >
                <DollarSign className="h-4 w-4 inline mr-1" />
                High Amount
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm ${
                showFilters
                  ? 'bg-green-100 text-green-700 border-green-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              } border`}
            >
              <Filter className="h-4 w-4 inline mr-2" />
              Filters
            </button>

            {/* Export Dropdown */}
            {filteredAndSortedDebts.length > 0 && (
              <div className="relative group">
                <button
                  className="px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 border"
                >
                  <Download className="h-4 w-4 inline mr-2" />
                  Export
                  <ChevronDown className="h-4 w-4 inline ml-1" />
                </button>

                <div className="absolute right-0 top-full mt-1 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div
                    className="rounded-lg shadow-lg border py-1"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e5e7eb'
                    }}
                  >
                    <button
                      onClick={handleExportCSV}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <Download className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Export as CSV</span>
                    </button>
                    <button
                      onClick={handleExportJSON}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <Download className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Export as JSON</span>
                    </button>
                    <div
                      className="my-1 h-px"
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
                      }}
                    />
                    <button
                      onClick={handleExportAnalytics}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <BarChart3 className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium">Analytics Report</span>
                    </button>
                  </div>
                </div>
              </div>
            )}

            <button
              onClick={() => setIsModalOpen(true)}
              className="flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Debt Record
            </button>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div
            className="p-6 rounded-xl border shadow-sm animate-slide-down"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e2e8f0'
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Date Range Filter */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Date Range
                </label>
                <div className="flex gap-2">
                  <input
                    type="date"
                    value={filters.dateRange.from}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, from: e.target.value }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                  <input
                    type="date"
                    value={filters.dateRange.to}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, to: e.target.value }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                </div>
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Amount Range (₱)
                </label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.amountRange.min}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      amountRange: { ...prev.amountRange, min: Number(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.amountRange.max}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      amountRange: { ...prev.amountRange, max: Number(e.target.value) || 50000 }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                </div>
              </div>

              {/* Customer Filter */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Customer
                </label>
                <input
                  type="text"
                  placeholder="Filter by customer name"
                  value={filters.customer}
                  onChange={(e) => setFilters(prev => ({ ...prev, customer: e.target.value }))}
                  className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                />
              </div>

              {/* Reset Filters */}
              <div className="flex items-end">
                <button
                  onClick={() => setFilters({
                    search: '',
                    dateRange: { from: '', to: '' },
                    amountRange: { min: 0, max: 50000 },
                    status: 'all',
                    customer: ''
                  })}
                  className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium text-sm"
                >
                  <RefreshCw className="h-4 w-4 inline mr-2" />
                  Reset Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Toolbar - Sort, View Mode, Bulk Actions */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          {/* Left Side - Results Info & Bulk Actions */}
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium" style={{
              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
            }}>
              {viewMode === 'customer'
                ? `${customerSummaries.length} customer${customerSummaries.length !== 1 ? 's' : ''} with ${filteredAndSortedDebts.length} debt${filteredAndSortedDebts.length !== 1 ? 's' : ''}`
                : `${filteredAndSortedDebts.length} debt record${filteredAndSortedDebts.length !== 1 ? 's' : ''} found`
              }
            </span>

            {/* Bulk Selection */}
            {filteredAndSortedDebts.length > 0 && viewMode !== 'customer' && (
              <div className="flex items-center gap-2">
                <button
                  onClick={handleSelectAll}
                  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title={selectedDebts.size === filteredAndSortedDebts.length ? 'Deselect All' : 'Select All'}
                >
                  {selectedDebts.size === filteredAndSortedDebts.length ? (
                    <CheckSquare className="h-4 w-4 text-green-600" />
                  ) : selectedDebts.size > 0 ? (
                    <Minus className="h-4 w-4 text-gray-600" />
                  ) : (
                    <Square className="h-4 w-4 text-gray-600" />
                  )}
                </button>

                {selectedDebts.size > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-green-600 font-medium">
                      {selectedDebts.size} selected
                    </span>
                    <button
                      onClick={handleBulkDelete}
                      className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium"
                    >
                      <Trash2 className="h-3 w-3 inline mr-1" />
                      Delete
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Side - Sort & View Controls */}
          <div className="flex items-center gap-3">
            {/* Sort Controls */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium" style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}>
                Sort by:
              </span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as DebtSortOption)}
                className="px-3 py-1.5 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <option value="debt_date">Debt Date</option>
                <option value="customer_name">Customer Name</option>
                <option value="total_amount">Amount</option>
                <option value="product_name">Product</option>
                <option value="created_at">Date Added</option>
              </select>
              <button
                onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title={`Sort ${sortDirection === 'asc' ? 'Descending' : 'Ascending'}`}
              >
                {sortDirection === 'asc' ? (
                  <SortAsc className="h-4 w-4" style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }} />
                ) : (
                  <SortDesc className="h-4 w-4" style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }} />
                )}
              </button>
            </div>

            {/* View Mode Toggle */}
            <div className="flex rounded-lg border" style={{
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'
            }}>
              <button
                onClick={() => setViewMode('customer')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'customer'
                    ? 'bg-green-100 text-green-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                title="Customer View"
              >
                <Users className="h-4 w-4 inline mr-1" />
                Customers
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'list'
                    ? 'bg-green-100 text-green-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                title="List View"
              >
                <List className="h-4 w-4 inline mr-1" />
                List
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-green-100 text-green-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                title="Grid View"
              >
                <Grid className="h-4 w-4 inline mr-1" />
                Grid
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Debts Display */}
      <div className="space-y-6">
        {viewMode === 'customer' ? (
          // Customer-grouped view
          customerSummaries.length > 0 ? (
            customerSummaries.map((customer) => (
              <div
                key={customer.customerName}
                className="rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
                }}
              >
                {/* Customer Header */}
                <div
                  className="px-6 py-4 border-b"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',
                    borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e2e8f0'
                  }}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-4">
                      <CustomerAvatar
                        key={`${customer.customerName}-${avatarRefreshKey}`}
                        customerName={customer.customerName?.split(' ')[0] || ''}
                        customerFamilyName={customer.customerName?.split(' ').slice(1).join(' ') || ''}
                        profilePictureUrl={customerProfiles.get(customer.customerName)?.profile_picture_url}
                        size="lg"
                        showEditButton={true}
                        onEditClick={() => handleEditProfile(
                          customer.customerName?.split(' ')[0] || '',
                          customer.customerName?.split(' ').slice(1).join(' ') || ''
                        )}
                      />
                      <div>
                        <h3
                          className="text-lg font-semibold"
                          style={{
                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                          }}
                        >
                          {customer.customerName}
                        </h3>
                        <p
                          className="text-sm"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          {customer.totalDebts} debt record{customer.totalDebts !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="space-y-2">
                        <div>
                          <p className="text-lg font-semibold text-gray-600">
                            ₱{customer.totalAmount.toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500">Total Debt</p>
                        </div>

                        {customer.totalPayments > 0 && (
                          <div>
                            <p className="text-lg font-semibold text-green-600">
                              -₱{customer.totalPayments.toFixed(2)}
                            </p>
                            <p className="text-xs text-gray-500">Total Paid</p>
                          </div>
                        )}

                        <div className="border-t pt-2">
                          <p className={`text-2xl font-bold ${
                            customer.remainingBalance <= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            ₱{customer.remainingBalance.toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {customer.remainingBalance <= 0 ? 'Fully Paid' : 'Balance Due'}
                          </p>
                        </div>

                        {customer.remainingBalance > 0 && (
                          <button
                            onClick={() => {
                              const nameParts = customer.customerName.split(' ')
                              const firstName = nameParts[0] || ''
                              const lastName = nameParts.slice(1).join(' ') || ''
                              handleMakePayment(firstName, lastName)
                            }}
                            className="w-full mt-2 px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg text-sm font-medium"
                          >
                            <DollarSign className="h-4 w-4 inline mr-1" />
                            Record Payment
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Customer Stats */}
                  <div className="mt-4 grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      <span
                        className="text-sm"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        Oldest: {format(new Date(customer.oldestDebt), 'MMM dd, yyyy')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-green-500" />
                      <span
                        className="text-sm"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        Recent: {format(new Date(customer.recentDebt), 'MMM dd, yyyy')}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Debt Records */}
                <div className="divide-y" style={{
                  borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
                }}>
                  {customer.debts.map((debt) => (
                    <div key={debt.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="p-1.5 rounded-lg bg-blue-100">
                              <Package className="h-4 w-4 text-blue-600" />
                            </div>
                            <h4
                              className="font-medium"
                              style={{
                                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                              }}
                            >
                              {debt.product_name}
                            </h4>
                          </div>

                          <div className="ml-8 space-y-1">
                            <div className="flex items-center gap-4 text-sm" style={{
                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                            }}>
                              <span className="flex items-center gap-1">
                                <Calculator className="h-3 w-3" />
                                Qty: {debt.quantity}
                              </span>
                              <span className="flex items-center gap-1">
                                <DollarSign className="h-3 w-3" />
                                Unit: ₱{debt.product_price.toFixed(2)}
                              </span>
                            </div>
                            <div className="flex items-center gap-1 text-sm" style={{
                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                            }}>
                              <Calendar className="h-3 w-3" />
                              <span>{format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>
                              <span className="mx-2">•</span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                differenceInDays(new Date(), new Date(debt.debt_date)) > 30
                                  ? 'bg-red-100 text-red-700'
                                  : differenceInDays(new Date(), new Date(debt.debt_date)) > 7
                                    ? 'bg-orange-100 text-orange-700'
                                    : 'bg-green-100 text-green-700'
                              }`}>
                                {differenceInDays(new Date(), new Date(debt.debt_date))} days ago
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 ml-4">
                          <div className="text-right">
                            <p className="text-lg font-bold text-red-600">
                              ₱{debt.total_amount.toFixed(2)}
                            </p>
                          </div>

                          <div className="flex gap-1">
                            <button
                              onClick={() => handleEdit(debt)}
                              className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-all duration-200 hover:scale-105"
                              title="Edit Debt Record"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(debt.id)}
                              className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-all duration-200 hover:scale-105"
                              title="Delete Debt Record"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div
              className="text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
              }}
            >
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',
                  border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'
                }}
              >
                <Users
                  className="h-10 w-10 transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'
                  }}
                />
              </div>
              <h3
                className="text-xl font-semibold mb-3 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                No customers with debt records found
              </h3>
              <p
                className="text-sm mb-6 max-w-md mx-auto transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'
                }}
              >
                {searchTerm || Object.values(filters).some(f => f && f !== 'all')
                  ? 'Try adjusting your search terms or filter criteria to find what you\'re looking for'
                  : 'Get started by adding your first debt record to track customer purchases'}
              </p>
              {!searchTerm && !Object.values(filters).some(f => f && f !== 'all') && (
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Add First Debt Record
                </button>
              )}
            </div>
          )
        ) : (
          // List and Grid views
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
          }>
            {filteredAndSortedDebts.map((debt) => (
              <div
                key={debt.id}
                className={`relative group rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl ${
                  selectedDebts.has(debt.id)
                    ? 'ring-2 ring-green-500 ring-offset-2'
                    : 'hover:scale-[1.02]'
                } ${viewMode === 'list' ? 'flex items-center' : ''}`}
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
                }}
              >
                {/* Selection Checkbox */}
                <div className="absolute top-3 left-3 z-10">
                  <button
                    onClick={() => handleSelectDebt(debt.id)}
                    className={`p-1 rounded-md transition-all duration-200 ${
                      selectedDebts.has(debt.id)
                        ? 'bg-green-500 text-white'
                        : 'bg-white/80 text-gray-600 hover:bg-white'
                    } shadow-sm`}
                  >
                    {selectedDebts.has(debt.id) ? (
                      <CheckSquare className="h-4 w-4" />
                    ) : (
                      <Square className="h-4 w-4" />
                    )}
                  </button>
                </div>

                {/* Debt Content */}
                <div className={`${viewMode === 'grid' ? 'p-6' : 'flex-1 p-4'}`}>
                  <div className={`${viewMode === 'list' ? 'flex items-center justify-between' : ''}`}>
                    <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
                      {/* Customer Info */}
                      <div className="flex items-center gap-3 mb-3">
                        <CustomerAvatar
                          key={`${debt.customer_name}-${debt.customer_family_name}-${avatarRefreshKey}`}
                          customerName={debt.customer_name}
                          customerFamilyName={debt.customer_family_name}
                          profilePictureUrl={customerProfiles.get(`${debt.customer_name} ${debt.customer_family_name}`)?.profile_picture_url}
                          size="md"
                          showEditButton={false}
                        />
                        <div>
                          <h3
                            className="font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                            }}
                          >
                            {debt.customer_name} {debt.customer_family_name}
                          </h3>
                          <p
                            className="text-sm"
                            style={{
                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                            }}
                          >
                            {debt.product_name}
                          </p>
                        </div>
                      </div>

                      {/* Debt Details */}
                      <div className={`${viewMode === 'list' ? 'flex items-center gap-6' : 'space-y-2'}`}>
                        <div className="flex items-center gap-4 text-sm" style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}>
                          <span className="flex items-center gap-1">
                            <Calculator className="h-3 w-3" />
                            Qty: {debt.quantity}
                          </span>
                          <span className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            ₱{debt.product_price.toFixed(2)}
                          </span>
                        </div>

                        <div className="flex items-center gap-1 text-sm" style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}>
                          <Calendar className="h-3 w-3" />
                          <span>{format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-2xl font-bold text-red-600">
                            ₱{debt.total_amount.toFixed(2)}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            differenceInDays(new Date(), new Date(debt.debt_date)) > 30
                              ? 'bg-red-100 text-red-700'
                              : differenceInDays(new Date(), new Date(debt.debt_date)) > 7
                                ? 'bg-orange-100 text-orange-700'
                                : 'bg-green-100 text-green-700'
                          }`}>
                            {differenceInDays(new Date(), new Date(debt.debt_date))} days ago
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className={`${viewMode === 'list' ? 'flex gap-2 ml-4' : 'flex gap-2 mt-4'}`}>
                      <button
                        onClick={() => handleEdit(debt)}
                        className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-300 hover:scale-105 font-medium"
                        title="Edit Debt Record"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        {viewMode === 'grid' ? 'Edit' : ''}
                      </button>
                      <button
                        onClick={() => handleDelete(debt.id)}
                        className="flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-300 hover:scale-105 font-medium"
                        title="Delete Debt Record"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        {viewMode === 'grid' ? 'Delete' : ''}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Empty State for List/Grid Views */}
        {(viewMode === 'list' || viewMode === 'grid') && filteredAndSortedDebts.length === 0 && !loading && (
          <div
            className="text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',
              borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
            }}
          >
            <div
              className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',
                border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'
              }}
            >
              <FileText
                className="h-10 w-10 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'
                }}
              />
            </div>
            <h3
              className="text-xl font-semibold mb-3 transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              {searchTerm || Object.values(filters).some(f => f && f !== 'all') ? 'No debt records found' : 'No debt records yet'}
            </h3>
            <p
              className="text-sm mb-6 max-w-md mx-auto transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'
              }}
            >
              {searchTerm || Object.values(filters).some(f => f && f !== 'all')
                ? 'Try adjusting your search criteria or filters to find specific debt records'
                : 'Start tracking customer debts by adding your first debt record to manage store credit'}
            </p>
            {!searchTerm && !Object.values(filters).some(f => f && f !== 'all') && (
              <button
                onClick={() => setIsModalOpen(true)}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
              >
                <Plus className="h-5 w-5 mr-2" />
                Add First Debt Record
              </button>
            )}
          </div>
        )}
      </div>

      {/* Debt Modal */}
      <DebtModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        debt={editingDebt}
      />

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handlePaymentModalClose}
        customerName={selectedCustomerForPayment?.name || ''}
        customerFamilyName={selectedCustomerForPayment?.familyName || ''}
        currentBalance={selectedCustomerForPayment?.balance || 0}
        payment={editingPayment}
        profilePictureUrl={
          selectedCustomerForPayment
            ? customerProfiles.get(`${selectedCustomerForPayment.name} ${selectedCustomerForPayment.familyName}`)?.profile_picture_url || undefined
            : undefined
        }
      />

      {/* Customer Profile Modal */}
      <CustomerProfileModal
        isOpen={isProfileModalOpen}
        onClose={handleProfileModalClose}
        customer={selectedCustomerForProfile?.customer || null}
        customerName={selectedCustomerForProfile?.name || ''}
        customerFamilyName={selectedCustomerForProfile?.familyName || ''}
        onProfileUpdated={handleProfileUpdated}
      />
    </div>
  )
}
