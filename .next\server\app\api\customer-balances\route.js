(()=>{var e={};e.id=344,e.ids=[344],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,t,r)=>{"use strict";r.d(t,{$W:()=>i});var s=r(72289),n=r(35282);let a=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),o=function(){try{return a.parse(process.env)}catch(e){if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===o.NODE_ENV,isProduction:"production"===o.NODE_ENV,isTest:"test"===o.NODE_ENV,database:{url:o.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:o.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:o.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:o.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:o.CLOUDINARY_API_KEY,apiSecret:o.CLOUDINARY_API_SECRET},ai:{geminiApiKey:o.GEMINI_API_KEY},auth:{secret:o.NEXTAUTH_SECRET,url:o.NEXTAUTH_URL},debug:o.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:c,NEXT_PUBLIC_SUPABASE_ANON_KEY:l,SUPABASE_SERVICE_ROLE_KEY:p,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:R,NEXTAUTH_URL:m,DEBUG:A,GEMINI_API_KEY:N}=o},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},53171:(e,t,r)=>{"use strict";r.d(t,{FB:()=>u,Rv:()=>l,Y8:()=>_,ie:()=>p,r6:()=>o,sv:()=>c,u3:()=>d,yj:()=>i});var s=r(32190);let n={OK:200,BAD_REQUEST:400,CONFLICT:409,INTERNAL_SERVER_ERROR:500};class a extends Error{constructor(e,t=n.INTERNAL_SERVER_ERROR,r){super(e),this.message=e,this.statusCode=t,this.code=r,this.name="ApiError"}}function o(e,t,r=n.OK){let a={success:!0,data:e};return t&&(a.message=t),s.NextResponse.json(a,{status:r})}function i(e,t=n.INTERNAL_SERVER_ERROR,r){return s.NextResponse.json({success:!1,error:e,code:r},{status:t})}function u(e){return async(...t)=>{try{return await e(...t)}catch(e){if(console.error("API Error:",e),e instanceof a)return i(e.message,e.statusCode,e.code);if(e instanceof Error)return i(e.message);return i("An unexpected error occurred")}}}async function c(e,t){try{let r=await e.json();return t(r)}catch{throw new a("Invalid request body",n.BAD_REQUEST)}}function l(e,t){let r=t.filter(t=>void 0===e[t]||null===e[t]||""===e[t]);if(r.length>0)throw new a(`Missing required fields: ${r.join(", ")}`,n.BAD_REQUEST)}function p(e){if(console.error("Database error:",e),"23505"===e.code)throw new a("Resource already exists",n.CONFLICT);if("23503"===e.code)throw new a("Referenced resource not found",n.BAD_REQUEST);if("23502"===e.code)throw new a("Required field is missing",n.BAD_REQUEST);throw new a("Database operation failed",n.INTERNAL_SERVER_ERROR)}function d(e,t={}){let{maxLimit:r=100}=t,s=Math.max(1,parseInt(e.get("page")||"1",10)),n=Math.min(r,Math.max(1,parseInt(e.get("limit")||"10",10)));return{page:s,limit:n,offset:(s-1)*n}}let E={"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"};function _(){return new s.NextResponse(null,{status:200,headers:E})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(66437),n=r(34960);let a=(0,s.UU)(n.$W.database.url,n.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95341:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>E,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>R});var s={};r.r(s),r.d(s,{GET:()=>p,OPTIONS:()=>l,POST:()=>d});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(53171),c=r(56621);async function l(){return(0,u.Y8)()}let p=(0,u.FB)(async e=>{let{searchParams:t}=new URL(e.url),{page:r,limit:s,offset:n}=(0,u.u3)(t),a=t.get("search"),o=t.get("hasBalance"),i=c.N.from("customer_balances").select("*",{count:"exact"}).order("remaining_balance",{ascending:!1}).range(n,n+s-1);a&&(i=i.or(`customer_name.ilike.%${a}%,customer_family_name.ilike.%${a}%`)),"true"===o&&(i=i.gt("remaining_balance",0));let{data:l,error:p,count:d}=await i;return p?(0,u.ie)(p):(0,u.r6)({balances:l,pagination:{page:r,limit:s,total:d||0,totalPages:Math.ceil((d||0)/s)}})});async function d(e){try{let{customer_name:t,customer_family_name:r}=await e.json();if(!t||!r)return i.NextResponse.json({error:"Customer name and family name are required"},{status:400});let{data:s,error:n}=await c.N.from("customer_balances").select("*").eq("customer_name",t).eq("customer_family_name",r).single();if(n){if("PGRST116"===n.code)return i.NextResponse.json({balance:{customer_name:t,customer_family_name:r,total_debt:0,total_payments:0,remaining_balance:0,last_debt_date:null,last_payment_date:null}});return i.NextResponse.json({error:n.message},{status:500})}return i.NextResponse.json({balance:s})}catch(e){return console.error("Error fetching customer balance:",e),i.NextResponse.json({error:"Failed to fetch customer balance"},{status:500})}}let E=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/customer-balances/route",pathname:"/api/customer-balances",filename:"route",bundlePath:"app/api/customer-balances/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customer-balances\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:R,serverHooks:m}=E;function A(){return(0,o.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:R})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,289,437],()=>r(95341));module.exports=s})();