(()=>{var e={};e.id=700,e.ids=[700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31605:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>ec,routeModule:()=>ei,serverHooks:()=>el,workAsyncStorage:()=>ea,workUnitAsyncStorage:()=>er});var s,o,i,a,r,l,c,u,d,h,f,p,E={};n.r(E),n.d(E,{GET:()=>eo,POST:()=>es});var g=n(96559),m=n(48088),C=n(37719);!function(e){e.STRING="string",e.NUMBER="number",e.INTEGER="integer",e.BOOLEAN="boolean",e.ARRAY="array",e.OBJECT="object"}(s||(s={})),function(e){e.LANGUAGE_UNSPECIFIED="language_unspecified",e.PYTHON="python"}(o||(o={})),function(e){e.OUTCOME_UNSPECIFIED="outcome_unspecified",e.OUTCOME_OK="outcome_ok",e.OUTCOME_FAILED="outcome_failed",e.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"}(i||(i={}));let _=["user","model","function","system"];!function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"}(a||(a={})),function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"}(r||(r={})),function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"}(l||(l={})),function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"}(c||(c={})),function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.LANGUAGE="LANGUAGE",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.SPII="SPII",e.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",e.OTHER="OTHER"}(u||(u={})),function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"}(d||(d={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"}(h||(h={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"}(f||(f={}));class O extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class y extends O{constructor(e,t){super(e),this.response=t}}class I extends O{constructor(e,t,n,s){super(e),this.status=t,this.statusText=n,this.errorDetails=s}}class A extends O{}class N extends O{}!function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"}(p||(p={}));class T{constructor(e,t,n,s,o){this.model=e,this.task=t,this.apiKey=n,this.stream=s,this.requestOptions=o}toString(){var e,t;let n=(null==(e=this.requestOptions)?void 0:e.apiVersion)||"v1beta",s=(null==(t=this.requestOptions)?void 0:t.baseUrl)||"https://generativelanguage.googleapis.com",o=`${s}/${n}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}}async function R(e){var t;let n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",function(e){let t=[];return(null==e?void 0:e.apiClient)&&t.push(e.apiClient),t.push("genai-js/0.24.1"),t.join(" ")}(e.requestOptions)),n.append("x-goog-api-key",e.apiKey);let s=null==(t=e.requestOptions)?void 0:t.customHeaders;if(s){if(!(s instanceof Headers))try{s=new Headers(s)}catch(e){throw new A(`unable to convert customHeaders value ${JSON.stringify(s)} to Headers: ${e.message}`)}for(let[e,t]of s.entries()){if("x-goog-api-key"===e)throw new A(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new A(`Header name ${e} can only be set using the apiClient field`);n.append(e,t)}}return n}async function v(e,t,n,s,o,i){let a=new T(e,t,n,s,i);return{url:a.toString(),fetchOptions:Object.assign(Object.assign({},function(e){let t={};if((null==e?void 0:e.signal)!==void 0||(null==e?void 0:e.timeout)>=0){let n=new AbortController;(null==e?void 0:e.timeout)>=0&&setTimeout(()=>n.abort(),e.timeout),(null==e?void 0:e.signal)&&e.signal.addEventListener("abort",()=>{n.abort()}),t.signal=n.signal}return t}(i)),{method:"POST",headers:await R(a),body:o})}}async function S(e,t,n,s,o,i={},a=fetch){let{url:r,fetchOptions:l}=await v(e,t,n,s,o,i);return w(r,l,a)}async function w(e,t,n=fetch){let s;try{s=await n(e,t)}catch(n){var o=n,i=e;let t=o;throw"AbortError"===t.name?(t=new N(`Request aborted when fetching ${i.toString()}: ${o.message}`)).stack=o.stack:o instanceof I||o instanceof A||((t=new O(`Error fetching from ${i.toString()}: ${o.message}`)).stack=o.stack),t}return s.ok||await b(s,e),s}async function b(e,t){let n,s="";try{let t=await e.json();s=t.error.message,t.error.details&&(s+=` ${JSON.stringify(t.error.details)}`,n=t.error.details)}catch(e){}throw new I(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${s}`,e.status,e.statusText,n)}function M(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),U(e.candidates[0]))throw new y(`${D(e)}`,e);return function(e){var t,n,s,o;let i=[];if(null==(n=null==(t=e.candidates)?void 0:t[0].content)?void 0:n.parts)for(let t of null==(o=null==(s=e.candidates)?void 0:s[0].content)?void 0:o.parts)t.text&&i.push(t.text),t.executableCode&&i.push("\n```"+t.executableCode.language+"\n"+t.executableCode.code+"\n```\n"),t.codeExecutionResult&&i.push("\n```\n"+t.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}(e)}if(e.promptFeedback)throw new y(`Text not available. ${D(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),U(e.candidates[0]))throw new y(`${D(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),P(e)[0]}if(e.promptFeedback)throw new y(`Function call not available. ${D(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),U(e.candidates[0]))throw new y(`${D(e)}`,e);return P(e)}if(e.promptFeedback)throw new y(`Function call not available. ${D(e)}`,e)},e}function P(e){var t,n,s,o;let i=[];if(null==(n=null==(t=e.candidates)?void 0:t[0].content)?void 0:n.parts)for(let t of null==(o=null==(s=e.candidates)?void 0:s[0].content)?void 0:o.parts)t.functionCall&&i.push(t.functionCall);return i.length>0?i:void 0}let x=[u.RECITATION,u.SAFETY,u.LANGUAGE];function U(e){return!!e.finishReason&&x.includes(e.finishReason)}function D(e){var t,n,s;let o="";if((!e.candidates||0===e.candidates.length)&&e.promptFeedback)o+="Response was blocked",(null==(t=e.promptFeedback)?void 0:t.blockReason)&&(o+=` due to ${e.promptFeedback.blockReason}`),(null==(n=e.promptFeedback)?void 0:n.blockReasonMessage)&&(o+=`: ${e.promptFeedback.blockReasonMessage}`);else if(null==(s=e.candidates)?void 0:s[0]){let t=e.candidates[0];U(t)&&(o+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(o+=`: ${t.finishMessage}`))}return o}function L(e){return this instanceof L?(this.v=e,this):new L(e)}"function"==typeof SuppressedError&&SuppressedError;let j=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;async function Y(e){let t=[],n=e.getReader();for(;;){let{done:e,value:s}=await n.read();if(e)return M(function(e){let t=e[e.length-1],n={promptFeedback:null==t?void 0:t.promptFeedback};for(let t of e){if(t.candidates){let e=0;for(let s of t.candidates)if(n.candidates||(n.candidates=[]),n.candidates[e]||(n.candidates[e]={index:e}),n.candidates[e].citationMetadata=s.citationMetadata,n.candidates[e].groundingMetadata=s.groundingMetadata,n.candidates[e].finishReason=s.finishReason,n.candidates[e].finishMessage=s.finishMessage,n.candidates[e].safetyRatings=s.safetyRatings,s.content&&s.content.parts){n.candidates[e].content||(n.candidates[e].content={role:s.content.role||"user",parts:[]});let t={};for(let o of s.content.parts)o.text&&(t.text=o.text),o.functionCall&&(t.functionCall=o.functionCall),o.executableCode&&(t.executableCode=o.executableCode),o.codeExecutionResult&&(t.codeExecutionResult=o.codeExecutionResult),0===Object.keys(t).length&&(t.text=""),n.candidates[e].content.parts.push(t)}e++}t.usageMetadata&&(n.usageMetadata=t.usageMetadata)}return n}(t));t.push(s)}}async function H(e,t,n,s){let[o,i]=(function(e){let t=e.getReader();return new ReadableStream({start(e){let n="";return function s(){return t.read().then(({value:t,done:o})=>{let i;if(o)return n.trim()?void e.error(new O("Failed to parse stream")):void e.close();let a=(n+=t).match(j);for(;a;){try{i=JSON.parse(a[1])}catch(t){e.error(new O(`Error parsing JSON response: "${a[1]}"`));return}e.enqueue(i),a=(n=n.substring(a[0].length)).match(j)}return s()}).catch(e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new N("Request aborted when reading from the stream"):new O("Error reading from the stream")})}()}})})((await S(t,p.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),s)).body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))).tee();return{stream:function(e){return function(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var s,o=n.apply(e,t||[]),i=[];return s={},a("next"),a("throw"),a("return"),s[Symbol.asyncIterator]=function(){return this},s;function a(e){o[e]&&(s[e]=function(t){return new Promise(function(n,s){i.push([e,t,n,s])>1||r(e,t)})})}function r(e,t){try{var n;(n=o[e](t)).value instanceof L?Promise.resolve(n.value.v).then(l,c):u(i[0][2],n)}catch(e){u(i[0][3],e)}}function l(e){r("next",e)}function c(e){r("throw",e)}function u(e,t){e(t),i.shift(),i.length&&r(i[0][0],i[0][1])}}(this,arguments,function*(){let t=e.getReader();for(;;){let{value:e,done:n}=yield L(t.read());if(n)break;yield yield L(M(e))}})}(o),response:Y(i)}}async function G(e,t,n,s){let o=await S(t,p.GENERATE_CONTENT,e,!1,JSON.stringify(n),s);return{response:M(await o.json())}}function k(e){if(null!=e){if("string"==typeof e)return{role:"system",parts:[{text:e}]};if(e.text)return{role:"system",parts:[e]};if(e.parts)if(!e.role)return{role:"system",parts:e.parts};else return e}}function F(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(let n of e)"string"==typeof n?t.push({text:n}):t.push(n);var n=t;let s={role:"user",parts:[]},o={role:"function",parts:[]},i=!1,a=!1;for(let e of n)"functionResponse"in e?(o.parts.push(e),a=!0):(s.parts.push(e),i=!0);if(i&&a)throw new O("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!i&&!a)throw new O("No content is provided for sending chat message.");return i?s:o}function K(e){let t;return t=e.contents?e:{contents:[F(e)]},e.systemInstruction&&(t.systemInstruction=k(e.systemInstruction)),t}let B=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],$={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function q(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;let n=null==(t=e.candidates[0])?void 0:t.content;if(void 0===n||void 0===n.parts||0===n.parts.length)return!1;for(let e of n.parts)if(void 0===e||0===Object.keys(e).length||void 0!==e.text&&""===e.text)return!1;return!0}let X="SILENT_ERROR";class V{constructor(e,t,n,s={}){this.model=t,this.params=n,this._requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null==n?void 0:n.history)&&(!function(e){let t=!1;for(let n of e){let{role:e,parts:s}=n;if(!t&&"user"!==e)throw new O(`First content should be with role 'user', got ${e}`);if(!_.includes(e))throw new O(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(_)}`);if(!Array.isArray(s))throw new O("Content should have 'parts' property with an array of Parts");if(0===s.length)throw new O("Each Content should have at least one part");let o={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(let e of s)for(let t of B)t in e&&(o[t]+=1);let i=$[e];for(let t of B)if(!i.includes(t)&&o[t]>0)throw new O(`Content with role '${e}' can't contain '${t}' part`);t=!0}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var n,s,o,i,a,r;let l;await this._sendPromise;let c=F(e),u={safetySettings:null==(n=this.params)?void 0:n.safetySettings,generationConfig:null==(s=this.params)?void 0:s.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(a=this.params)?void 0:a.systemInstruction,cachedContent:null==(r=this.params)?void 0:r.cachedContent,contents:[...this._history,c]},d=Object.assign(Object.assign({},this._requestOptions),t);return this._sendPromise=this._sendPromise.then(()=>G(this._apiKey,this.model,u,d)).then(e=>{var t;if(q(e.response)){this._history.push(c);let n=Object.assign({parts:[],role:"model"},null==(t=e.response.candidates)?void 0:t[0].content);this._history.push(n)}else{let t=D(e.response);t&&console.warn(`sendMessage() was unsuccessful. ${t}. Inspect response object for details.`)}l=e}).catch(e=>{throw this._sendPromise=Promise.resolve(),e}),await this._sendPromise,l}async sendMessageStream(e,t={}){var n,s,o,i,a,r;await this._sendPromise;let l=F(e),c={safetySettings:null==(n=this.params)?void 0:n.safetySettings,generationConfig:null==(s=this.params)?void 0:s.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(a=this.params)?void 0:a.systemInstruction,cachedContent:null==(r=this.params)?void 0:r.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t),d=H(this._apiKey,this.model,c,u);return this._sendPromise=this._sendPromise.then(()=>d).catch(e=>{throw Error(X)}).then(e=>e.response).then(e=>{if(q(e)){this._history.push(l);let t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{let t=D(e);t&&console.warn(`sendMessageStream() was unsuccessful. ${t}. Inspect response object for details.`)}}).catch(e=>{e.message!==X&&console.error(e)}),d}}async function J(e,t,n,s){return(await S(t,p.COUNT_TOKENS,e,!1,JSON.stringify(n),s)).json()}async function W(e,t,n,s){return(await S(t,p.EMBED_CONTENT,e,!1,JSON.stringify(n),s)).json()}async function z(e,t,n,s){let o=n.requests.map(e=>Object.assign(Object.assign({},e),{model:t}));return(await S(t,p.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:o}),s)).json()}class Q{constructor(e,t,n={}){this.apiKey=e,this._requestOptions=n,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=k(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var n;let s=K(e),o=Object.assign(Object.assign({},this._requestOptions),t);return G(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(n=this.cachedContent)?void 0:n.name},s),o)}async generateContentStream(e,t={}){var n;let s=K(e),o=Object.assign(Object.assign({},this._requestOptions),t);return H(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(n=this.cachedContent)?void 0:n.name},s),o)}startChat(e){var t;return new V(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(t=this.cachedContent)?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){let n=function(e,t){var n;let s={model:null==t?void 0:t.model,generationConfig:null==t?void 0:t.generationConfig,safetySettings:null==t?void 0:t.safetySettings,tools:null==t?void 0:t.tools,toolConfig:null==t?void 0:t.toolConfig,systemInstruction:null==t?void 0:t.systemInstruction,cachedContent:null==(n=null==t?void 0:t.cachedContent)?void 0:n.name,contents:[]},o=null!=e.generateContentRequest;if(e.contents){if(o)throw new A("CountTokensRequest must have one of contents or generateContentRequest, not both.");s.contents=e.contents}else if(o)s=Object.assign(Object.assign({},s),e.generateContentRequest);else{let t=F(e);s.contents=[t]}return{generateContentRequest:s}}(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),s=Object.assign(Object.assign({},this._requestOptions),t);return J(this.apiKey,this.model,n,s)}async embedContent(e,t={}){let n="string"==typeof e||Array.isArray(e)?{content:F(e)}:e,s=Object.assign(Object.assign({},this._requestOptions),t);return W(this.apiKey,this.model,n,s)}async batchEmbedContents(e,t={}){let n=Object.assign(Object.assign({},this._requestOptions),t);return z(this.apiKey,this.model,e,n)}}class Z{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new O("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new Q(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,n){if(!e.name)throw new A("Cached content must contain a `name` field.");if(!e.model)throw new A("Cached content must contain a `model` field.");for(let n of["model","systemInstruction"])if((null==t?void 0:t[n])&&e[n]&&(null==t?void 0:t[n])!==e[n]){if("model"===n&&(t.model.startsWith("models/")?t.model.replace("models/",""):t.model)===(e.model.startsWith("models/")?e.model.replace("models/",""):e.model))continue;throw new A(`Different value for "${n}" specified in modelParams (${t[n]}) and cachedContent (${e[n]})`)}let s=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new Q(this.apiKey,s,n)}}var ee=n(32190),et=n(34960);let en=new Z(et.$W.ai.geminiApiKey||"AIzaSyA_rZzSvLvHQ3RksK0gyssYX0c4jNAymB4");async function es(e){try{let{message:t,context:n}=await e.json();if(!t)return ee.NextResponse.json({error:"Message is required"},{status:400});let s=en.getGenerativeModel({model:"gemini-1.5-flash"}),o=`You are an AI assistant for Revantad Store, a professional sari-sari store management system. You help store owners and administrators with:

1. **Business Analytics & Insights**: Analyze sales data, inventory trends, customer behavior
2. **Inventory Management**: Product stock levels, reorder suggestions, category optimization
3. **Customer Debt Management**: Payment tracking, debt analysis, customer relationship insights
4. **Financial Planning**: Revenue analysis, expense tracking, profit optimization
5. **Store Operations**: Daily operations, staff management, process improvements
6. **Marketing Strategies**: Customer engagement, promotional ideas, sales growth tactics

**Context about the store:**
- This is a Filipino sari-sari store (neighborhood convenience store)
- The system manages products, customer debts, inventory, and business analytics
- Users are store owners/administrators who need practical business advice
- Focus on actionable insights and practical solutions

**Your personality:**
- Professional but friendly and approachable
- Knowledgeable about Filipino business culture and sari-sari store operations
- Provide specific, actionable advice
- Use clear, easy-to-understand language
- Be encouraging and supportive

**Current context:** ${n||"General admin dashboard"}

Please provide helpful, relevant responses based on the user's question about their store management needs.`,i=`${o}

User Question: ${t}`,a=await s.generateContent(i),r=(await a.response).text();return ee.NextResponse.json({success:!0,response:r,timestamp:new Date().toISOString()})}catch(e){if(console.error("AI API Error:",e),e instanceof Error){if(e.message.includes("API_KEY"))return ee.NextResponse.json({error:"AI service configuration error. Please check API key."},{status:500});if(e.message.includes("quota")||e.message.includes("limit"))return ee.NextResponse.json({error:"AI service quota exceeded. Please try again later."},{status:429})}return ee.NextResponse.json({error:"Failed to process AI request. Please try again."},{status:500})}}async function eo(){try{let e=!!et.$W.ai.geminiApiKey||!!process.env.GEMINI_API_KEY;return ee.NextResponse.json({status:"AI service is running",configured:e,timestamp:new Date().toISOString()})}catch{return ee.NextResponse.json({error:"AI service health check failed"},{status:500})}}let ei=new g.AppRouteRouteModule({definition:{kind:m.RouteKind.APP_ROUTE,page:"/api/ai/route",pathname:"/api/ai",filename:"route",bundlePath:"app/api/ai/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts",nextConfigOutput:"",userland:E}),{workAsyncStorage:ea,workUnitAsyncStorage:er,serverHooks:el}=ei;function ec(){return(0,C.patchFetch)({workAsyncStorage:ea,workUnitAsyncStorage:er})}},34960:(e,t,n)=>{"use strict";n.d(t,{$W:()=>r});var s=n(72289),o=n(35282);let i=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),a=function(){try{return i.parse(process.env)}catch(e){if(e instanceof o.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),r={isDevelopment:"development"===a.NODE_ENV,isProduction:"production"===a.NODE_ENV,isTest:"test"===a.NODE_ENV,database:{url:a.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:a.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:a.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:a.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:a.CLOUDINARY_API_KEY,apiSecret:a.CLOUDINARY_API_SECRET},ai:{geminiApiKey:a.GEMINI_API_KEY},auth:{secret:a.NEXTAUTH_SECRET,url:a.NEXTAUTH_URL},debug:a.DEBUG},{NODE_ENV:l,NEXT_PUBLIC_SUPABASE_URL:c,NEXT_PUBLIC_SUPABASE_ANON_KEY:u,SUPABASE_SERVICE_ROLE_KEY:d,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:h,CLOUDINARY_API_KEY:f,CLOUDINARY_API_SECRET:p,NEXTAUTH_SECRET:E,NEXTAUTH_URL:g,DEBUG:m,GEMINI_API_KEY:C}=a},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[447,580,289],()=>n(31605));module.exports=s})();