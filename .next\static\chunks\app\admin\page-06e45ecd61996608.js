(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{283:(e,a,t)=>{"use strict";t.d(a,{A:()=>n,AuthProvider:()=>o});var r=t(5155),s=t(2115);let l=(0,s.createContext)(void 0);function n(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function o(e){let{children:a}=e,[t,n]=(0,s.useState)(null),[o,i]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=localStorage.getItem("revantad_user");if(e)try{let a=JSON.parse(e);n(a)}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("revantad_user")}i(!1)},[]);let d=async(e,a)=>{i(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==a)return i(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return n(e),localStorage.setItem("revantad_user",JSON.stringify(e)),i(!1),!0}}catch(e){return console.error("Login error:",e),i(!1),!1}};return(0,r.jsx)(l.Provider,{value:{user:t,login:d,logout:()=>{n(null),localStorage.removeItem("revantad_user")},isLoading:o,isAuthenticated:!!t},children:a})}},408:(e,a,t)=>{"use strict";t.d(a,{SettingsProvider:()=>o,t:()=>i});var r=t(5155),s=t(2115);let l=(0,s.createContext)(void 0),n={store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",website:"https://revantadstore.com",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"},operatingDays:["monday","tuesday","wednesday","thursday","friday","saturday"],businessRegistration:{registrationNumber:"REG-2024-001",taxId:"TAX-*********",businessType:"Retail",registrationDate:"2024-01-01"},locations:[{id:1,name:"Main Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",isMain:!0}],branding:{logo:null,primaryColor:"#22c55e",secondaryColor:"#facc15",slogan:"Your Neighborhood Store"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner",avatar:null,bio:"Experienced store owner managing Revantad Store operations.",dateOfBirth:"1990-01-01",address:"123 Barangay Street, Manila, Philippines",emergencyContact:{name:"Emergency Contact",phone:"+63 ************",relationship:"Family"},preferences:{language:"en",timezone:"Asia/Manila",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"}},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,channels:{email:"<EMAIL>",sms:"+63 ************",webhook:""},customRules:[{id:1,name:"Critical Stock Alert",condition:"stock < 5",action:"email + sms",enabled:!0}],templates:{lowStock:"Product {{productName}} is running low ({{currentStock}} remaining)",newDebt:"New debt recorded for {{customerName}}: ₱{{amount}}",paymentReceived:"Payment received from {{customerName}}: ₱{{amount}}"}},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5",currentPassword:"",newPassword:"",confirmPassword:"",apiKeys:[{id:1,name:"Main API Key",key:"sk_live_***************",created:"2024-01-01",lastUsed:"2024-01-20",permissions:["read","write"]}],loginHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",ip:"***********",device:"Chrome on Windows",location:"Manila, Philippines",success:!0}],passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!0}},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US",colorScheme:{primary:"#22c55e",secondary:"#facc15",accent:"#3b82f6",background:"#ffffff",surface:"#f8fafc"},layout:{sidebarPosition:"left",density:"comfortable",showAnimations:!0,compactMode:!1},typography:{fontFamily:"Inter",fontSize:"medium",fontWeight:"normal"}},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z",cloudStorage:{provider:"local",bucket:"",accessKey:"",secretKey:""},backupHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",size:"2.5 MB",status:"completed",type:"automatic"},{id:2,timestamp:"2024-01-19T10:30:00Z",size:"2.4 MB",status:"completed",type:"automatic"}],verification:{enabled:!0,lastVerified:"2024-01-20T10:35:00Z",status:"verified"}}};function o(e){let{children:a}=e,[t,o]=(0,s.useState)(n),[i,d]=(0,s.useState)(!1),[c,m]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=localStorage.getItem("revantad-settings");if(e)try{let a=JSON.parse(e);o({...n,...a})}catch(e){console.error("Error loading settings:",e)}},[]);let x=async()=>{d(!0);try{await new Promise(e=>setTimeout(e,1e3)),localStorage.setItem("revantad-settings",JSON.stringify(t)),m(!1),"dark"===t.appearance.theme?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),console.warn("Settings saved successfully:",t)}catch(e){throw console.error("Error saving settings:",e),e}finally{d(!1)}};return(0,r.jsx)(l.Provider,{value:{settings:t,updateSettings:(e,a)=>{o(t=>({...t,[e]:{...t[e],...a}})),m(!0)},saveSettings:x,resetSettings:e=>{e?o(a=>({...a,[e]:n[e]})):o(n),m(!0)},isLoading:i,hasUnsavedChanges:c},children:a})}function i(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},6071:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>aJ});var r=t(5155),s=t(1362),l=t(2115),n=t(7340),o=t(7108),i=t(7580),d=t(7213),c=t(7924),m=t(2098),x=t(3509),g=t(1007),u=t(4835),h=t(6874),b=t.n(h),p=t(283);function f(e){let{activeSection:a,setActiveSection:t}=e,[h,f]=(0,l.useState)(""),{setTheme:y,resolvedTheme:j}=(0,s.D)(),[v,k]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),{user:C,logout:A}=(0,p.A)();(0,l.useEffect)(()=>{w(!0)},[]);let S=[{id:"dashboard",label:"Home Dashboard",icon:n.A,tooltip:"Dashboard Overview"},{id:"products",label:"Product Lists",icon:o.A,tooltip:"Manage Products"},{id:"debts",label:"Customer Debts",icon:i.A,tooltip:"Customer Debt Management"},{id:"family-gallery",label:"Family Gallery",icon:d.A,tooltip:"Family Photos & Memories"}];return(0,r.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300",style:{backgroundColor:"dark"===j?"#111827":"#ffffff",borderColor:"dark"===j?"#374151":"#e5e7eb"},children:(0,r.jsxs)("div",{className:"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 w-auto",children:[(0,r.jsxs)(b(),{href:"/landing",className:"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0",title:"Return to Front Page",children:[(0,r.jsx)("div",{className:"w-10 h-10 hero-gradient rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"R"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gradient hidden sm:block",children:"Revantad"})]}),(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),console.log("Searching for:",h)},className:"w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search",value:h,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200"})]})})]}),(0,r.jsx)("div",{className:"hidden sm:flex items-center justify-center",children:(0,r.jsx)("div",{className:"flex items-center space-x-3 md:space-x-4 lg:space-x-5",children:S.map(e=>{let s=e.icon,l=a===e.id;return(0,r.jsxs)("button",{onClick:()=>{console.warn("\uD83D\uDD04 Navigation clicked:",e.id,"→",e.label),t(e.id)},className:"relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-300 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.08] hover:shadow-lg",style:{background:l?"dark"===j?"linear-gradient(135deg, rgba(34, 197, 94, 0.25) 0%, rgba(16, 185, 129, 0.2) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(16, 185, 129, 0.1) 100%)":"transparent",color:l?"dark"===j?"#4ade80":"#16a34a":"dark"===j?"#cbd5e1":"#374151",boxShadow:l?"dark"===j?"0 4px 12px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 12px rgba(34, 197, 94, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"none",border:l?"dark"===j?"1px solid rgba(34, 197, 94, 0.4)":"1px solid rgba(34, 197, 94, 0.3)":"1px solid transparent"},title:e.tooltip,onMouseEnter:e=>{l||(e.currentTarget.style.background="dark"===j?"linear-gradient(135deg, rgba(71, 85, 105, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%)":"linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)",e.currentTarget.style.color="dark"===j?"#f1f5f9":"#111827",e.currentTarget.style.boxShadow="dark"===j?"0 2px 8px rgba(0, 0, 0, 0.3)":"0 2px 8px rgba(0, 0, 0, 0.1)")},onMouseLeave:e=>{l||(e.currentTarget.style.background="transparent",e.currentTarget.style.color="dark"===j?"#cbd5e1":"#374151",e.currentTarget.style.boxShadow="none")},children:[l&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-12 md:w-14 lg:w-16 h-1.5 rounded-full transition-all duration-300",style:{background:"dark"===j?"linear-gradient(90deg, #4ade80 0%, #22c55e 100%)":"linear-gradient(90deg, #16a34a 0%, #22c55e 100%)",boxShadow:"0 2px 6px rgba(34, 197, 94, 0.5)"}}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl opacity-20 transition-all duration-300",style:{background:"dark"===j?"linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%)",filter:"blur(1px)"}})]}),(0,r.jsx)(s,{className:"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-300 group-hover:scale-110 relative z-10"}),(0,r.jsxs)("div",{className:"absolute top-full mt-4 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-gray-800 text-white text-xs px-4 py-3 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50 shadow-xl border border-gray-700",children:[(0,r.jsx)("div",{className:"font-semibold text-white",children:e.label}),(0,r.jsx)("div",{className:"text-gray-300 text-[10px] mt-1",children:e.tooltip}),(0,r.jsx)("div",{className:"absolute -top-1.5 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-gray-900 dark:bg-gray-800 rotate-45 border-l border-t border-gray-700"})]})]},e.id)})})}),(0,r.jsx)("div",{className:"sm:hidden flex items-center justify-center",children:(0,r.jsx)("button",{onClick:()=>t("dashboard"===a?"products":"dashboard"),className:"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",title:"Toggle View",children:"dashboard"===a?(0,r.jsx)(o.A,{className:"h-5 w-5"}):(0,r.jsx)(n.A,{className:"h-5 w-5"})})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>{if(!N)return;let e=document.documentElement;"dark"===j?(e.classList.remove("dark"),y("light")):(e.classList.add("dark"),y("dark"))},className:"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0",title:N?"Switch to ".concat("dark"===j?"light":"dark"," mode (Current: ").concat(j,")"):"Toggle theme",disabled:!N,children:N?"dark"===j?(0,r.jsx)(m.A,{className:"h-5 w-5"}):(0,r.jsx)(x.A,{className:"h-5 w-5"}):(0,r.jsx)("div",{className:"h-5 w-5 bg-gray-400 rounded-full animate-pulse"})}),(0,r.jsxs)("div",{className:"relative flex-shrink-0",children:[(0,r.jsxs)("button",{onClick:()=>k(!v),className:"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow",children:(0,r.jsx)(g.A,{className:"h-4 w-4 text-white"})}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors",children:(null==C?void 0:C.name)||"Admin"})]}),v&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1",children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200 dark:border-slate-700",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(null==C?void 0:C.name)||"Admin User"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:(null==C?void 0:C.email)||"<EMAIL>"})]}),(0,r.jsxs)("button",{onClick:()=>t("settings"),className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsxs)("button",{onClick:()=>{A(),window.location.href="/login"},className:"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Logout"})]})]})]})]})]})})}var y=t(5657),j=t(2713),v=t(9676),k=t(9074),N=t(381),w=t(3052),C=t(2355);function A(e){let{activeSection:a,setActiveSection:t}=e,{resolvedTheme:n}=(0,s.D)(),[o,i]=(0,l.useState)(!1),[d,c]=(0,l.useState)(!1);(0,l.useEffect)(()=>{c(!0);let e=localStorage.getItem("sidebar-collapsed");null!==e&&i(JSON.parse(e))},[]),(0,l.useEffect)(()=>{d&&localStorage.setItem("sidebar-collapsed",JSON.stringify(o))},[o,d]);let m=[{id:"ai-support",label:"AI Support",icon:y.A},{id:"api-graphing",label:"API Graphing & Visuals",icon:j.A},{id:"history",label:"History",icon:v.A},{id:"calendar",label:"Calendar",icon:k.A},{id:"settings",label:"Settings",icon:N.A}];return(0,r.jsx)("div",{className:"shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] transition-all duration-300 ease-in-out ".concat(o?"w-20 min-w-20":"w-80 min-w-80"),style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",borderColor:"dark"===n?"#334155":"#e5e7eb",borderWidth:"1px",boxShadow:"dark"===n?"0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)":"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)"},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ".concat(o?"px-3 py-3":"px-6 py-4"),style:{background:"dark"===n?"linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)":"linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)",borderBottom:"dark"===n?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===n?"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)":"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},children:[(0,r.jsxs)("div",{className:"flex items-center ".concat(o?"justify-center":"mb-3"),children:[(0,r.jsx)("div",{className:"rounded-lg flex items-center justify-center transition-all duration-300 ".concat(o?"w-10 h-10":"w-8 h-8 mr-3"),style:{background:"dark"===n?"linear-gradient(135deg, #22c55e 0%, #16a34a 100%)":"linear-gradient(135deg, #10b981 0%, #059669 100%)",boxShadow:"0 4px 8px rgba(34, 197, 94, 0.3)"},children:(0,r.jsx)("span",{className:"text-white font-bold ".concat(o?"text-base":"text-sm"),children:"⚡"})}),!o&&(0,r.jsx)("h2",{className:"text-lg font-bold transition-all duration-300 crisp-text",style:{color:"dark"===n?"#f8fafc":"#111827",textShadow:"dark"===n?"0 1px 2px rgba(0, 0, 0, 0.3)":"none"},children:"Additional Tools"})]}),!o&&(0,r.jsx)("p",{className:"text-xs font-medium transition-all duration-300 crisp-text",style:{color:"dark"===n?"#94a3b8":"#64748b",letterSpacing:"0.025em"},children:"Advanced features and utilities"})]}),(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)("button",{onClick:()=>{i(!o)},className:"absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group",style:{backgroundColor:"dark"===n?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",border:"dark"===n?"1px solid rgba(34, 197, 94, 0.3)":"1px solid rgba(34, 197, 94, 0.2)",boxShadow:"dark"===n?"0 2px 8px rgba(34, 197, 94, 0.2)":"0 2px 8px rgba(34, 197, 94, 0.15)"},title:o?"Expand Sidebar":"Collapse Sidebar",children:o?(0,r.jsx)(w.A,{className:"w-4 h-4 transition-all duration-200 group-hover:scale-105",style:{color:"dark"===n?"#4ade80":"#16a34a"}}):(0,r.jsx)(C.A,{className:"w-4 h-4 transition-all duration-200 group-hover:scale-105",style:{color:"dark"===n?"#4ade80":"#16a34a"}})}),(0,r.jsx)("div",{className:"absolute inset-0 scroll-fade-top scroll-fade-bottom",children:(0,r.jsx)("nav",{className:"h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ".concat(o?"px-2":"px-4"),children:m.map(e=>{let s=e.icon,l=a===e.id;return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("button",{onClick:()=>t(e.id),className:"w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ".concat(o?"p-2.5 rounded-lg justify-center":"p-3 rounded-xl"),style:{background:l?"dark"===n?"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)":"transparent",border:l?"dark"===n?"1px solid rgba(34, 197, 94, 0.4)":"1px solid rgba(34, 197, 94, 0.3)":"1px solid transparent",boxShadow:l?"dark"===n?"0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"none"},onMouseEnter:e=>{l||(e.currentTarget.style.background="dark"===n?"rgba(71, 85, 105, 0.15)":"rgba(243, 244, 246, 0.6)",e.currentTarget.style.border="dark"===n?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.6)")},onMouseLeave:e=>{l||(e.currentTarget.style.background="transparent",e.currentTarget.style.border="1px solid transparent")},children:o?(0,r.jsx)(s,{className:"h-5 w-5 transition-all duration-200 relative z-10",style:{color:l?"dark"===n?"#4ade80":"#16a34a":"dark"===n?"#e2e8f0":"#64748b",filter:l?"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))":"none"}}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden",style:{background:l?"dark"===n?"linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)":"dark"===n?"linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)":"linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)",boxShadow:l?"dark"===n?"0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"dark"===n?"inset 0 1px 0 rgba(255, 255, 255, 0.05)":"inset 0 1px 0 rgba(255, 255, 255, 0.9)"},children:(0,r.jsx)(s,{className:"h-4 w-4 transition-all duration-200 relative z-10",style:{color:l?"dark"===n?"#4ade80":"#16a34a":"dark"===n?"#e2e8f0":"#64748b",filter:l?"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))":"none"}})}),(0,r.jsx)("div",{className:"flex-1 sidebar-text",children:(0,r.jsx)("h3",{className:"font-medium text-sm transition-colors duration-200 leading-snug",style:{color:l?"dark"===n?"#4ade80":"#16a34a":"dark"===n?"#f8fafc":"#111827",fontWeight:l?"600":"500"},children:e.label})})]})}),o&&(0,r.jsxs)("div",{className:"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",color:"dark"===n?"#f8fafc":"#111827",border:"dark"===n?"1px solid rgba(148, 163, 184, 0.3)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===n?"0 4px 12px rgba(0, 0, 0, 0.3)":"0 4px 12px rgba(0, 0, 0, 0.15)"},children:[(0,r.jsx)("div",{className:"font-semibold",children:e.label}),(0,r.jsx)("div",{className:"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0",style:{borderTop:"6px solid transparent",borderBottom:"6px solid transparent",borderRight:"6px solid ".concat("dark"===n?"#1e293b":"#ffffff")}})]})]},e.id)})})})]}),(0,r.jsx)("div",{className:"sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ".concat(o?"px-3 py-3":"px-6 py-4"),style:{background:"dark"===n?"linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)":"linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)",borderTop:"dark"===n?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===n?"0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)":"0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)"},children:(0,r.jsx)("div",{className:"text-sm transition-colors duration-300",style:{color:"dark"===n?"#94a3b8":"#64748b"},children:o?(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10",style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",boxShadow:"0 4px 8px rgba(59, 130, 246, 0.3)"},children:[(0,r.jsx)("span",{className:"text-white font-bold relative z-10 text-base",children:"R"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)"}})]})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2 space-x-3",children:[(0,r.jsxs)("div",{className:"rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8",style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",boxShadow:"0 4px 8px rgba(59, 130, 246, 0.3)"},children:[(0,r.jsx)("span",{className:"text-white font-bold relative z-10 text-sm",children:"R"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)"}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-bold text-sm transition-colors duration-300 block",style:{color:"dark"===n?"#f8fafc":"#1e293b",textShadow:"dark"===n?"0 1px 2px rgba(0, 0, 0, 0.3)":"none"},children:"Revantad Store"}),(0,r.jsx)("span",{className:"text-xs font-medium",style:{color:"dark"===n?"#64748b":"#94a3b8",letterSpacing:"0.025em"},children:"Professional Business Management"})]})]}),(0,r.jsx)("div",{className:"text-xs font-medium px-3 py-2 rounded-lg",style:{backgroundColor:"dark"===n?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",color:"dark"===n?"#cbd5e1":"#6b7280",border:"dark"===n?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.6)"},children:"Admin Dashboard v2.0"})]})})}),!o&&(0,r.jsx)("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 opacity-60 pointer-events-none",style:{color:"dark"===n?"#94a3b8":"#64748b"},children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Scroll for more"}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-current animate-pulse"}),(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-current animate-pulse",style:{animationDelay:"0.2s"}}),(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-current animate-pulse",style:{animationDelay:"0.4s"}})]})]})})]})})}var S=t(5868),D=t(1243),_=t(9397),P=t(4186),M=t(3904),L=t(4870),T=t(8515),R=t(1539),E=t(4616),I=t(1586),F=t(7809),z=t(6785),O=t(3861),U=t(4355);function B(e){let{stats:a,onSectionChange:t}=e,{resolvedTheme:n}=(0,s.D)(),[d,c]=(0,l.useState)(!1),[m,x]=(0,l.useState)(new Date),[g,u]=(0,l.useState)(a);(0,l.useEffect)(()=>{let e=setInterval(()=>{x(new Date)},1e3);return()=>clearInterval(e)},[]),(0,l.useEffect)(()=>{u(a)},[a]);let h=async()=>{c(!0),await new Promise(e=>setTimeout(e,1e3)),c(!1)},b=[{label:"Products in List",value:g.totalProducts,previousValue:Math.max(0,g.totalProducts-Math.floor(5*Math.random())),change:12.5,changeType:"increase",icon:o.A,color:"#3b82f6",bgColor:"dark"===n?"rgba(59, 130, 246, 0.1)":"rgba(59, 130, 246, 0.05)"},{label:"Customer Debts",value:g.totalDebts,previousValue:Math.max(0,g.totalDebts+Math.floor(3*Math.random())),change:-8.3,changeType:"decrease",icon:i.A,color:"#10b981",bgColor:"dark"===n?"rgba(16, 185, 129, 0.1)":"rgba(16, 185, 129, 0.05)"},{label:"Total Debt Amount",value:g.totalDebtAmount,previousValue:g.totalDebtAmount+Math.floor(1e3*Math.random()),change:-15.7,changeType:"decrease",icon:S.A,color:"#f59e0b",bgColor:"dark"===n?"rgba(245, 158, 11, 0.1)":"rgba(245, 158, 11, 0.05)"},{label:"Low Stock Items",value:g.lowStockItems,previousValue:Math.max(0,g.lowStockItems+Math.floor(2*Math.random())),change:-25,changeType:"decrease",icon:D.A,color:"#ef4444",bgColor:"dark"===n?"rgba(239, 68, 68, 0.1)":"rgba(239, 68, 68, 0.05)"}],p=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),f=e=>{if(t)switch(e){case"add-product":case"manage-stock":t("products");break;case"record-debt":t("debts");break;case"view-analytics":t("api-graphing");break;case"view-history":t("history")}};return(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===n?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold mb-2 flex items-center gap-3",style:{color:"dark"===n?"#f8fafc":"#111827"},children:[(0,r.jsx)(_.A,{className:"h-7 w-7 text-green-500"}),"Dashboard Overview of your Revantad Store"]}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-2",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),"Last updated: ",m.toLocaleTimeString("en-PH",{hour:"2-digit",minute:"2-digit",second:"2-digit"})," • Real-time monitoring active"]})]}),(0,r.jsxs)("button",{onClick:h,disabled:d,className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",style:{backgroundColor:"dark"===n?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",border:"dark"===n?"1px solid rgba(34, 197, 94, 0.3)":"1px solid rgba(34, 197, 94, 0.2)",color:"dark"===n?"#4ade80":"#16a34a"},children:[(0,r.jsx)(M.A,{className:"h-4 w-4 ".concat(d?"animate-spin":"")}),d?"Refreshing...":"Refresh"]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,a)=>{let t=e.icon,s="increase"===e.changeType?L.A:T.A,l="increase"===e.changeType;return(0,r.jsxs)("div",{className:"group rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] cursor-pointer border",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===n?"linear-gradient(135deg, #1e293b 0%, ".concat(e.bgColor," 100%)"):"linear-gradient(135deg, #ffffff 0%, ".concat(e.bgColor," 100%)")},children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl transition-all duration-300 group-hover:scale-110",style:{backgroundColor:e.bgColor,border:"1px solid ".concat(e.color,"20")},children:(0,r.jsx)(t,{className:"h-6 w-6 transition-all duration-300",style:{color:e.color}})}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(s,{className:"h-4 w-4 ".concat(l?"text-green-500":"text-red-500")}),(0,r.jsxs)("span",{className:"text-sm font-semibold ".concat(l?"text-green-500":"text-red-500"),children:[Math.abs(e.change),"%"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium mb-2 transition-colors duration-300",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:e.label}),(0,r.jsx)("p",{className:"text-3xl font-bold mb-1 transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:e.label.includes("Amount")?p(e.value):e.value.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs transition-colors duration-300",style:{color:"dark"===n?"#94a3b8":"#9ca3af"},children:["vs ",e.label.includes("Amount")?p(e.previousValue):e.previousValue.toLocaleString()," last period"]})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("div",{className:"h-1 rounded-full overflow-hidden",style:{backgroundColor:"dark"===n?"#334155":"#f1f5f9"},children:(0,r.jsx)("div",{className:"h-full rounded-full transition-all duration-1000 ease-out",style:{backgroundColor:e.color,width:"".concat(Math.min(100,e.value/(e.value+e.previousValue)*100),"%")}})})})]},a)})}),(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===n?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(R.A,{className:"h-6 w-6 text-yellow-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Quick Actions"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>f("add-product"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",style:{border:"dark"===n?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===n?"#334155":"#f9fafb",background:"dark"===n?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to Products section to add new items",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(59, 130, 246, 0.1)"},children:(0,r.jsx)(E.A,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Add Product"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Add new item to inventory"})]}),(0,r.jsxs)("button",{onClick:()=>f("record-debt"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",style:{border:"dark"===n?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===n?"#334155":"#f9fafb",background:"dark"===n?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to Debts section to record customer debt",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(16, 185, 129, 0.1)"},children:(0,r.jsx)(I.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Record Debt"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Add customer debt record"})]}),(0,r.jsxs)("button",{onClick:()=>f("view-analytics"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2",style:{border:"dark"===n?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===n?"#334155":"#f9fafb",background:"dark"===n?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to API Graphing & Visuals for business analytics",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(245, 158, 11, 0.1)"},children:(0,r.jsx)(j.A,{className:"h-6 w-6 text-yellow-600"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"View Analytics"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Business insights & reports"})]}),(0,r.jsxs)("button",{onClick:()=>f("manage-stock"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2",style:{border:"dark"===n?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===n?"#334155":"#f9fafb",background:"dark"===n?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to Products section to manage inventory levels",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(139, 69, 19, 0.1)"},children:(0,r.jsx)(F.A,{className:"h-6 w-6 text-amber-700"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Manage Stock"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Update inventory levels"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===n?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(z.A,{className:"h-6 w-6 text-blue-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Store Overview"})]}),(0,r.jsx)("div",{className:"space-y-4",children:[{label:"Products in List",value:g.totalProducts,icon:o.A,color:"#3b82f6"},{label:"Outstanding Debts",value:g.totalDebts,icon:i.A,color:"#10b981"},{label:"Total Amount Owed",value:p(g.totalDebtAmount),icon:S.A,color:"#f59e0b"},{label:"Items Need Restocking",value:g.lowStockItems,icon:D.A,color:g.lowStockItems>0?"#ef4444":"#10b981"}].map((e,a)=>{let t=e.icon;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] border",style:{backgroundColor:"dark"===n?"#334155":"#f9fafb",border:"dark"===n?"1px solid #475569":"1px solid #e5e7eb"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:"".concat(e.color,"20")},children:(0,r.jsx)(t,{className:"h-5 w-5",style:{color:e.color}})}),(0,r.jsx)("span",{className:"font-medium transition-colors duration-300",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:e.label})]}),(0,r.jsx)("span",{className:"text-lg font-bold transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"string"==typeof e.value?e.value:e.value.toLocaleString()})]},a)})})]}),(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===n?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(O.A,{className:"h-6 w-6 text-green-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Recent Activities"})]}),(0,r.jsx)("button",{onClick:()=>f("view-history"),className:"text-sm px-3 py-1 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1",style:{backgroundColor:"dark"===n?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",color:"dark"===n?"#4ade80":"#16a34a"},title:"Navigate to History section for detailed activity logs",children:"View All"})]}),(0,r.jsx)("div",{className:"space-y-3",children:[{action:"New product added",item:"Coca Cola 1.5L",time:"2 minutes ago",type:"product",icon:o.A,priority:"normal",clickable:!0},{action:"Debt payment received",item:"Juan Dela Cruz - ₱500",time:"15 minutes ago",type:"payment",icon:S.A,priority:"high",clickable:!0},{action:"Low stock alert",item:"Rice 25kg - Only 3 left",time:"1 hour ago",type:"alert",icon:D.A,priority:"urgent",clickable:!0},{action:"New customer debt",item:"Maria Santos - ₱1,200",time:"2 hours ago",type:"debt",icon:i.A,priority:"normal",clickable:!0}].map((e,a)=>{let t=e.icon,s=e=>{switch(e){case"product":return"#3b82f6";case"payment":return"#10b981";case"alert":return"#ef4444";case"debt":return"#f59e0b";default:return"#6b7280"}},l=(e=>{switch(e){case"urgent":return{color:"#ef4444",pulse:!0};case"high":return{color:"#f59e0b",pulse:!1};case"normal":return{color:"#10b981",pulse:!1};default:return{color:"#6b7280",pulse:!1}}})(e.priority);return(0,r.jsxs)("button",{onClick:()=>e.clickable&&f("product"===e.type?"add-product":"debt"===e.type?"record-debt":"view-history"),className:"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-300 hover:scale-[1.01] border text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 ".concat(e.clickable?"cursor-pointer":"cursor-default"),style:{backgroundColor:"dark"===n?"#334155":"#f9fafb",border:"dark"===n?"1px solid #475569":"1px solid #e5e7eb"},disabled:!e.clickable,title:e.clickable?"Click to navigate to ".concat(e.type," section"):"",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"p-2 rounded-full flex-shrink-0 ".concat(l.pulse?"animate-pulse":""),style:{backgroundColor:"".concat(s(e.type),"20")},children:(0,r.jsx)(t,{className:"h-4 w-4",style:{color:s(e.type)}})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 ".concat(l.pulse?"animate-ping":""),style:{backgroundColor:l.color,borderColor:"dark"===n?"#334155":"#f9fafb"}})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate",style:{color:"dark"===n?"#f8fafc":"#111827"},children:e.action}),(0,r.jsx)("p",{className:"text-xs truncate",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:e.item})]}),(0,r.jsx)("span",{className:"text-xs flex-shrink-0",style:{color:"dark"===n?"#94a3b8":"#9ca3af"},children:e.time})]},a)})})]})]}),(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===n?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(U.A,{className:"h-6 w-6 text-purple-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"Performance Summary"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[(0,r.jsxs)("svg",{className:"w-20 h-20 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"dark"===n?"#334155":"#e5e7eb",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#10b981",strokeWidth:"2",strokeDasharray:"85, 100",className:"animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-green-500",children:"85%"})})]}),(0,r.jsx)("p",{className:"text-sm font-medium",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Store Health"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[(0,r.jsxs)("svg",{className:"w-20 h-20 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"dark"===n?"#334155":"#e5e7eb",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#3b82f6",strokeWidth:"2",strokeDasharray:"72, 100",className:"animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-blue-500",children:"72%"})})]}),(0,r.jsx)("p",{className:"text-sm font-medium",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Inventory Level"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[(0,r.jsxs)("svg",{className:"w-20 h-20 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"dark"===n?"#334155":"#e5e7eb",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#f59e0b",strokeWidth:"2",strokeDasharray:"58, 100",className:"animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-yellow-500",children:"58%"})})]}),(0,r.jsx)("p",{className:"text-sm font-medium",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Debt Recovery"})]})]})]})]})}var q=t(4792),H=t(8500),G=t(3109),V=t(6932),W=t(1788),Y=t(9881),K=t(8832),J=t(7712),X=t(2657),Z=t(646);function Q(e){let{stats:a}=e,{resolvedTheme:t}=(0,s.D)(),[n,d]=(0,l.useState)({salesData:[],debtData:[],categoryData:[],trendData:[],performanceMetrics:{revenue:{current:0,previous:0,change:0},customers:{current:0,previous:0,change:0},products:{current:0,previous:0,change:0},efficiency:{current:0,previous:0,change:0}}}),[c,m]=(0,l.useState)({dateRange:"month",chartType:"line",dataType:"all",showTrends:!0,showForecasting:!1}),[x,g]=(0,l.useState)("all"),[u,h]=(0,l.useState)(!1),[b,p]=(0,l.useState)(new Date),[f,y]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=()=>{y(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let j=(0,l.useCallback)(()=>{h(!0);let e=()=>[.8,.85,.9,1,1.1,1.2,1.3,1.25,1.15,1.05,.95,.9].map(e=>Math.floor(25e3*e*(1+(Math.random()-.5)*.2))),t=()=>[.9,1,1.1,1.2,1.3,1.1,.8].map(e=>Math.floor(8e3*e*(1+(Math.random()-.5)*.15))),r=()=>{let e=[{name:"Beverages",value:35,color:"#22c55e"},{name:"Snacks",value:28,color:"#3b82f6"},{name:"Household",value:20,color:"#f59e0b"},{name:"Personal Care",value:12,color:"#ef4444"},{name:"Others",value:5,color:"#8b5cf6"}].map(e=>({...e,value:e.value+Math.floor((Math.random()-.5)*10)}));return"all"!==x&&(e=e.filter(e=>e.name.toLowerCase()===x.toLowerCase())),e},s=()=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e,a)=>{let t=2e4+2e3*a+Math.floor(5e3*Math.random()),r=5e3+Math.floor(3e3*Math.random());return{month:e,sales:t,debt:r,profit:.3*t-.1*r}}),l=(e,a)=>{let t=e.reduce((e,a)=>e+a,0),r=t*(.85+.3*Math.random());return{revenue:{current:t,previous:r,change:(t-r)/r*100},customers:{current:a.totalDebts,previous:Math.floor(a.totalDebts*(.9+.2*Math.random())),change:Math.floor((Math.random()-.5)*20)},products:{current:a.totalProducts,previous:Math.floor(a.totalProducts*(.95+.1*Math.random())),change:Math.floor((Math.random()-.3)*15)},efficiency:{current:85+Math.floor(10*Math.random()),previous:80+Math.floor(10*Math.random()),change:Math.floor((Math.random()-.4)*10)}}};setTimeout(()=>{let n=e(),o=t(),i=r(),c=s(),m=l(n,a);d({salesData:n,debtData:o,categoryData:i,trendData:c,performanceMetrics:m}),p(new Date),h(!1)},800)},[a]);(0,l.useEffect)(()=>{j()},[j,c.dateRange,x]);let v=()=>({backgroundColor:"dark"===t?"#1e293b":"#ffffff",textStyle:{color:"dark"===t?"#f1f5f9":"#1f2937",fontFamily:"Inter, system-ui, sans-serif"},grid:{borderColor:"dark"===t?"#334155":"#e5e7eb"}}),k=(0,l.useMemo)(()=>({...v(),title:{text:"Monthly Sales Revenue",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{trigger:"axis",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:e=>{let a=e[0];return'\n          <div style="padding: 8px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">'.concat(a.name,'</div>\n            <div style="display: flex; align-items: center;">\n              <div style="width: 10px; height: 10px; background: ').concat(a.color,'; border-radius: 50%; margin-right: 8px;"></div>\n              Revenue: ₱').concat(a.value.toLocaleString(),'\n            </div>\n            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">\n              ').concat(a.value>(n.salesData[a.dataIndex-1]||0)?"↗️ Increased":"↘️ Decreased"," from previous month\n            </div>\n          </div>\n        ")}},legend:{show:!0,top:40,textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},xAxis:{type:"category",data:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12}},yAxis:{type:"value",axisLabel:{formatter:"₱{value}",color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12},axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},splitLine:{lineStyle:{color:"dark"===t?"#334155":"#f3f4f6",type:"dashed"}}},series:[{name:"Revenue",data:n.salesData,type:c.chartType,smooth:!0,lineStyle:{color:"#22c55e",width:3},itemStyle:{color:"#22c55e",borderRadius:"bar"===c.chartType?[4,4,0,0]:0},areaStyle:"area"===c.chartType?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(34, 197, 94, 0.4)"},{offset:1,color:"rgba(34, 197, 94, 0.05)"}]}}:void 0,emphasis:{focus:"series",itemStyle:{shadowBlur:10,shadowColor:"rgba(34, 197, 94, 0.5)"}},markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}],itemStyle:{color:"#facc15"}},markLine:c.showTrends?{data:[{type:"average",name:"Average"}],lineStyle:{color:"#f59e0b",type:"dashed"}}:void 0}],grid:{left:"3%",right:"4%",bottom:"10%",top:"15%",containLabel:!0},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100,handleIcon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",handleSize:"80%",handleStyle:{color:"#22c55e",shadowBlur:3,shadowColor:"rgba(0, 0, 0, 0.6)",shadowOffsetX:2,shadowOffsetY:2}}],toolbox:{feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{pixelRatio:2}},iconStyle:{borderColor:"dark"===t?"#9ca3af":"#6b7280"}}}),[n.salesData,c.chartType,t]),w=(0,l.useMemo)(()=>({...v(),title:{text:"Weekly Customer Debt Trends",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{trigger:"axis",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:e=>{let a=e[0],t=(a.value/n.debtData.reduce((e,a)=>e+a,0)*100).toFixed(1);return'\n          <div style="padding: 8px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">'.concat(a.name,'</div>\n            <div style="display: flex; align-items: center;">\n              <div style="width: 10px; height: 10px; background: ').concat(a.color,'; border-radius: 2px; margin-right: 8px;"></div>\n              Total Debt: ₱').concat(a.value.toLocaleString(),'\n            </div>\n            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">\n              ').concat(t,"% of weekly total\n            </div>\n          </div>\n        ")}},legend:{show:!0,top:40,textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},xAxis:{type:"category",data:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12,rotate:45}},yAxis:{type:"value",axisLabel:{formatter:"₱{value}",color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12},axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},splitLine:{lineStyle:{color:"dark"===t?"#334155":"#f3f4f6",type:"dashed"}}},series:[{name:"Customer Debt",data:n.debtData,type:"bar",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#facc15"},{offset:1,color:"#eab308"}]},borderRadius:[4,4,0,0]},emphasis:{focus:"series",itemStyle:{color:"#f59e0b",shadowBlur:10,shadowColor:"rgba(245, 158, 11, 0.5)"}},markPoint:{data:[{type:"max",name:"Peak Day"},{type:"min",name:"Low Day"}],itemStyle:{color:"#ef4444"}}}],grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},toolbox:{feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{pixelRatio:2}},iconStyle:{borderColor:"dark"===t?"#9ca3af":"#6b7280"}}}),[n.debtData,t]),C=(0,l.useMemo)(()=>({...v(),title:{text:"Product Categories Distribution",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{trigger:"item",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:e=>'\n          <div style="padding: 8px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">'.concat(e.name,'</div>\n            <div style="display: flex; align-items: center;">\n              <div style="width: 10px; height: 10px; background: ').concat(e.color,'; border-radius: 50%; margin-right: 8px;"></div>\n              Value: ').concat(e.value,'%\n            </div>\n            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">\n              ').concat(e.percent,"% of total sales\n            </div>\n          </div>\n        ")},legend:{orient:"horizontal",bottom:10,textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563",fontSize:12}},grid:{top:40,bottom:50,left:20,right:20,containLabel:!0},series:[{name:"Categories",type:"pie",radius:["25%","45%"],center:["50%","50%"],avoidLabelOverlap:!0,itemStyle:{borderRadius:6,borderColor:"dark"===t?"#1e293b":"#ffffff",borderWidth:2},label:{show:!0,position:"outside",formatter:"{b}: {c}%",color:"dark"===t?"#cbd5e1":"#4b5563",fontSize:12,fontWeight:"500",distanceToLabelLine:8,padding:[2,4],backgroundColor:"dark"===t?"rgba(30, 41, 59, 0.8)":"rgba(255, 255, 255, 0.9)",borderColor:"dark"===t?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",borderWidth:1,borderRadius:4},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},label:{show:!0,fontSize:13,fontWeight:"bold"}},labelLine:{show:!0,length:20,length2:10,lineStyle:{color:"dark"===t?"#475569":"#d1d5db",width:1.5}},data:n.categoryData}]}),[n.categoryData,t]),A=(0,l.useMemo)(()=>{let e=Array.from({length:24},(e,a)=>"".concat(a,":00")),a=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],r=[];for(let e=0;e<7;e++)for(let a=0;a<24;a++){let t=Math.floor(100*Math.random())+10;r.push([a,e,t])}return{...v(),title:{text:"Sales Activity Heatmap",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{position:"top",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:t=>'\n            <div style="padding: 8px;">\n              <div style="font-weight: bold; margin-bottom: 4px;">'.concat(a[t.data[1]]," ").concat(e[t.data[0]],'</div>\n              <div style="display: flex; align-items: center;">\n                <div style="width: 10px; height: 10px; background: ').concat(t.color,'; border-radius: 2px; margin-right: 8px;"></div>\n                Sales Activity: ').concat(t.data[2],"%\n              </div>\n            </div>\n          ")},grid:{height:"60%",top:"15%"},xAxis:{type:"category",data:e,splitArea:{show:!0},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:10}},yAxis:{type:"category",data:a,splitArea:{show:!0},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12}},visualMap:{min:0,max:100,calculable:!0,orient:"horizontal",left:"center",bottom:"5%",inRange:{color:["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffbf","#fee090","#fdae61","#f46d43","#d73027","#a50026"]},textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},series:[{name:"Sales Activity",type:"heatmap",data:r,label:{show:!1},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}},[t]),D=(0,l.useMemo)(()=>({...v(),title:{text:"Business Performance",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},series:[{name:"Performance",type:"gauge",center:["50%","60%"],startAngle:200,endAngle:-40,min:0,max:100,splitNumber:10,itemStyle:{color:"#22c55e"},progress:{show:!0,width:30},pointer:{show:!1},axisLine:{lineStyle:{width:30,color:[[.3,"#ef4444"],[.7,"#f59e0b"],[1,"#22c55e"]]}},axisTick:{distance:-45,splitNumber:5,lineStyle:{width:2,color:"dark"===t?"#475569":"#d1d5db"}},splitLine:{distance:-52,length:14,lineStyle:{width:3,color:"dark"===t?"#475569":"#d1d5db"}},axisLabel:{distance:-20,color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12},anchor:{show:!1},title:{show:!1},detail:{valueAnimation:!0,width:"60%",lineHeight:40,borderRadius:8,offsetCenter:[0,"-15%"],fontSize:24,fontWeight:"bold",formatter:"{value}%",color:"dark"===t?"#f1f5f9":"#1f2937"},data:[{value:n.performanceMetrics.efficiency.current,name:"Efficiency"}]}]}),[n.performanceMetrics.efficiency.current,t]),L=(0,l.useMemo)(()=>{let e=n.salesData.reduce((e,a)=>e+a,0),a=e/n.salesData.length,t=n.debtData.reduce((e,a)=>e+a,0)/n.debtData.length;return[{title:"Total Revenue",value:"₱"+e.toLocaleString(),icon:S.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-50 dark:bg-green-900/20",change:"+12.5%",changeColor:"text-green-600 dark:text-green-400",trend:"up",subtitle:"Avg: ₱".concat(a.toLocaleString(),"/month")},{title:"Active Customers",value:n.performanceMetrics.customers.current.toString(),icon:i.A,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-50 dark:bg-blue-900/20",change:"".concat(n.performanceMetrics.customers.change>0?"+":"").concat(n.performanceMetrics.customers.change.toFixed(1),"%"),changeColor:n.performanceMetrics.customers.change>0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",trend:n.performanceMetrics.customers.change>0?"up":"down",subtitle:"Customer base growth"},{title:"Products Listed",value:n.performanceMetrics.products.current.toString(),icon:o.A,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-50 dark:bg-purple-900/20",change:"".concat(n.performanceMetrics.products.change>0?"+":"").concat(n.performanceMetrics.products.change.toFixed(1),"%"),changeColor:n.performanceMetrics.products.change>0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",trend:n.performanceMetrics.products.change>0?"up":"down",subtitle:"Inventory expansion"},{title:"Business Efficiency",value:"".concat(n.performanceMetrics.efficiency.current,"%"),icon:z.A,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-50 dark:bg-orange-900/20",change:"".concat(n.performanceMetrics.efficiency.change>0?"+":"").concat(n.performanceMetrics.efficiency.change.toFixed(1),"%"),changeColor:n.performanceMetrics.efficiency.change>0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",trend:n.performanceMetrics.efficiency.change>0?"up":"down",subtitle:"Operational performance"},{title:"Weekly Debt Avg",value:"₱"+t.toLocaleString(),icon:H.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-50 dark:bg-yellow-900/20",change:"-3.2%",changeColor:"text-green-600 dark:text-green-400",trend:"down",subtitle:"Daily average debt"},{title:"Growth Rate",value:"".concat((e/(.85*e)*100-100).toFixed(1),"%"),icon:G.A,color:"text-emerald-600 dark:text-emerald-400",bgColor:"bg-emerald-50 dark:bg-emerald-900/20",change:"+15.8%",changeColor:"text-green-600 dark:text-green-400",trend:"up",subtitle:"Monthly growth rate"}]},[n,a.totalDebtAmount]);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(()=>(0,r.jsx)("div",{className:"card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(V.A,{className:"h-4 w-4 text-gray-500 dark:text-gray-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Filters:"})]}),(0,r.jsxs)("select",{value:c.dateRange,onChange:e=>m(a=>({...a,dateRange:e.target.value})),className:"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"week",children:"This Week"}),(0,r.jsx)("option",{value:"month",children:"This Month"}),(0,r.jsx)("option",{value:"quarter",children:"This Quarter"}),(0,r.jsx)("option",{value:"year",children:"This Year"})]}),(0,r.jsxs)("select",{value:c.chartType,onChange:e=>m(a=>({...a,chartType:e.target.value})),className:"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"line",children:"Line Chart"}),(0,r.jsx)("option",{value:"bar",children:"Bar Chart"}),(0,r.jsx)("option",{value:"area",children:"Area Chart"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:j,disabled:u,className:"flex items-center space-x-2 px-3 py-1 text-sm bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 ".concat(u?"animate-spin":"")}),(0,r.jsx)("span",{children:u?"Updating...":"Refresh"})]}),(0,r.jsxs)("button",{className:"flex items-center space-x-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200",children:[(0,r.jsx)(W.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Export"})]})]})]})}),{}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 sm:gap-4",children:L.map((e,a)=>(0,r.jsx)("div",{className:"card p-4 hover:shadow-lg transition-all duration-300 group",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor," group-hover:scale-110 transition-transform duration-200"),children:(0,r.jsx)(e.icon,{className:"h-5 w-5 ".concat(e.color)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:["up"===e.trend&&(0,r.jsx)(Y.A,{className:"h-3 w-3 text-green-500"}),"down"===e.trend&&(0,r.jsx)(K.A,{className:"h-3 w-3 text-red-500"}),"neutral"===e.trend&&(0,r.jsx)(J.A,{className:"h-3 w-3 text-gray-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide",children:e.title}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white mt-1",children:e.value}),e.subtitle&&(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.subtitle}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"text-xs font-medium ".concat(e.changeColor),children:e.change}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"vs last period"})]})]})]})},a))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6",children:[(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)(G.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Sales Revenue"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Monthly performance trends"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Live"})]})})]}),u?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):(0,r.jsx)(q.A,{option:k,style:{height:f?"300px":"400px"}})]}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsx)(i.A,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Customer Debt"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Weekly debt patterns"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Active"})]})})]}),u?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):(0,r.jsx)(q.A,{option:w,style:{height:f?"300px":"400px"}})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6",children:[(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/20 rounded-lg shadow-sm",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Product Categories"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Interactive sales distribution by category"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1 px-2 py-1 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"Live"})]}),(0,r.jsx)("button",{className:"p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200",title:"View options",children:(0,r.jsx)(X.A,{className:"h-4 w-4 text-gray-500 dark:text-gray-400"})}),(0,r.jsx)("button",{className:"p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200",title:"Chart settings",children:(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-500 dark:text-gray-400"})})]})]}),(0,r.jsxs)("div",{className:"mb-6 px-2",children:[(0,r.jsx)("div",{className:"flex flex-wrap items-center justify-center gap-2 sm:gap-3",children:[{key:"all",label:"All Categories",color:"bg-gray-500 hover:bg-gray-600",darkColor:"dark:bg-gray-600 dark:hover:bg-gray-700"},{key:"beverages",label:"Beverages",color:"bg-green-500 hover:bg-green-600",darkColor:"dark:bg-green-600 dark:hover:bg-green-700"},{key:"snacks",label:"Snacks",color:"bg-blue-500 hover:bg-blue-600",darkColor:"dark:bg-blue-600 dark:hover:bg-blue-700"},{key:"household",label:"Household",color:"bg-yellow-500 hover:bg-yellow-600",darkColor:"dark:bg-yellow-600 dark:hover:bg-yellow-700"},{key:"personal care",label:"Personal Care",color:"bg-red-500 hover:bg-red-600",darkColor:"dark:bg-red-600 dark:hover:bg-red-700"},{key:"others",label:"Others",color:"bg-purple-500 hover:bg-purple-600",darkColor:"dark:bg-purple-600 dark:hover:bg-purple-700"}].map(e=>(0,r.jsx)("button",{onClick:()=>g(e.key),className:"px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-white text-xs sm:text-sm font-medium transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50 shadow-md backdrop-blur-sm ".concat(e.color," ").concat(e.darkColor," ").concat(x===e.key?"ring-2 ring-white dark:ring-gray-300 ring-opacity-50 scale-105 shadow-lg":"opacity-90 hover:opacity-100"),style:{boxShadow:x===e.key?"0 8px 16px rgba(0, 0, 0, 0.25), 0 0 0 2px rgba(255, 255, 255, 0.4)":"0 4px 8px rgba(0, 0, 0, 0.15)",minWidth:f?"80px":"100px"},children:(0,r.jsx)("span",{className:"block truncate",children:e.label})},e.key))}),(0,r.jsx)("div",{className:"text-center mt-3",children:(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Click on a category to filter the chart data"})})]}),u?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):(0,r.jsx)("div",{className:"transition-all duration-500 ease-in-out",children:(0,r.jsx)(q.A,{option:C,style:{height:f?"400px":"500px"},opts:{renderer:"svg"}})})]}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg",children:(0,r.jsx)(z.A,{className:"h-5 w-5 text-emerald-600 dark:text-emerald-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Performance Gauge"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Overall business efficiency"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Real-time"})]})})]}),u?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading gauge data..."})]})}):(0,r.jsx)(q.A,{option:D,style:{height:f?"300px":"400px"}})]})]}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg",children:(0,r.jsx)(_.A,{className:"h-5 w-5 text-purple-600 dark:text-purple-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Sales Activity Heatmap"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Hourly sales patterns throughout the week"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Pattern Analysis"})]})})]}),u?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading heatmap data..."})]})}):(0,r.jsx)(q.A,{option:A,style:{height:f?"400px":"500px"}})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Predictive Insights"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:"AI Powered"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(G.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-300",children:"Revenue Forecast"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-400",children:"Expected 15% growth next month based on current trends"}),(0,r.jsx)("div",{className:"mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"75%"}})})]}),(0,r.jsxs)("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-300",children:"Customer Growth"})]}),(0,r.jsx)("p",{className:"text-xs text-green-700 dark:text-green-400",children:"New customer acquisition rate increasing by 8%"}),(0,r.jsx)("div",{className:"mt-2 w-full bg-green-200 dark:bg-green-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"68%"}})})]}),(0,r.jsxs)("div",{className:"p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-300",children:"Inventory Alert"})]}),(0,r.jsx)("p",{className:"text-xs text-yellow-700 dark:text-yellow-400",children:"3 products predicted to run low stock within 5 days"}),(0,r.jsx)("div",{className:"mt-2 w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-yellow-600 h-2 rounded-full",style:{width:"30%"}})})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"System Status"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400 font-medium",children:"Online"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Data Streaming"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(Z.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400",children:"Active"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(R.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"API Response"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:"~250ms"})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(P.A,{className:"h-4 w-4 text-yellow-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Last Update"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:b.toLocaleTimeString()})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(z.A,{className:"h-4 w-4 text-purple-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Data Quality"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"text-sm text-purple-600 dark:text-purple-400",children:"98.5%"})})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Performance"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(z.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:"Optimized"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Chart Rendering"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"95%"}})}),(0,r.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400",children:"95%"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Data Processing"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"88%"}})}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:"88%"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Memory Usage"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full",style:{width:"72%"}})}),(0,r.jsx)("span",{className:"text-sm text-yellow-600 dark:text-yellow-400",children:"72%"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg",children:(0,r.jsx)(G.A,{className:"h-5 w-5 text-indigo-600 dark:text-indigo-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Business Intelligence Summary"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Key insights and recommendations"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-indigo-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Auto-generated"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(Z.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-300",children:"Strong Performance"})]}),(0,r.jsx)("p",{className:"text-xs text-green-700 dark:text-green-400",children:"Revenue growth is exceeding targets by 12%. Continue current marketing strategies."})]}),(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-300",children:"Customer Retention"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-400",children:"Customer loyalty programs showing positive impact. 85% retention rate achieved."})]}),(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-300",children:"Inventory Optimization"})]}),(0,r.jsx)("p",{className:"text-xs text-yellow-700 dark:text-yellow-400",children:"Consider increasing stock for high-demand items. Seasonal patterns identified."})]}),(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(z.A,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-purple-800 dark:text-purple-300",children:"Efficiency Gains"})]}),(0,r.jsx)("p",{className:"text-xs text-purple-700 dark:text-purple-400",children:"Operational efficiency improved by 15%. Focus on peak hour optimization."})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-800 dark:text-gray-300",children:"Next Actions"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 dark:text-gray-400",children:"Expand successful product lines"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 dark:text-gray-400",children:"Implement customer feedback system"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 dark:text-gray-400",children:"Optimize inventory turnover"})]})]})]})]})]})}var $=t(4416),ee=t(6474),ea=t(9145),et=t(8979),er=t(2525),es=t(5213),el=t(5488),en=t(4653),eo=t(5968),ei=t(4481),ed=t(3717),ec=t(6766),em=t(5647),ex=t(8663),eg=t(4028),eu=t(9509);let eh=ex.Ik({NODE_ENV:ex.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:ex.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:ex.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:ex.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:ex.Yj().optional(),CLOUDINARY_API_KEY:ex.Yj().optional(),CLOUDINARY_API_SECRET:ex.Yj().optional(),GEMINI_API_KEY:ex.Yj().optional(),NEXTAUTH_SECRET:ex.Yj().optional(),NEXTAUTH_URL:ex.Yj().optional(),DEBUG:ex.Yj().transform(e=>"true"===e).default("false")}),eb=function(){try{return eh.parse(eu.env)}catch(e){if(e instanceof eg.G){let a=e.errors.map(e=>"".concat(e.path.join("."),": ").concat(e.message));throw Error("❌ Invalid environment variables:\n".concat(a.join("\n"),"\n\n")+"Please check your .env.local file and ensure all required variables are set.\nSee .env.example for reference.")}throw e}}(),ep={isDevelopment:"development"===eb.NODE_ENV,isProduction:"production"===eb.NODE_ENV,isTest:"test"===eb.NODE_ENV,database:{url:eb.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:eb.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:eb.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:eb.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:eb.CLOUDINARY_API_KEY,apiSecret:eb.CLOUDINARY_API_SECRET},ai:{geminiApiKey:eb.GEMINI_API_KEY},auth:{secret:eb.NEXTAUTH_SECRET,url:eb.NEXTAUTH_URL},debug:eb.DEBUG},{NODE_ENV:ef,NEXT_PUBLIC_SUPABASE_URL:ey,NEXT_PUBLIC_SUPABASE_ANON_KEY:ej,SUPABASE_SERVICE_ROLE_KEY:ev,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:ek,CLOUDINARY_API_KEY:eN,CLOUDINARY_API_SECRET:ew,NEXTAUTH_SECRET:eC,NEXTAUTH_URL:eA,DEBUG:eS,GEMINI_API_KEY:eD}=eb;(0,em.UU)(ep.database.url,ep.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}});let e_=["Snacks","Canned Goods","Beverages","Personal Care","Household Items","Condiments","Rice & Grains","Instant Foods","Dairy Products","Others"];function eP(e){let{type:a,count:t=6}=e,{resolvedTheme:l}=(0,s.D)(),n={backgroundColor:"dark"===l?"#374151":"#f3f4f6",backgroundImage:"dark"===l?"linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)":"linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)",backgroundSize:"200% 100%",animation:"shimmer 2s infinite"},o=()=>(0,r.jsxs)("div",{className:"rounded-lg shadow-md overflow-hidden",style:{backgroundColor:"dark"===l?"#1e293b":"#ffffff",border:"dark"===l?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,r.jsx)("div",{className:"aspect-square",style:n}),(0,r.jsxs)("div",{className:"p-4 space-y-3",children:[(0,r.jsx)("div",{className:"h-5 rounded",style:n}),(0,r.jsx)("div",{className:"h-4 w-2/3 rounded",style:n}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-6 w-20 rounded",style:n}),(0,r.jsx)("div",{className:"h-4 w-16 rounded",style:n})]}),(0,r.jsx)("div",{className:"h-4 w-24 rounded",style:n}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("div",{className:"flex-1 h-10 rounded",style:n}),(0,r.jsx)("div",{className:"flex-1 h-10 rounded",style:n})]})]})]}),i=()=>(0,r.jsxs)("div",{className:"rounded-lg shadow-md overflow-hidden",style:{backgroundColor:"dark"===l?"#1e293b":"#ffffff",border:"dark"===l?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-6 w-48 rounded",style:n}),(0,r.jsx)("div",{className:"h-4 w-32 rounded",style:n})]}),(0,r.jsx)("div",{className:"h-8 w-24 rounded",style:n})]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:[void 0,void 0].map((e,a)=>(0,r.jsx)("div",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-5 w-40 rounded",style:n}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"h-4 w-56 rounded",style:n}),(0,r.jsx)("div",{className:"h-4 w-48 rounded",style:n})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("div",{className:"h-6 w-20 rounded",style:n}),(0,r.jsx)("div",{className:"h-8 w-8 rounded",style:n}),(0,r.jsx)("div",{className:"h-8 w-8 rounded",style:n})]})]})},a))})]});return"products"===a?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[...Array(t)].map((e,a)=>(0,r.jsx)(o,{},a))}):"debts"===a?(0,r.jsx)("div",{className:"space-y-6",children:[...Array(t)].map((e,a)=>(0,r.jsx)(i,{},a))}):null}var eM=t(9869);function eL(e){let{isOpen:a,onClose:t,product:s}=e,[n,i]=(0,l.useState)({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),[d,c]=(0,l.useState)(null),[m,x]=(0,l.useState)(""),[g,u]=(0,l.useState)(!1),[h,b]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s?(i({name:s.name,net_weight:s.net_weight,price:s.price.toString(),stock_quantity:s.stock_quantity.toString(),category:s.category,image_url:s.image_url||""}),x(s.image_url||"")):(i({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),x("")),c(null)},[s,a]);let p=async()=>{if(!d)return n.image_url;b(!0);try{let e=new FormData;e.append("file",d);let a=await fetch("/api/upload",{method:"POST",body:e});return(await a.json()).url}catch(e){return console.error("Error uploading image:",e),n.image_url}finally{b(!1)}},f=async e=>{e.preventDefault(),u(!0);try{let e=await p(),a={...n,image_url:e,price:parseFloat(n.price),stock_quantity:parseInt(n.stock_quantity)},r=s?"/api/products/".concat(s.id):"/api/products",l=s?"PUT":"POST";(await fetch(r,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok?t():console.error("Error saving product")}catch(e){console.error("Error saving product:",e)}finally{u(!1)}};return a?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:s?"Edit Product in List":"Add Product to List"}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)($.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Image"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2",children:m?(0,r.jsx)(ec.default,{src:m,alt:"Preview",width:128,height:128,className:"w-full h-full object-cover rounded-lg"}):(0,r.jsx)(o.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){c(t);let e=new FileReader;e.onloadend=()=>{x(e.result)},e.readAsDataURL(t)}},className:"hidden",id:"image-upload"}),(0,r.jsxs)("label",{htmlFor:"image-upload",className:"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50",children:[(0,r.jsx)(eM.A,{className:"h-4 w-4 mr-2"}),"Choose Image"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:n.name,onChange:e=>i({...n,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Net Weight *"}),(0,r.jsx)("input",{type:"text",required:!0,placeholder:"e.g., 100g, 1L, 250ml",value:n.net_weight,onChange:e=>i({...n,net_weight:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:n.price,onChange:e=>i({...n,price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Stock Quantity *"}),(0,r.jsx)("input",{type:"number",min:"0",required:!0,value:n.stock_quantity,onChange:e=>i({...n,stock_quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category *"}),(0,r.jsxs)("select",{required:!0,value:n.category,onChange:e=>i({...n,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Select Category"}),e_.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:g||h,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:g||h?"Saving...":s?"Update in List":"Add to List"})]})]})]})}):null}var eT=t(4357),eR=t(8564),eE=t(4020);function eI(e){let{product:a,onEdit:t,onDelete:n,onDuplicate:o,onToggleFavorite:i,className:d=""}=e,{resolvedTheme:c}=(0,s.D)(),[m,x]=(0,l.useState)(!1),g=(0,l.useRef)(null);(0,l.useEffect)(()=>{let e=e=>{g.current&&!g.current.contains(e.target)&&x(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let u=e=>{e(),x(!1)},h=[{label:"Edit Product",icon:ed.A,action:()=>t(a),color:"text-blue-600"},{label:"View Details",icon:X.A,action:()=>console.log("View details:",a.id),color:"text-gray-600"},{label:"Duplicate",icon:eT.A,action:()=>null==o?void 0:o(a),color:"text-green-600"},{label:"Add to Favorites",icon:eR.A,action:()=>null==i?void 0:i(a.id),color:"text-yellow-600"},{label:"View Analytics",icon:G.A,action:()=>console.log("View analytics:",a.id),color:"text-purple-600"},{label:"Export Data",icon:W.A,action:()=>console.log("Export data:",a.id),color:"text-indigo-600"},{label:"Delete Product",icon:er.A,action:()=>n(a.id),color:"text-red-600",separator:!0}];return(0,r.jsxs)("div",{className:"relative ".concat(d),ref:g,children:[(0,r.jsx)("button",{onClick:()=>x(!m),className:"p-1.5 rounded-md bg-white/90 text-gray-600 hover:bg-white shadow-sm transition-all duration-200 hover:shadow-md",title:"Quick Actions",children:(0,r.jsx)(eE.A,{className:"h-4 w-4"})}),m&&(0,r.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 rounded-lg shadow-lg border z-50 py-1 animate-slide-down",style:{backgroundColor:"dark"===c?"#374151":"#ffffff",border:"dark"===c?"1px solid #6b7280":"1px solid #e5e7eb"},children:h.map((e,a)=>(0,r.jsxs)("div",{children:[e.separator&&(0,r.jsx)("div",{className:"my-1 h-px",style:{backgroundColor:"dark"===c?"#6b7280":"#e5e7eb"}}),(0,r.jsxs)("button",{onClick:()=>u(e.action),className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===c?"#f9fafb":"#111827"},children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 ".concat(e.color)}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]})]},a))})]})}function eF(e){let{imageUrl:a,productName:t,isOpen:n,onClose:i}=e,{resolvedTheme:d}=(0,s.D)(),[c,m]=(0,l.useState)(!1);return n?(0,r.jsxs)("div",{className:"fixed inset-0 bg-black/80 flex items-center justify-center z-[100] p-4",children:[(0,r.jsx)("button",{onClick:i,className:"absolute top-4 right-4 p-2 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors z-10",title:"Close",children:(0,r.jsx)($.A,{className:"h-6 w-6"})}),(0,r.jsx)("div",{className:"relative max-w-4xl max-h-full w-full h-full flex items-center justify-center",children:a?(0,r.jsxs)("div",{className:"relative",children:[!c&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white"})}),(0,r.jsx)(ec.default,{src:a,alt:t,width:800,height:800,className:"max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-opacity duration-300 ".concat(c?"opacity-100":"opacity-0"),onLoad:()=>m(!0),priority:!0})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center p-12 rounded-lg",style:{backgroundColor:"dark"===d?"#374151":"#f3f4f6"},children:[(0,r.jsx)(o.A,{className:"h-24 w-24 text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-500 text-lg font-medium",children:"No image available"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-2",children:t})]})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 text-center",children:(0,r.jsx)("div",{className:"bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 inline-block",children:(0,r.jsx)("h3",{className:"text-white font-semibold text-lg",children:t})})})]}):null}function ez(e,a,t){let r=new Blob([e],{type:t}),s=URL.createObjectURL(r),l=document.createElement("a");l.href=s,l.download=a,l.style.display="none",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(s)}function eO(e){let{onStatsUpdate:a}=e,{resolvedTheme:t}=(0,s.D)(),[n,i]=(0,l.useState)([]),[d,m]=(0,l.useState)(!0),[x,g]=(0,l.useState)(""),[u,h]=(0,l.useState)(!1),[b,p]=(0,l.useState)(null),[f,y]=(0,l.useState)("grid"),[v,k]=(0,l.useState)("created_at"),[N,w]=(0,l.useState)("desc"),[C,A]=(0,l.useState)(new Set),[S,_]=(0,l.useState)(!1),[P,L]=(0,l.useState)({category:"",priceRange:{min:0,max:1e4},stockRange:{min:0,max:1e3},lowStock:!1,outOfStock:!1}),[T,R]=(0,l.useState)([]),[I,F]=(0,l.useState)(!1),{zoomImage:z,openZoom:O,closeZoom:U,isZoomOpen:B}=function(){let[e,a]=(0,l.useState)(null);return{zoomImage:e,openZoom:(e,t)=>{a({imageUrl:e,productName:t})},closeZoom:()=>{a(null)},isZoomOpen:!!e}}();(0,l.useEffect)(()=>{Y()},[]);let q=(0,l.useCallback)(e=>{if(!e||e.length<2)return void R([]);R([...new Set(n.filter(a=>a.name.toLowerCase().includes(e.toLowerCase())||a.category.toLowerCase().includes(e.toLowerCase())).map(e=>e.name).slice(0,5))])},[n]),H=(0,l.useMemo)(()=>{let e=n.filter(e=>{let a=!x||e.name.toLowerCase().includes(x.toLowerCase())||e.category.toLowerCase().includes(x.toLowerCase()),t=!P.category||e.category===P.category,r=e.price>=P.priceRange.min&&e.price<=P.priceRange.max,s=e.stock_quantity>=P.stockRange.min&&e.stock_quantity<=P.stockRange.max,l=!P.lowStock||e.stock_quantity<10,n=!P.outOfStock||0===e.stock_quantity;return a&&t&&r&&s&&l&&n});return e.sort((e,a)=>{let t,r;switch(v){case"name":t=e.name.toLowerCase(),r=a.name.toLowerCase();break;case"price":t=e.price,r=a.price;break;case"stock":t=e.stock_quantity,r=a.stock_quantity;break;case"category":t=e.category.toLowerCase(),r=a.category.toLowerCase();break;case"created_at":t=new Date(e.created_at).getTime(),r=new Date(a.created_at).getTime();break;default:return 0}return t<r?"asc"===N?-1:1:t>r?"asc"===N?1:-1:0}),e},[n,x,P,v,N]),Y=async()=>{try{console.warn("\uD83D\uDD04 Fetching products...");let e=await fetch("/api/products");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let a=await e.json();console.warn("\uD83D\uDCE6 Products API response:",a),a.success&&a.data&&a.data.products?(i(a.data.products),console.warn("✅ Products loaded (new structure):",a.data.products.length,"items")):a.products?(i(a.products),console.warn("✅ Products loaded (old structure):",a.products.length,"items")):Array.isArray(a)?(i(a),console.warn("✅ Products loaded (direct array):",a.length,"items")):(console.warn("⚠️ Unexpected API response structure:",a),i([]))}catch(e){console.error("❌ Error fetching products:",e),i([])}finally{m(!1)}},K=async e=>{if(confirm("Are you sure you want to delete this product?"))try{(await fetch("/api/products/".concat(e),{method:"DELETE"})).ok&&(i(n.filter(a=>a.id!==e)),a())}catch(e){console.error("Error deleting product:",e)}},Z=e=>{p(e),h(!0)},Q=e=>{let a=new Set(C);a.has(e)?a.delete(e):a.add(e),A(a)},em=async()=>{if(0!==C.size&&confirm("Are you sure you want to delete ".concat(C.size," product(s)?")))try{let e=Array.from(C).map(e=>fetch("/api/products/".concat(e),{method:"DELETE"}));await Promise.all(e),A(new Set),Y(),a()}catch(e){console.error("Error deleting products:",e)}},ex=e=>{g(e),q(e),F(e.length>=2)},eg=e=>{g(e),F(!1)};return d?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("div",{className:"w-64 h-10 rounded-lg",style:{backgroundColor:"dark"===t?"#374151":"#f3f4f6",backgroundImage:"dark"===t?"linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)":"linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)",backgroundSize:"200% 100%",animation:"shimmer 2s infinite"}}),(0,r.jsx)("div",{className:"w-40 h-10 rounded-lg",style:{backgroundColor:"dark"===t?"#374151":"#f3f4f6",backgroundImage:"dark"===t?"linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)":"linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)",backgroundSize:"200% 100%",animation:"shimmer 2s infinite"}})]}),(0,r.jsx)("div",{className:"w-48 h-10 rounded-lg",style:{backgroundColor:"dark"===t?"#374151":"#f3f4f6",backgroundImage:"dark"===t?"linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)":"linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)",backgroundSize:"200% 100%",animation:"shimmer 2s infinite"}})]}),(0,r.jsx)(eP,{type:"products",count:8})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,r.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300",style:{color:"dark"===t?"#9ca3af":"#6b7280"}}),(0,r.jsx)("input",{type:"text",placeholder:"Search products by name or category...",value:x,onChange:e=>ex(e.target.value),onFocus:()=>F(x.length>=2),onBlur:()=>setTimeout(()=>F(!1),200),className:"w-full pl-10 pr-4 py-2.5 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-sm",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}}),I&&T.length>0&&(0,r.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg border z-50 max-h-48 overflow-y-auto",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db"},children:T.map((e,a)=>(0,r.jsx)("button",{onClick:()=>eg(e),className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",style:{color:"dark"===t?"#f9fafb":"#111827"},children:e},a))})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>L(e=>({...e,lowStock:!e.lowStock})),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ".concat(P.lowStock?"bg-orange-100 text-orange-700 border-orange-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)(D.A,{className:"h-4 w-4 inline mr-1"}),"Low Stock"]}),(0,r.jsxs)("button",{onClick:()=>L(e=>({...e,outOfStock:!e.outOfStock})),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ".concat(P.outOfStock?"bg-red-100 text-red-700 border-red-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)($.A,{className:"h-4 w-4 inline mr-1"}),"Out of Stock"]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>_(!S),className:"px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm ".concat(S?"bg-green-100 text-green-700 border-green-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)(V.A,{className:"h-4 w-4 inline mr-2"}),"Filters"]}),H.length>0&&(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("button",{className:"px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 border",children:[(0,r.jsx)(W.A,{className:"h-4 w-4 inline mr-2"}),"Export",(0,r.jsx)(ee.A,{className:"h-4 w-4 inline ml-1"})]}),(0,r.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",children:(0,r.jsxs)("div",{className:"rounded-lg shadow-lg border py-1",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #e5e7eb"},children:[(0,r.jsxs)("button",{onClick:()=>{!function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"products";ez(["ID,Name,Category,Price,Stock Quantity,Net Weight,Image URL,Created At,Updated At",...e.map(e=>[e.id,'"'.concat(e.name.replace(/"/g,'""'),'"'),'"'.concat(e.category,'"'),e.price,e.stock_quantity,'"'.concat(e.net_weight,'"'),e.image_url||"",e.created_at,e.updated_at].join(","))].join("\n"),"".concat(a,".csv"),"text/csv")}(H,"products_".concat(new Date().toISOString().split("T")[0]))},className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===t?"#f9fafb":"#111827"},children:[(0,r.jsx)(W.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Export as CSV"})]}),(0,r.jsxs)("button",{onClick:()=>{!function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"products";ez(JSON.stringify(e,null,2),"".concat(a,".json"),"application/json")}(H,"products_".concat(new Date().toISOString().split("T")[0]))},className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===t?"#f9fafb":"#111827"},children:[(0,r.jsx)(W.A,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Export as JSON"})]}),(0,r.jsx)("div",{className:"my-1 h-px",style:{backgroundColor:"dark"===t?"#6b7280":"#e5e7eb"}}),(0,r.jsxs)("button",{onClick:()=>{!function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"product_analytics";ez(JSON.stringify(function(e){let a=e.length,t=e.reduce((e,a)=>e+a.price*a.stock_quantity,0),r=e.filter(e=>e.stock_quantity<10).length,s=e.filter(e=>0===e.stock_quantity).length,l=e.reduce((e,a)=>(e[a.category]||(e[a.category]={count:0,totalValue:0,averagePrice:0,totalStock:0}),e[a.category].count++,e[a.category].totalValue+=a.price*a.stock_quantity,e[a.category].totalStock+=a.stock_quantity,e),{});return Object.keys(l).forEach(a=>{let t=e.filter(e=>e.category===a);l[a].averagePrice=t.reduce((e,a)=>e+a.price,0)/t.length}),{summary:{totalProducts:a,totalValue:t.toFixed(2),lowStockItems:r,outOfStockItems:s,averageProductValue:(t/a).toFixed(2)},categoryBreakdown:l,generatedAt:new Date().toISOString()}}(e),null,2),"".concat(a,".json"),"application/json")}(H,"product_analytics_".concat(new Date().toISOString().split("T")[0]))},className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===t?"#f9fafb":"#111827"},children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-purple-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Analytics Report"})]})]})})]}),(0,r.jsxs)("button",{onClick:()=>h(!0),className:"flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})]})]}),S&&(0,r.jsx)("div",{className:"p-6 rounded-xl border shadow-sm animate-slide-down",style:{backgroundColor:"dark"===t?"#374151":"#f8fafc",border:"dark"===t?"1px solid #6b7280":"1px solid #e2e8f0"},children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===t?"#f9fafb":"#111827"},children:"Category"}),(0,r.jsxs)("select",{value:P.category,onChange:e=>L(a=>({...a,category:e.target.value})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"},children:[(0,r.jsx)("option",{value:"",children:"All Categories"}),e_.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===t?"#f9fafb":"#111827"},children:"Price Range (₱)"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"number",placeholder:"Min",value:P.priceRange.min,onChange:e=>L(a=>({...a,priceRange:{...a.priceRange,min:Number(e.target.value)||0}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}}),(0,r.jsx)("input",{type:"number",placeholder:"Max",value:P.priceRange.max,onChange:e=>L(a=>({...a,priceRange:{...a.priceRange,max:Number(e.target.value)||1e4}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===t?"#f9fafb":"#111827"},children:"Stock Range"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"number",placeholder:"Min",value:P.stockRange.min,onChange:e=>L(a=>({...a,stockRange:{...a.stockRange,min:Number(e.target.value)||0}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}}),(0,r.jsx)("input",{type:"number",placeholder:"Max",value:P.stockRange.max,onChange:e=>L(a=>({...a,stockRange:{...a.stockRange,max:Number(e.target.value)||1e3}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("button",{onClick:()=>L({category:"",priceRange:{min:0,max:1e4},stockRange:{min:0,max:1e3},lowStock:!1,outOfStock:!1}),className:"w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 inline mr-2"}),"Reset Filters"]})})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{className:"text-sm font-medium",style:{color:"dark"===t?"#cbd5e1":"#6b7280"},children:[H.length," product",1!==H.length?"s":""," found"]}),H.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>{C.size===H.length?A(new Set):A(new Set(H.map(e=>e.id)))},className:"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:C.size===H.length?"Deselect All":"Select All",children:C.size===H.length?(0,r.jsx)(ea.A,{className:"h-4 w-4 text-green-600"}):C.size>0?(0,r.jsx)(J.A,{className:"h-4 w-4 text-gray-600"}):(0,r.jsx)(et.A,{className:"h-4 w-4 text-gray-600"})}),C.size>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[C.size," selected"]}),(0,r.jsxs)("button",{onClick:em,className:"px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium",children:[(0,r.jsx)(er.A,{className:"h-3 w-3 inline mr-1"}),"Delete"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",style:{color:"dark"===t?"#cbd5e1":"#6b7280"},children:"Sort by:"}),(0,r.jsxs)("select",{value:v,onChange:e=>k(e.target.value),className:"px-3 py-1.5 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"},children:[(0,r.jsx)("option",{value:"created_at",children:"Date Added"}),(0,r.jsx)("option",{value:"name",children:"Name"}),(0,r.jsx)("option",{value:"price",children:"Price"}),(0,r.jsx)("option",{value:"stock",children:"Stock"}),(0,r.jsx)("option",{value:"category",children:"Category"})]}),(0,r.jsx)("button",{onClick:()=>w("asc"===N?"desc":"asc"),className:"p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"Sort ".concat("asc"===N?"Descending":"Ascending"),children:"asc"===N?(0,r.jsx)(es.A,{className:"h-4 w-4",style:{color:"dark"===t?"#cbd5e1":"#6b7280"}}):(0,r.jsx)(el.A,{className:"h-4 w-4",style:{color:"dark"===t?"#cbd5e1":"#6b7280"}})})]}),(0,r.jsxs)("div",{className:"flex rounded-lg border",style:{border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db"},children:[(0,r.jsx)("button",{onClick:()=>y("grid"),className:"p-2 transition-colors ".concat("grid"===f?"bg-green-100 text-green-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),title:"Grid View",children:(0,r.jsx)(en.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>y("list"),className:"p-2 transition-colors ".concat("list"===f?"bg-green-100 text-green-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),title:"List View",children:(0,r.jsx)(eo.A,{className:"h-4 w-4"})})]})]})]})]}),(0,r.jsx)("div",{className:"grid"===f?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6":"space-y-4",children:H.map(e=>(0,r.jsxs)("div",{className:"relative group rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl ".concat(C.has(e.id)?"ring-2 ring-green-500 ring-offset-2":"hover:scale-[1.02]"," ").concat("list"===f?"flex items-center":""),style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,r.jsx)("div",{className:"absolute top-3 left-3 z-10",children:(0,r.jsx)("button",{onClick:()=>Q(e.id),className:"p-1 rounded-md transition-all duration-200 ".concat(C.has(e.id)?"bg-green-500 text-white":"bg-white/80 text-gray-600 hover:bg-white"," shadow-sm"),children:C.has(e.id)?(0,r.jsx)(ea.A,{className:"h-4 w-4"}):(0,r.jsx)(et.A,{className:"h-4 w-4"})})}),(0,r.jsx)("div",{className:"absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)(eI,{product:e,onEdit:Z,onDelete:K,onDuplicate:e=>{p({...e,id:"",name:"".concat(e.name," (Copy)"),created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),h(!0)}})}),(0,r.jsxs)("div",{className:"".concat("grid"===f?"aspect-square":"w-24 h-24 flex-shrink-0"," flex items-center justify-center transition-colors duration-300 relative overflow-hidden cursor-pointer group/image"),style:{backgroundColor:"dark"===t?"#374151":"#f3f4f6"},onClick:()=>O(e.image_url,e.name),children:[e.image_url?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(ec.default,{src:e.image_url,alt:e.name,width:"grid"===f?200:96,height:"grid"===f?200:96,className:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20 opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 flex items-center justify-center",children:(0,r.jsx)("div",{className:"bg-white/90 rounded-full p-2",children:(0,r.jsx)(ei.A,{className:"h-4 w-4 text-gray-700"})})})]}):(0,r.jsx)(o.A,{className:"".concat("grid"===f?"h-16 w-16":"h-12 w-12"," transition-colors duration-300"),style:{color:"dark"===t?"#9ca3af":"#6b7280"}}),0===e.stock_quantity&&(0,r.jsx)("div",{className:"absolute inset-0 bg-red-500/20 flex items-center justify-center",children:(0,r.jsx)("span",{className:"bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium",children:"Out of Stock"})}),e.stock_quantity>0&&e.stock_quantity<10&&(0,r.jsx)("div",{className:"absolute top-2 right-2",children:(0,r.jsx)("span",{className:"bg-orange-500 text-white px-2 py-1 rounded-md text-xs font-medium",children:"Low Stock"})})]}),(0,r.jsx)("div",{className:"".concat("grid"===f?"p-4":"flex-1 p-4"),children:(0,r.jsxs)("div",{className:"".concat("list"===f?"flex items-center justify-between":""),children:[(0,r.jsxs)("div",{className:"".concat("list"===f?"flex-1":""),children:[(0,r.jsx)("h3",{className:"font-semibold mb-1 transition-colors duration-300 line-clamp-2",style:{color:"dark"===t?"#f8fafc":"#111827"},children:e.name}),(0,r.jsxs)("div",{className:"".concat("list"===f?"flex items-center gap-4":"space-y-2"),children:[(0,r.jsx)("p",{className:"text-sm transition-colors duration-300",style:{color:"dark"===t?"#cbd5e1":"#6b7280"},children:e.category}),(0,r.jsxs)("div",{className:"".concat("list"===f?"flex items-center gap-4":"flex justify-between items-center"),children:[(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",e.price.toFixed(2)]}),(0,r.jsx)("span",{className:"text-sm transition-colors duration-300",style:{color:"dark"===t?"#9ca3af":"#6b7280"},children:e.net_weight})]}),(0,r.jsxs)("div",{className:"".concat("list"===f?"flex items-center gap-2":"flex justify-between items-center mb-4"),children:[(0,r.jsxs)("span",{className:"text-sm ".concat(0===e.stock_quantity?"text-red-600 font-medium":e.stock_quantity<10?"text-orange-600 font-medium":""),style:{color:e.stock_quantity>=10?"dark"===t?"#cbd5e1":"#6b7280":void 0},children:["Stock: ",e.stock_quantity]}),"grid"===f&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[e.stock_quantity<10&&(0,r.jsx)(D.A,{className:"h-4 w-4 text-orange-500"}),(0,r.jsx)(G.A,{className:"h-4 w-4 text-green-500"})]})]})]})]}),(0,r.jsxs)("div",{className:"".concat("list"===f?"flex gap-2 ml-4":"flex gap-2"),children:[(0,r.jsxs)("button",{onClick:()=>Z(e),className:"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-300 hover:scale-105 font-medium",title:"Edit Product",children:[(0,r.jsx)(ed.A,{className:"h-4 w-4 mr-1"}),"grid"===f?"Edit":""]}),(0,r.jsxs)("button",{onClick:()=>K(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-300 hover:scale-105 font-medium",title:"Delete Product",children:[(0,r.jsx)(er.A,{className:"h-4 w-4 mr-1"}),"grid"===f?"Delete":""]}),"list"===f&&(0,r.jsx)("button",{className:"px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 hover:scale-105 font-medium",title:"View Details",children:(0,r.jsx)(X.A,{className:"h-4 w-4"})})]})]})})]},e.id))}),0===H.length&&!d&&(0,r.jsxs)("div",{className:"text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300",style:{backgroundColor:"dark"===t?"rgba(30, 41, 59, 0.5)":"rgba(249, 250, 251, 0.8)",borderColor:"dark"===t?"#475569":"#d1d5db"},children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300",style:{backgroundColor:"dark"===t?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.05)",border:"dark"===t?"2px solid rgba(34, 197, 94, 0.3)":"2px solid rgba(34, 197, 94, 0.2)"},children:(0,r.jsx)(o.A,{className:"h-10 w-10 transition-colors duration-300",style:{color:"dark"===t?"#4ade80":"#16a34a"}})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 transition-colors duration-300",style:{color:"dark"===t?"#f8fafc":"#111827"},children:x||P.category||P.lowStock||P.outOfStock?"No products found":"No products in your inventory"}),(0,r.jsx)("p",{className:"text-sm mb-6 max-w-md mx-auto transition-colors duration-300",style:{color:"dark"===t?"#94a3b8":"#6b7280"},children:x||P.category||P.lowStock||P.outOfStock?"Try adjusting your search terms or filter criteria to find what you're looking for":"Get started by adding your first product to build your inventory and start managing your store"}),!x&&!P.category&&!P.lowStock&&!P.outOfStock&&(0,r.jsxs)("button",{onClick:()=>h(!0),className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl",children:[(0,r.jsx)(E.A,{className:"h-5 w-5 mr-2"}),"Add Your First Product"]})]}),(0,r.jsx)(eL,{isOpen:u,onClose:()=>{h(!1),p(null),Y(),a()},product:b}),(0,r.jsx)(eF,{imageUrl:null==z?void 0:z.imageUrl,productName:(null==z?void 0:z.productName)||"",isOpen:B,onClose:U})]})}var eU=t(6736);let eB={sm:"w-8 h-8",md:"w-12 h-12",lg:"w-16 h-16",xl:"w-24 h-24"},eq={sm:"h-3 w-3",md:"h-4 w-4",lg:"h-6 w-6",xl:"h-8 w-8"},eH={sm:"w-5 h-5",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-10 h-10"},eG={sm:"h-2 w-2",md:"h-3 w-3",lg:"h-3 w-3",xl:"h-4 w-4"};function eV(e){let{profilePictureUrl:a,customerName:t,customerFamilyName:s,size:n="md",showEditButton:o=!1,onEditClick:i,className:d=""}=e,[c,m]=(0,l.useState)(!1),x=()=>{let e=t.charAt(0).toUpperCase(),a=s.charAt(0).toUpperCase();return"".concat(e).concat(a)},u=a&&!c;return(0,r.jsxs)("div",{className:"relative inline-block ".concat(d),children:[(0,r.jsx)("div",{className:"".concat(eB[n]," rounded-full overflow-hidden border-2 border-white shadow-md transition-all duration-300 hover:shadow-lg"),style:{backgroundColor:u?"transparent":void 0},children:u?(0,r.jsx)(ec.default,{src:a,alt:"".concat(t," ").concat(s),width:"xl"===n?96:"lg"===n?64:"md"===n?48:32,height:"xl"===n?96:"lg"===n?64:"md"===n?48:32,className:"w-full h-full object-cover",onError:()=>m(!0)}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center text-white font-semibold ".concat((()=>{let e=["bg-red-500","bg-blue-500","bg-green-500","bg-yellow-500","bg-purple-500","bg-pink-500","bg-indigo-500","bg-teal-500","bg-orange-500","bg-cyan-500"];return e[Math.abs((t+s).split("").reduce((e,a)=>a.charCodeAt(0)+((e<<5)-e),0))%e.length]})()),children:x()?(0,r.jsx)("span",{className:"".concat("xl"===n?"text-2xl":"lg"===n?"text-lg":"md"===n?"text-sm":"text-xs"),children:x()}):(0,r.jsx)(g.A,{className:eq[n]})})}),o&&i&&(0,r.jsx)("button",{onClick:i,className:"absolute -bottom-1 -right-1 ".concat(eH[n]," bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-all duration-200 hover:scale-110 shadow-md hover:shadow-lg flex items-center justify-center"),title:"Edit Profile Picture",children:(0,r.jsx)(eU.A,{className:eG[n]})})]})}var eW=t(9420),eY=t(4516),eK=t(7434);function eJ(e){let{isOpen:a,onClose:t,customer:n,customerName:o="",customerFamilyName:i="",onProfileUpdated:d}=e,{resolvedTheme:c}=(0,s.D)(),m=(0,l.useRef)(null),[x,u]=(0,l.useState)(!1),[h,b]=(0,l.useState)(!1),[p,f]=(0,l.useState)({customer_name:"",customer_family_name:"",profile_picture_url:"",profile_picture_public_id:"",phone_number:"",address:"",notes:""});(0,l.useEffect)(()=>{a&&(n?f({customer_name:n.customer_name,customer_family_name:n.customer_family_name,profile_picture_url:n.profile_picture_url||"",profile_picture_public_id:n.profile_picture_public_id||"",phone_number:n.phone_number||"",address:n.address||"",notes:n.notes||""}):f({customer_name:o,customer_family_name:i,profile_picture_url:"",profile_picture_public_id:"",phone_number:"",address:"",notes:""}))},[a,n,o,i]);let y=async e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(t.type))return void alert("Please select a valid image file (JPEG, PNG, or WebP)");if(t.size>5242880)return void alert("File size must be less than 5MB");b(!0);try{let e=new FormData;e.append("file",t);let a=await fetch("/api/upload/profile-picture",{method:"POST",body:e});if(a.ok){let e=await a.json();f(a=>({...a,profile_picture_url:e.url,profile_picture_public_id:e.public_id}))}else{let e=await a.json();alert("Error uploading image: ".concat(e.error||"Unknown error"))}}catch(e){console.error("Error uploading image:",e),alert("Error uploading image. Please check your connection and try again.")}finally{b(!1),e.target.value=""}}},j=async()=>{if(p.profile_picture_public_id)try{await fetch("/api/upload/profile-picture?public_id=".concat(encodeURIComponent(p.profile_picture_public_id)),{method:"DELETE"})}catch(e){console.error("Error deleting image from Cloudinary:",e)}f(e=>({...e,profile_picture_url:"",profile_picture_public_id:""}))},v=async e=>{e.preventDefault(),u(!0);try{let e=n?"/api/customers/".concat(n.id):"/api/customers",a=n?"PUT":"POST",r=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(p)});if(r.ok){let e=await r.json();d&&e.customer&&d(e.customer),console.log("Customer profile saved successfully!"),t()}else{let e=await r.json();console.error("Error saving customer profile:",e.error||"Unknown error"),alert("Error saving customer profile. Please try again.")}}catch(e){console.error("Error saving customer profile:",e),alert("Error saving customer profile. Please check your connection and try again.")}finally{u(!1)}};return a?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-lg rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto",style:{backgroundColor:"dark"===c?"#1e293b":"#ffffff"},children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",style:{borderColor:"dark"===c?"#334155":"#e5e7eb"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-blue-100",children:(0,r.jsx)(g.A,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",style:{color:"dark"===c?"#f8fafc":"#111827"},children:n?"Edit Customer Profile":"Create Customer Profile"}),(0,r.jsx)("p",{className:"text-sm",style:{color:"dark"===c?"#cbd5e1":"#6b7280"},children:"Manage customer information and profile picture"})]})]}),(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)($.A,{className:"h-5 w-5",style:{color:"dark"===c?"#cbd5e1":"#6b7280"}})})]}),(0,r.jsxs)("form",{onSubmit:v,className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[p.profile_picture_url?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg",children:(0,r.jsx)(ec.default,{src:p.profile_picture_url,alt:"Profile",width:128,height:128,className:"w-full h-full object-cover"})}),(0,r.jsx)("button",{type:"button",onClick:j,className:"absolute -top-2 -right-2 p-1.5 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg",children:(0,r.jsx)(er.A,{className:"h-3 w-3"})})]}):(0,r.jsx)("div",{className:"w-32 h-32 rounded-full border-2 border-dashed flex items-center justify-center",style:{borderColor:"dark"===c?"#6b7280":"#d1d5db",backgroundColor:"dark"===c?"#374151":"#f9fafb"},children:(0,r.jsx)(g.A,{className:"h-12 w-12",style:{color:"dark"===c?"#9ca3af":"#6b7280"}})}),(0,r.jsx)("button",{type:"button",onClick:()=>{var e;return null==(e=m.current)?void 0:e.click()},disabled:h,className:"absolute -bottom-2 -right-2 p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors shadow-lg disabled:opacity-50",children:h?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,r.jsx)(eU.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("input",{ref:m,type:"file",accept:"image/*",onChange:y,className:"hidden"}),(0,r.jsx)("p",{className:"text-sm text-center",style:{color:"dark"===c?"#9ca3af":"#6b7280"},children:"Click the camera icon to upload a profile picture"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===c?"#f9fafb":"#111827"},children:"First Name *"}),(0,r.jsx)("input",{type:"text",value:p.customer_name,onChange:e=>f({...p,customer_name:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===c?"#374151":"#ffffff",border:"dark"===c?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===c?"#f9fafb":"#111827"},required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===c?"#f9fafb":"#111827"},children:"Last Name *"}),(0,r.jsx)("input",{type:"text",value:p.customer_family_name,onChange:e=>f({...p,customer_family_name:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===c?"#374151":"#ffffff",border:"dark"===c?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===c?"#f9fafb":"#111827"},required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===c?"#f9fafb":"#111827"},children:[(0,r.jsx)(eW.A,{className:"h-4 w-4 inline mr-1"}),"Phone Number"]}),(0,r.jsx)("input",{type:"tel",value:p.phone_number,onChange:e=>f({...p,phone_number:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===c?"#374151":"#ffffff",border:"dark"===c?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===c?"#f9fafb":"#111827"},placeholder:"09XX XXX XXXX"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===c?"#f9fafb":"#111827"},children:[(0,r.jsx)(eY.A,{className:"h-4 w-4 inline mr-1"}),"Address"]}),(0,r.jsx)("textarea",{value:p.address,onChange:e=>f({...p,address:e.target.value}),rows:2,className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none",style:{backgroundColor:"dark"===c?"#374151":"#ffffff",border:"dark"===c?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===c?"#f9fafb":"#111827"},placeholder:"Customer's address..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===c?"#f9fafb":"#111827"},children:[(0,r.jsx)(eK.A,{className:"h-4 w-4 inline mr-1"}),"Notes"]}),(0,r.jsx)("textarea",{value:p.notes,onChange:e=>f({...p,notes:e.target.value}),rows:3,className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none",style:{backgroundColor:"dark"===c?"#374151":"#ffffff",border:"dark"===c?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===c?"#f9fafb":"#111827"},placeholder:"Additional notes about the customer..."})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border rounded-lg font-medium transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-700",style:{borderColor:"dark"===c?"#6b7280":"#d1d5db",color:"dark"===c?"#f9fafb":"#111827"},children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:x,className:"flex-1 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Saving...":n?"Update Profile":"Create Profile"})]})]})]})}):null}var eX=t(8449),eZ=t(2348),eQ=t(6740);function e$(e){let{isOpen:a,onClose:t,debt:s}=e,[n,o]=(0,l.useState)({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:""}),[i,d]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s?o({customer_name:s.customer_name,customer_family_name:s.customer_family_name,product_name:s.product_name,product_price:s.product_price.toString(),quantity:s.quantity.toString(),debt_date:s.debt_date}):o({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:new Date().toISOString().split("T")[0]||""})},[s,a]);let c=async e=>{e.preventDefault(),d(!0);try{let e={...n,product_price:parseFloat(n.product_price),quantity:parseInt(n.quantity)},a=s?"/api/debts/".concat(s.id):"/api/debts",r=s?"PUT":"POST";(await fetch(a,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?t():console.error("Error saving debt record")}catch(e){console.error("Error saving debt record:",e)}finally{d(!1)}};if(!a)return null;let m=n.product_price&&n.quantity?(parseFloat(n.product_price)*parseInt(n.quantity)).toFixed(2):"0.00";return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:s?"Edit Debt Record":"Add New Debt Record"}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)($.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("form",{onSubmit:c,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer First Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:n.customer_name,onChange:e=>o({...n,customer_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Juan"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer Family Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:n.customer_family_name,onChange:e=>o({...n,customer_family_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Dela Cruz"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:n.product_name,onChange:e=>o({...n,product_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Lucky Me Pancit Canton"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Price (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:n.product_price,onChange:e=>o({...n,product_price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0.00"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantity *"}),(0,r.jsx)("input",{type:"number",min:"1",required:!0,value:n.quantity,onChange:e=>o({...n,quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Debt Date *"}),(0,r.jsx)("input",{type:"date",required:!0,value:n.debt_date,onChange:e=>o({...n,debt_date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Total Amount:"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",m]})]})}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:i,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:i?"Saving...":s?"Update":"Add Record"})]})]})]})})}let e0=["Cash","GCash","PayMaya","Bank Transfer","Credit Card","Debit Card","Other"];function e1(e){let{isOpen:a,onClose:t,customerName:n="",customerFamilyName:o="",currentBalance:i=0,payment:d,profilePictureUrl:c}=e,{resolvedTheme:m}=(0,s.D)(),[x,g]=(0,l.useState)(!1),[u,h]=(0,l.useState)({customer_name:"",customer_family_name:"",payment_amount:"",payment_date:new Date().toISOString().split("T")[0],payment_method:"Cash",notes:""});(0,l.useEffect)(()=>{a&&(d?h({customer_name:d.customer_name,customer_family_name:d.customer_family_name,payment_amount:d.payment_amount.toString(),payment_date:d.payment_date,payment_method:d.payment_method,notes:d.notes||""}):h({customer_name:n,customer_family_name:o,payment_amount:"",payment_date:new Date().toISOString().split("T")[0],payment_method:"Cash",notes:""}))},[a,d,n,o]);let b=async e=>{e.preventDefault(),g(!0);try{let e={...u,payment_amount:parseFloat(u.payment_amount)},a=d?"/api/payments/".concat(d.id):"/api/payments",r=d?"PUT":"POST";(await fetch(a,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?t():console.error("Error saving payment record")}catch(e){console.error("Error saving payment record:",e)}finally{g(!1)}},p=i-parseFloat(u.payment_amount||"0");return a?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto",style:{backgroundColor:"dark"===m?"#1e293b":"#ffffff"},children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",style:{borderColor:"dark"===m?"#334155":"#e5e7eb"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[n&&o?(0,r.jsx)(eV,{customerName:n,customerFamilyName:o,profilePictureUrl:c||void 0,size:"md",showEditButton:!1}):(0,r.jsx)("div",{className:"p-2 rounded-lg bg-green-100",children:(0,r.jsx)(S.A,{className:"h-5 w-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",style:{color:"dark"===m?"#f8fafc":"#111827"},children:d?"Edit Payment":"Record Payment"}),n&&o&&(0,r.jsxs)("p",{className:"text-sm",style:{color:"dark"===m?"#cbd5e1":"#6b7280"},children:[n," ",o]})]})]}),(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)($.A,{className:"h-5 w-5",style:{color:"dark"===m?"#cbd5e1":"#6b7280"}})})]}),!d&&i>0&&(0,r.jsxs)("div",{className:"p-4 m-6 rounded-lg border",style:{backgroundColor:"dark"===m?"#374151":"#f8fafc",borderColor:"dark"===m?"#6b7280":"#e2e8f0"},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium",style:{color:"dark"===m?"#f9fafb":"#111827"},children:"Current Balance"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-red-600",children:["₱",i.toFixed(2)]})]}),u.payment_amount&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-sm",style:{color:"dark"===m?"#cbd5e1":"#6b7280"},children:"After Payment"}),(0,r.jsxs)("span",{className:"text-sm font-medium ".concat(p<=0?"text-green-600":"text-orange-600"),children:["₱",p.toFixed(2)]})]}),(0,r.jsxs)("button",{type:"button",onClick:()=>{h(e=>({...e,payment_amount:i.toString()}))},className:"w-full px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm font-medium",children:[(0,r.jsx)(eQ.A,{className:"h-4 w-4 inline mr-2"}),"Pay Full Balance"]})]}),(0,r.jsxs)("form",{onSubmit:b,className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===m?"#f9fafb":"#111827"},children:"First Name *"}),(0,r.jsx)("input",{type:"text",value:u.customer_name,onChange:e=>h({...u,customer_name:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===m?"#374151":"#ffffff",border:"dark"===m?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===m?"#f9fafb":"#111827"},required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===m?"#f9fafb":"#111827"},children:"Last Name *"}),(0,r.jsx)("input",{type:"text",value:u.customer_family_name,onChange:e=>h({...u,customer_family_name:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===m?"#374151":"#ffffff",border:"dark"===m?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===m?"#f9fafb":"#111827"},required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===m?"#f9fafb":"#111827"},children:"Payment Amount (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0.01",value:u.payment_amount,onChange:e=>h({...u,payment_amount:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===m?"#374151":"#ffffff",border:"dark"===m?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===m?"#f9fafb":"#111827"},placeholder:"0.00",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===m?"#f9fafb":"#111827"},children:[(0,r.jsx)(k.A,{className:"h-4 w-4 inline mr-1"}),"Payment Date"]}),(0,r.jsx)("input",{type:"date",value:u.payment_date,onChange:e=>h({...u,payment_date:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===m?"#374151":"#ffffff",border:"dark"===m?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===m?"#f9fafb":"#111827"},required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===m?"#f9fafb":"#111827"},children:[(0,r.jsx)(I.A,{className:"h-4 w-4 inline mr-1"}),"Payment Method"]}),(0,r.jsx)("select",{value:u.payment_method,onChange:e=>h({...u,payment_method:e.target.value}),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===m?"#374151":"#ffffff",border:"dark"===m?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===m?"#f9fafb":"#111827"},children:e0.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===m?"#f9fafb":"#111827"},children:[(0,r.jsx)(eK.A,{className:"h-4 w-4 inline mr-1"}),"Notes (Optional)"]}),(0,r.jsx)("textarea",{value:u.notes,onChange:e=>h({...u,notes:e.target.value}),rows:3,className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 resize-none",style:{backgroundColor:"dark"===m?"#374151":"#ffffff",border:"dark"===m?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===m?"#f9fafb":"#111827"},placeholder:"Add any additional notes about this payment..."})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border rounded-lg font-medium transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-700",style:{borderColor:"dark"===m?"#6b7280":"#d1d5db",color:"dark"===m?"#f9fafb":"#111827"},children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:x,className:"flex-1 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Saving...":d?"Update Payment":"Record Payment"})]})]})]})}):null}function e2(e){var a;let{onStatsUpdate:t}=e,{resolvedTheme:n}=(0,s.D)(),[d,m]=(0,l.useState)([]),[x,g]=(0,l.useState)(!0),[u,h]=(0,l.useState)(""),[b,p]=(0,l.useState)(!1),[f,y]=(0,l.useState)(null),[v,N]=(0,l.useState)("customer"),[w,C]=(0,l.useState)("debt_date"),[A,_]=(0,l.useState)("desc"),[L,T]=(0,l.useState)(new Set),[R,I]=(0,l.useState)(!1),[F,z]=(0,l.useState)({search:"",dateRange:{from:"",to:""},amountRange:{min:0,max:5e4},status:"all",customer:""}),[O,U]=(0,l.useState)([]),[B,q]=(0,l.useState)(!1),[H,G]=(0,l.useState)(!1),[Y,K]=(0,l.useState)(null),[X,Z]=(0,l.useState)(new Map),[Q,$]=(0,l.useState)(null),[ei,ec]=(0,l.useState)(!1),[em,ex]=(0,l.useState)(null),[eg,eu]=(0,l.useState)(0),[eh,eb]=(0,l.useState)(new Map),{getCustomerProfile:ep,updateCustomerProfile:ef}=function(){let[e,a]=(0,l.useState)(new Map);return{getCustomerProfile:async(t,r)=>{let s="".concat(t,"_").concat(r);if(e.has(s))return e.get(s);try{let e=await fetch("/api/customers?search=".concat(encodeURIComponent("".concat(t," ").concat(r))));if(e.ok){let l=await e.json();if(l.customers&&Array.isArray(l.customers)){let e=l.customers.find(e=>e.customer_name===t&&e.customer_family_name===r);if(e)return a(a=>new Map(a.set(s,e))),e}}}catch(e){console.error("Error fetching customer profile:",e)}return null},updateCustomerProfile:(e,t,r)=>{let s="".concat(e,"_").concat(t);a(e=>new Map(e.set(s,r)))},customers:e}}();(0,l.useEffect)(()=>{ev(),ey(),ej()},[]);let ey=async()=>{try{let e=await fetch("/api/customer-balances");if(e.ok){let a=await e.json(),t=new Map;a.balances&&Array.isArray(a.balances)&&a.balances.forEach(e=>{let a="".concat(e.customer_name," ").concat(e.customer_family_name);t.set(a,e)}),Z(t)}}catch(e){console.error("Error fetching customer balances:",e),Z(new Map)}},ej=async()=>{try{let e=await fetch("/api/customers");if(e.ok){let a=await e.json(),t=new Map;a.customers&&Array.isArray(a.customers)&&a.customers.forEach(e=>{let a="".concat(e.customer_name," ").concat(e.customer_family_name);t.set(a,e)}),eb(t)}}catch(e){console.error("Error fetching customer profiles:",e),eb(new Map)}},ev=async()=>{try{console.warn("\uD83D\uDD04 Fetching debts...");let e=await fetch("/api/debts");if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let a=await e.json();console.warn("\uD83D\uDCB3 Debts API response:",a),a.success&&a.data&&a.data.debts?(m(a.data.debts),console.warn("✅ Debts loaded (new structure):",a.data.debts.length,"items")):a.debts?(m(a.debts),console.warn("✅ Debts loaded (old structure):",a.debts.length,"items")):Array.isArray(a)?(m(a),console.warn("✅ Debts loaded (direct array):",a.length,"items")):(console.warn("⚠️ Unexpected API response structure:",a),m([]))}catch(e){console.error("❌ Error fetching debts:",e),m([])}finally{g(!1)}},ek=async e=>{if(confirm("Are you sure you want to delete this debt record?"))try{(await fetch("/api/debts/".concat(e),{method:"DELETE"})).ok&&(m(d.filter(a=>a.id!==e)),t())}catch(e){console.error("Error deleting debt:",e)}},eN=e=>{y(e),p(!0)},ew=(e,a)=>{let t="".concat(e," ").concat(a),r=X.get(t);K({name:e,familyName:a,balance:(null==r?void 0:r.remaining_balance)||0}),G(!0)},eC=async(e,a)=>{let t=await ep(e,a);ex({name:e,familyName:a,customer:t}),ec(!0)},eA=(0,l.useCallback)(e=>{if(!e||e.length<2)return void U([]);U([...new Set(d.filter(a=>{let t="".concat(a.customer_name," ").concat(a.customer_family_name).toLowerCase(),r=a.product_name.toLowerCase();return t.includes(e.toLowerCase())||r.includes(e.toLowerCase())}).map(e=>"".concat(e.customer_name," ").concat(e.customer_family_name)).slice(0,5))])},[d]),eS=(0,l.useMemo)(()=>{let e=d.filter(e=>{let a="".concat(e.customer_name," ").concat(e.customer_family_name).toLowerCase(),t=e.product_name.toLowerCase(),r=(F.search||u).toLowerCase(),s=!r||a.includes(r)||t.includes(r),l=new Date(e.debt_date),n=(!F.dateRange.from||l>=new Date(F.dateRange.from))&&(!F.dateRange.to||l<=new Date(F.dateRange.to)),o=e.total_amount>=F.amountRange.min&&e.total_amount<=F.amountRange.max,i=!0;if("all"!==F.status){let a=(0,eX.c)(new Date,l);switch(F.status){case"recent":i=a<=7;break;case"overdue":i=a>30;break;case"high-amount":i=e.total_amount>1e3}}let d=!F.customer||a.includes(F.customer.toLowerCase());return s&&n&&o&&i&&d});return e.sort((e,a)=>{let t,r;switch(w){case"customer_name":t="".concat(e.customer_name," ").concat(e.customer_family_name).toLowerCase(),r="".concat(a.customer_name," ").concat(a.customer_family_name).toLowerCase();break;case"total_amount":t=e.total_amount,r=a.total_amount;break;case"debt_date":t=new Date(e.debt_date).getTime(),r=new Date(a.debt_date).getTime();break;case"product_name":t=e.product_name.toLowerCase(),r=a.product_name.toLowerCase();break;case"created_at":t=new Date(e.created_at).getTime(),r=new Date(a.created_at).getTime();break;default:return 0}return t<r?"asc"===A?-1:1:t>r?"asc"===A?1:-1:0}),e},[d,u,F,w,A]),eD=(0,l.useMemo)(()=>{let e=[],a=new Map;return eS.forEach(e=>{let t="".concat(e.customer_name," ").concat(e.customer_family_name);a.has(t)||a.set(t,[]),a.get(t).push(e)}),a.forEach((a,t)=>{var r,s;let l=a.reduce((e,a)=>e+a.total_amount,0),n=a.sort((e,a)=>new Date(e.debt_date).getTime()-new Date(a.debt_date).getTime()),o=X.get(t),i=(null==o?void 0:o.total_payments)||0,d=(null==o?void 0:o.remaining_balance)||l;e.push({customerName:t,totalDebts:a.length,totalAmount:l,totalPayments:i,remainingBalance:d,oldestDebt:(null==(r=n[0])?void 0:r.debt_date)||"",recentDebt:(null==(s=n[n.length-1])?void 0:s.debt_date)||"",debts:n})}),e.sort((e,a)=>a.totalAmount-e.totalAmount)},[eS,X]),e_=e=>{let a=new Set(L);a.has(e)?a.delete(e):a.add(e),T(a)},eM=async()=>{if(0!==L.size&&confirm("Are you sure you want to delete ".concat(L.size," debt record(s)?")))try{let e=Array.from(L).map(e=>fetch("/api/debts/".concat(e),{method:"DELETE"}));await Promise.all(e),T(new Set),ev(),t()}catch(e){console.error("Error deleting debt records:",e)}},eL=e=>{h(e),z(a=>({...a,search:e})),eA(e),q(e.length>=2)},eT=e=>{h(e),z(a=>({...a,search:e})),q(!1)};return x?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"w-64 h-10 rounded-lg",style:{backgroundColor:"dark"===n?"#374151":"#f3f4f6",backgroundImage:"dark"===n?"linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)":"linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)",backgroundSize:"200% 100%",animation:"shimmer 2s infinite"}}),(0,r.jsx)("div",{className:"w-48 h-10 rounded-lg",style:{backgroundColor:"dark"===n?"#374151":"#f3f4f6",backgroundImage:"dark"===n?"linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)":"linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)",backgroundSize:"200% 100%",animation:"shimmer 2s infinite"}})]}),(0,r.jsx)(eP,{type:"debts",count:4})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,r.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300",style:{color:"dark"===n?"#9ca3af":"#6b7280"}}),(0,r.jsx)("input",{type:"text",placeholder:"Search customers, products, or amounts...",value:u,onChange:e=>eL(e.target.value),onFocus:()=>q(u.length>=2),onBlur:()=>setTimeout(()=>q(!1),200),className:"w-full pl-10 pr-4 py-2.5 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-sm",style:{backgroundColor:"dark"===n?"#374151":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"}}),B&&O.length>0&&(0,r.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg border z-50 max-h-48 overflow-y-auto",style:{backgroundColor:"dark"===n?"#374151":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db"},children:O.map((e,a)=>(0,r.jsxs)("button",{onClick:()=>eT(e),className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",style:{color:"dark"===n?"#f9fafb":"#111827"},children:[(0,r.jsx)(i.A,{className:"h-4 w-4 inline mr-2 text-green-600"}),e]},a))})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>z(e=>({...e,status:"recent"===e.status?"all":"recent"})),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ".concat("recent"===F.status?"bg-blue-100 text-blue-700 border-blue-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)(P.A,{className:"h-4 w-4 inline mr-1"}),"Recent"]}),(0,r.jsxs)("button",{onClick:()=>z(e=>({...e,status:"overdue"===e.status?"all":"overdue"})),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ".concat("overdue"===F.status?"bg-red-100 text-red-700 border-red-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)(D.A,{className:"h-4 w-4 inline mr-1"}),"Overdue"]}),(0,r.jsxs)("button",{onClick:()=>z(e=>({...e,status:"high-amount"===e.status?"all":"high-amount"})),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ".concat("high-amount"===F.status?"bg-orange-100 text-orange-700 border-orange-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)(S.A,{className:"h-4 w-4 inline mr-1"}),"High Amount"]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>I(!R),className:"px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm ".concat(R?"bg-green-100 text-green-700 border-green-300":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"," border"),children:[(0,r.jsx)(V.A,{className:"h-4 w-4 inline mr-2"}),"Filters"]}),eS.length>0&&(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("button",{className:"px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 border",children:[(0,r.jsx)(W.A,{className:"h-4 w-4 inline mr-2"}),"Export",(0,r.jsx)(ee.A,{className:"h-4 w-4 inline ml-1"})]}),(0,r.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",children:(0,r.jsxs)("div",{className:"rounded-lg shadow-lg border py-1",style:{backgroundColor:"dark"===n?"#374151":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #e5e7eb"},children:[(0,r.jsxs)("button",{onClick:()=>{!function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"customer_debts";ez(["ID,Customer Name,Total Amount,Product Name,Quantity,Debt Date,Created At,Updated At",...e.map(e=>{var a;return[e.id,'"'.concat(e.customer_name.replace(/"/g,'""'),'"'),e.total_amount,'"'.concat((null==(a=e.product_name)?void 0:a.replace(/"/g,'""'))||"",'"'),e.quantity,e.debt_date||"",e.created_at,e.updated_at].join(",")})].join("\n"),"".concat(a,".csv"),"text/csv")}(eS,"customer_debts_".concat(new Date().toISOString().split("T")[0]))},className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===n?"#f9fafb":"#111827"},children:[(0,r.jsx)(W.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Export as CSV"})]}),(0,r.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify(eS,null,2)],{type:"application/json"}),a=URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download="customer_debts_".concat(new Date().toISOString().split("T")[0],".json"),t.click(),URL.revokeObjectURL(a)},className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===n?"#f9fafb":"#111827"},children:[(0,r.jsx)(W.A,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Export as JSON"})]}),(0,r.jsx)("div",{className:"my-1 h-px",style:{backgroundColor:"dark"===n?"#6b7280":"#e5e7eb"}}),(0,r.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify({summary:{totalDebts:eS.length,totalAmount:eS.reduce((e,a)=>e+a.total_amount,0),uniqueCustomers:new Set(eS.map(e=>"".concat(e.customer_name," ").concat(e.customer_family_name))).size,averageDebtAmount:eS.length>0?eS.reduce((e,a)=>e+a.total_amount,0)/eS.length:0},customerBreakdown:eD,generatedAt:new Date().toISOString()},null,2)],{type:"application/json"}),a=URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download="debt_analytics_".concat(new Date().toISOString().split("T")[0],".json"),t.click(),URL.revokeObjectURL(a)},className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3",style:{color:"dark"===n?"#f9fafb":"#111827"},children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-purple-600"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Analytics Report"})]})]})})]}),(0,r.jsxs)("button",{onClick:()=>p(!0),className:"flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Add Debt Record"]})]})]}),R&&(0,r.jsx)("div",{className:"p-6 rounded-xl border shadow-sm animate-slide-down",style:{backgroundColor:"dark"===n?"#374151":"#f8fafc",border:"dark"===n?"1px solid #6b7280":"1px solid #e2e8f0"},children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===n?"#f9fafb":"#111827"},children:"Date Range"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"date",value:F.dateRange.from,onChange:e=>z(a=>({...a,dateRange:{...a.dateRange,from:e.target.value}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"}}),(0,r.jsx)("input",{type:"date",value:F.dateRange.to,onChange:e=>z(a=>({...a,dateRange:{...a.dateRange,to:e.target.value}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"}})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===n?"#f9fafb":"#111827"},children:"Amount Range (₱)"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"number",placeholder:"Min",value:F.amountRange.min,onChange:e=>z(a=>({...a,amountRange:{...a.amountRange,min:Number(e.target.value)||0}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"}}),(0,r.jsx)("input",{type:"number",placeholder:"Max",value:F.amountRange.max,onChange:e=>z(a=>({...a,amountRange:{...a.amountRange,max:Number(e.target.value)||5e4}})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"}})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"dark"===n?"#f9fafb":"#111827"},children:"Customer"}),(0,r.jsx)("input",{type:"text",placeholder:"Filter by customer name",value:F.customer,onChange:e=>z(a=>({...a,customer:e.target.value})),className:"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"}})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("button",{onClick:()=>z({search:"",dateRange:{from:"",to:""},amountRange:{min:0,max:5e4},status:"all",customer:""}),className:"w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium text-sm",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 inline mr-2"}),"Reset Filters"]})})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("span",{className:"text-sm font-medium",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"customer"===v?"".concat(eD.length," customer").concat(1!==eD.length?"s":""," with ").concat(eS.length," debt").concat(1!==eS.length?"s":""):"".concat(eS.length," debt record").concat(1!==eS.length?"s":""," found")}),eS.length>0&&"customer"!==v&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>{L.size===eS.length?T(new Set):T(new Set(eS.map(e=>e.id)))},className:"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:L.size===eS.length?"Deselect All":"Select All",children:L.size===eS.length?(0,r.jsx)(ea.A,{className:"h-4 w-4 text-green-600"}):L.size>0?(0,r.jsx)(J.A,{className:"h-4 w-4 text-gray-600"}):(0,r.jsx)(et.A,{className:"h-4 w-4 text-gray-600"})}),L.size>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[L.size," selected"]}),(0,r.jsxs)("button",{onClick:eM,className:"px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium",children:[(0,r.jsx)(er.A,{className:"h-3 w-3 inline mr-1"}),"Delete"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:"Sort by:"}),(0,r.jsxs)("select",{value:w,onChange:e=>C(e.target.value),className:"px-3 py-1.5 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===n?"#374151":"#ffffff",border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===n?"#f9fafb":"#111827"},children:[(0,r.jsx)("option",{value:"debt_date",children:"Debt Date"}),(0,r.jsx)("option",{value:"customer_name",children:"Customer Name"}),(0,r.jsx)("option",{value:"total_amount",children:"Amount"}),(0,r.jsx)("option",{value:"product_name",children:"Product"}),(0,r.jsx)("option",{value:"created_at",children:"Date Added"})]}),(0,r.jsx)("button",{onClick:()=>_("asc"===A?"desc":"asc"),className:"p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",title:"Sort ".concat("asc"===A?"Descending":"Ascending"),children:"asc"===A?(0,r.jsx)(es.A,{className:"h-4 w-4",style:{color:"dark"===n?"#cbd5e1":"#6b7280"}}):(0,r.jsx)(el.A,{className:"h-4 w-4",style:{color:"dark"===n?"#cbd5e1":"#6b7280"}})})]}),(0,r.jsxs)("div",{className:"flex rounded-lg border",style:{border:"dark"===n?"1px solid #6b7280":"1px solid #d1d5db"},children:[(0,r.jsxs)("button",{onClick:()=>N("customer"),className:"px-3 py-2 text-sm transition-colors ".concat("customer"===v?"bg-green-100 text-green-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),title:"Customer View",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 inline mr-1"}),"Customers"]}),(0,r.jsxs)("button",{onClick:()=>N("list"),className:"px-3 py-2 text-sm transition-colors ".concat("list"===v?"bg-green-100 text-green-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),title:"List View",children:[(0,r.jsx)(eo.A,{className:"h-4 w-4 inline mr-1"}),"List"]}),(0,r.jsxs)("button",{onClick:()=>N("grid"),className:"px-3 py-2 text-sm transition-colors ".concat("grid"===v?"bg-green-100 text-green-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),title:"Grid View",children:[(0,r.jsx)(en.A,{className:"h-4 w-4 inline mr-1"}),"Grid"]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:["customer"===v?eD.length>0?eD.map(e=>{var a,t,s;return(0,r.jsxs)("div",{className:"rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg",style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b",style:{backgroundColor:"dark"===n?"#374151":"#f8fafc",borderColor:"dark"===n?"#6b7280":"#e2e8f0"},children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(eV,{customerName:(null==(a=e.customerName)?void 0:a.split(" ")[0])||"",customerFamilyName:(null==(t=e.customerName)?void 0:t.split(" ").slice(1).join(" "))||"",profilePictureUrl:(null==(s=eh.get(e.customerName))?void 0:s.profile_picture_url)||void 0,size:"lg",showEditButton:!0,onEditClick:()=>{var a,t;return eC((null==(a=e.customerName)?void 0:a.split(" ")[0])||"",(null==(t=e.customerName)?void 0:t.split(" ").slice(1).join(" "))||"")}},"".concat(e.customerName,"-").concat(eg)),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",style:{color:"dark"===n?"#f8fafc":"#111827"},children:e.customerName}),(0,r.jsxs)("p",{className:"text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:[e.totalDebts," debt record",1!==e.totalDebts?"s":""]})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-lg font-semibold text-gray-600",children:["₱",e.totalAmount.toFixed(2)]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Total Debt"})]}),e.totalPayments>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-lg font-semibold text-green-600",children:["-₱",e.totalPayments.toFixed(2)]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Total Paid"})]}),(0,r.jsxs)("div",{className:"border-t pt-2",children:[(0,r.jsxs)("p",{className:"text-2xl font-bold ".concat(e.remainingBalance<=0?"text-green-600":"text-red-600"),children:["₱",e.remainingBalance.toFixed(2)]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.remainingBalance<=0?"Fully Paid":"Balance Due"})]}),e.remainingBalance>0&&(0,r.jsxs)("button",{onClick:()=>{let a=e.customerName.split(" ");ew(a[0]||"",a.slice(1).join(" ")||"")},className:"w-full mt-2 px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg text-sm font-medium",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 inline mr-1"}),"Record Payment"]})]})})]}),(0,r.jsxs)("div",{className:"mt-4 grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(P.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsxs)("span",{className:"text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:["Oldest: ",(0,eZ.GP)(new Date(e.oldestDebt),"MMM dd, yyyy")]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("span",{className:"text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:["Recent: ",(0,eZ.GP)(new Date(e.recentDebt),"MMM dd, yyyy")]})]})]})]}),(0,r.jsx)("div",{className:"divide-y",style:{borderColor:"dark"===n?"#6b7280":"#e5e7eb"},children:e.debts.map(e=>(0,r.jsx)("div",{className:"px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-1.5 rounded-lg bg-blue-100",children:(0,r.jsx)(o.A,{className:"h-4 w-4 text-blue-600"})}),(0,r.jsx)("h4",{className:"font-medium",style:{color:"dark"===n?"#f8fafc":"#111827"},children:e.product_name})]}),(0,r.jsxs)("div",{className:"ml-8 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(eQ.A,{className:"h-3 w-3"}),"Qty: ",e.quantity]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(S.A,{className:"h-3 w-3"}),"Unit: ₱",e.product_price.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:[(0,r.jsx)(k.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:(0,eZ.GP)(new Date(e.debt_date),"MMM dd, yyyy")}),(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat((0,eX.c)(new Date,new Date(e.debt_date))>30?"bg-red-100 text-red-700":(0,eX.c)(new Date,new Date(e.debt_date))>7?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"),children:[(0,eX.c)(new Date,new Date(e.debt_date))," days ago"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 ml-4",children:[(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("p",{className:"text-lg font-bold text-red-600",children:["₱",e.total_amount.toFixed(2)]})}),(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)("button",{onClick:()=>eN(e),className:"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-all duration-200 hover:scale-105",title:"Edit Debt Record",children:(0,r.jsx)(ed.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>ek(e.id),className:"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-all duration-200 hover:scale-105",title:"Delete Debt Record",children:(0,r.jsx)(er.A,{className:"h-4 w-4"})})]})]})]})},e.id))})]},e.customerName)}):(0,r.jsxs)("div",{className:"text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300",style:{backgroundColor:"dark"===n?"rgba(30, 41, 59, 0.5)":"rgba(249, 250, 251, 0.8)",borderColor:"dark"===n?"#475569":"#d1d5db"},children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300",style:{backgroundColor:"dark"===n?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.05)",border:"dark"===n?"2px solid rgba(34, 197, 94, 0.3)":"2px solid rgba(34, 197, 94, 0.2)"},children:(0,r.jsx)(i.A,{className:"h-10 w-10 transition-colors duration-300",style:{color:"dark"===n?"#4ade80":"#16a34a"}})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:"No customers with debt records found"}),(0,r.jsx)("p",{className:"text-sm mb-6 max-w-md mx-auto transition-colors duration-300",style:{color:"dark"===n?"#94a3b8":"#6b7280"},children:u||Object.values(F).some(e=>e&&"all"!==e)?"Try adjusting your search terms or filter criteria to find what you're looking for":"Get started by adding your first debt record to track customer purchases"}),!u&&!Object.values(F).some(e=>e&&"all"!==e)&&(0,r.jsxs)("button",{onClick:()=>p(!0),className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium",children:[(0,r.jsx)(E.A,{className:"h-5 w-5 mr-2"}),"Add First Debt Record"]})]}):(0,r.jsx)("div",{className:"grid"===v?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:eS.map(e=>{var a;return(0,r.jsxs)("div",{className:"relative group rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl ".concat(L.has(e.id)?"ring-2 ring-green-500 ring-offset-2":"hover:scale-[1.02]"," ").concat("list"===v?"flex items-center":""),style:{backgroundColor:"dark"===n?"#1e293b":"#ffffff",border:"dark"===n?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,r.jsx)("div",{className:"absolute top-3 left-3 z-10",children:(0,r.jsx)("button",{onClick:()=>e_(e.id),className:"p-1 rounded-md transition-all duration-200 ".concat(L.has(e.id)?"bg-green-500 text-white":"bg-white/80 text-gray-600 hover:bg-white"," shadow-sm"),children:L.has(e.id)?(0,r.jsx)(ea.A,{className:"h-4 w-4"}):(0,r.jsx)(et.A,{className:"h-4 w-4"})})}),(0,r.jsx)("div",{className:"".concat("grid"===v?"p-6":"flex-1 p-4"),children:(0,r.jsxs)("div",{className:"".concat("list"===v?"flex items-center justify-between":""),children:[(0,r.jsxs)("div",{className:"".concat("list"===v?"flex-1":""),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)(eV,{customerName:e.customer_name,customerFamilyName:e.customer_family_name,profilePictureUrl:(null==(a=eh.get("".concat(e.customer_name," ").concat(e.customer_family_name)))?void 0:a.profile_picture_url)||void 0,size:"md",showEditButton:!1},"".concat(e.customer_name,"-").concat(e.customer_family_name,"-").concat(eg)),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold",style:{color:"dark"===n?"#f8fafc":"#111827"},children:[e.customer_name," ",e.customer_family_name]}),(0,r.jsx)("p",{className:"text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:e.product_name})]})]}),(0,r.jsxs)("div",{className:"".concat("list"===v?"flex items-center gap-6":"space-y-2"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(eQ.A,{className:"h-3 w-3"}),"Qty: ",e.quantity]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(S.A,{className:"h-3 w-3"}),"₱",e.product_price.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm",style:{color:"dark"===n?"#cbd5e1":"#6b7280"},children:[(0,r.jsx)(k.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:(0,eZ.GP)(new Date(e.debt_date),"MMM dd, yyyy")})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-red-600",children:["₱",e.total_amount.toFixed(2)]}),(0,r.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat((0,eX.c)(new Date,new Date(e.debt_date))>30?"bg-red-100 text-red-700":(0,eX.c)(new Date,new Date(e.debt_date))>7?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"),children:[(0,eX.c)(new Date,new Date(e.debt_date))," days ago"]})]})]})]}),(0,r.jsxs)("div",{className:"".concat("list"===v?"flex gap-2 ml-4":"flex gap-2 mt-4"),children:[(0,r.jsxs)("button",{onClick:()=>eN(e),className:"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-300 hover:scale-105 font-medium",title:"Edit Debt Record",children:[(0,r.jsx)(ed.A,{className:"h-4 w-4 mr-1"}),"grid"===v?"Edit":""]}),(0,r.jsxs)("button",{onClick:()=>ek(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-300 hover:scale-105 font-medium",title:"Delete Debt Record",children:[(0,r.jsx)(er.A,{className:"h-4 w-4 mr-1"}),"grid"===v?"Delete":""]})]})]})})]},e.id)})}),("list"===v||"grid"===v)&&0===eS.length&&!x&&(0,r.jsxs)("div",{className:"text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300",style:{backgroundColor:"dark"===n?"rgba(30, 41, 59, 0.5)":"rgba(249, 250, 251, 0.8)",borderColor:"dark"===n?"#475569":"#d1d5db"},children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300",style:{backgroundColor:"dark"===n?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.05)",border:"dark"===n?"2px solid rgba(34, 197, 94, 0.3)":"2px solid rgba(34, 197, 94, 0.2)"},children:(0,r.jsx)(eK.A,{className:"h-10 w-10 transition-colors duration-300",style:{color:"dark"===n?"#4ade80":"#16a34a"}})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 transition-colors duration-300",style:{color:"dark"===n?"#f8fafc":"#111827"},children:u||Object.values(F).some(e=>e&&"all"!==e)?"No debt records found":"No debt records yet"}),(0,r.jsx)("p",{className:"text-sm mb-6 max-w-md mx-auto transition-colors duration-300",style:{color:"dark"===n?"#94a3b8":"#6b7280"},children:u||Object.values(F).some(e=>e&&"all"!==e)?"Try adjusting your search criteria or filters to find specific debt records":"Start tracking customer debts by adding your first debt record to manage store credit"}),!u&&!Object.values(F).some(e=>e&&"all"!==e)&&(0,r.jsxs)("button",{onClick:()=>p(!0),className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl",children:[(0,r.jsx)(E.A,{className:"h-5 w-5 mr-2"}),"Add First Debt Record"]})]})]}),(0,r.jsx)(e$,{isOpen:b,onClose:()=>{p(!1),y(null),ev(),ey(),t()},debt:f}),(0,r.jsx)(e1,{isOpen:H,onClose:()=>{G(!1),K(null),$(null),ey(),t()},customerName:(null==Y?void 0:Y.name)||"",customerFamilyName:(null==Y?void 0:Y.familyName)||"",currentBalance:(null==Y?void 0:Y.balance)||0,payment:Q,profilePictureUrl:Y&&(null==(a=eh.get("".concat(Y.name," ").concat(Y.familyName)))?void 0:a.profile_picture_url)||void 0}),(0,r.jsx)(eJ,{isOpen:ei,onClose:()=>{ec(!1),ex(null),ey(),ej()},customer:(null==em?void 0:em.customer)||null,customerName:(null==em?void 0:em.name)||"",customerFamilyName:(null==em?void 0:em.familyName)||"",onProfileUpdated:e=>{ef(e.customer_name,e.customer_family_name,e);let a="".concat(e.customer_name," ").concat(e.customer_family_name);eb(t=>new Map(t.set(a,e))),eu(e=>e+1),ey(),ej()}})]})}var e5=t(4395),e4=t(3332),e3=t(1976),e6=t(6516),e7=t(2178),e9=t(5690);function e8(){var e,a,t,s,n;let[o,m]=(0,l.useState)([{id:"1",url:"/api/placeholder/400/300",title:"Pag-bukas sa Tindahan",description:"Grand opening sa aming Revantad Store kauban ang buong pamilya",date:"2024-01-15",likes:12,isLiked:!0,category:"store",tags:["opening","family","milestone"],views:45,isPrivate:!1,uploadedBy:"Admin",fileSize:"2.3 MB",dimensions:"1920x1080",location:"Cebu City"},{id:"2",url:"/api/placeholder/400/300",title:"Anniversary Celebration",description:"Nag-celebrate mi sa first year sa business",date:"2024-02-20",likes:8,isLiked:!1,category:"celebrations",tags:["anniversary","celebration","milestone"],views:32,isPrivate:!1,uploadedBy:"Admin",fileSize:"1.8 MB",dimensions:"1920x1080",location:"Cebu City"},{id:"3",url:"/api/placeholder/400/300",title:"Community Festival",description:"Nag-participate mi sa local community festival",date:"2024-03-10",likes:15,isLiked:!0,category:"events",tags:["community","festival","participation"],views:67,isPrivate:!1,uploadedBy:"Admin",fileSize:"3.1 MB",dimensions:"1920x1080",location:"Barangay San Jose"},{id:"4",url:"/api/placeholder/400/300",title:"Mga Produkto sa Store",description:"Showcase sa mga bag-ong produkto sa aming tindahan",date:"2024-03-25",likes:6,isLiked:!1,category:"products",tags:["products","showcase","inventory"],views:28,isPrivate:!1,uploadedBy:"Admin",fileSize:"1.5 MB",dimensions:"1920x1080"},{id:"5",url:"/api/placeholder/400/300",title:"Family Bonding Time",description:"Quality time sa pamilya after work sa tindahan",date:"2024-04-05",likes:20,isLiked:!0,category:"family",tags:["family","bonding","quality-time"],views:89,isPrivate:!1,uploadedBy:"Admin",fileSize:"2.7 MB",dimensions:"1920x1080"},{id:"6",url:"/api/placeholder/400/300",title:"Mga Suki sa Tindahan",description:"Mga loyal customers nga nag-visit sa store",date:"2024-04-12",likes:11,isLiked:!1,category:"customers",tags:["customers","loyalty","community"],views:41,isPrivate:!1,uploadedBy:"Admin",fileSize:"2.1 MB",dimensions:"1920x1080"}]),[x,g]=(0,l.useState)(!1),[u,h]=(0,l.useState)(null),[b,p]=(0,l.useState)(!1),[f,y]=(0,l.useState)(0),[j,v]=(0,l.useState)(!1),[A,S]=(0,l.useState)(""),[D,L]=(0,l.useState)("all"),[T,R]=(0,l.useState)("date"),[I,F]=(0,l.useState)("desc"),[z,O]=(0,l.useState)("grid"),[U,B]=(0,l.useState)([]),[q,H]=(0,l.useState)(!1),[G,Y]=(0,l.useState)(!1),K=(0,l.useRef)(null),[J,Z]=(0,l.useState)({title:"",description:"",file:null,category:"family",tags:"",isPrivate:!1,location:""}),Q=(0,l.useMemo)(()=>{let e=o.filter(e=>{let a=""===A||e.title.toLowerCase().includes(A.toLowerCase())||e.description.toLowerCase().includes(A.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(A.toLowerCase())),t="all"===D||e.category===D;return a&&t});return e.sort((e,a)=>{let t=0;switch(T){case"date":t=new Date(e.date).getTime()-new Date(a.date).getTime();break;case"likes":t=e.likes-a.likes;break;case"views":t=e.views-a.views;break;case"title":t=e.title.localeCompare(a.title)}return"desc"===I?-t:t}),e},[o,A,D,T,I]),ee=e=>{m(o.map(a=>a.id===e?{...a,likes:a.isLiked?a.likes-1:a.likes+1,isLiked:!a.isLiked}:a))},ea=e=>{m(o.map(a=>a.id===e?{...a,views:a.views+1}:a))},et=e=>{confirm("Sigurado ka ba nga i-delete ni nga photo?")&&(m(o.filter(a=>a.id!==e)),B(U.filter(a=>a!==e)))},ei=e=>{B(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},ec=e=>{y(e),p(!0);let a=Q[e];a&&ea(a.id)},em=(0,l.useMemo)(()=>{let e=o.reduce((e,a)=>e+a.views,0),a=o.reduce((e,a)=>e+a.likes,0),t=o.reduce((e,a)=>(e[a.category]=(e[a.category]||0)+1,e),{});return{totalPhotos:o.length,totalViews:e,totalLikes:a,categoryCounts:t,averageViews:Math.round(e/o.length)||0,mostPopular:o.length>0?o.reduce((e,a)=>e&&e.views>a.views?e:a,o[0]):null}},[o]),ex=[{id:"all",label:"Tanan",icon:en.A},{id:"family",label:"Pamilya",icon:i.A},{id:"store",label:"Tindahan",icon:e5.A},{id:"events",label:"Mga Event",icon:k.A},{id:"products",label:"Produkto",icon:e4.A},{id:"customers",label:"Mga Suki",icon:e3.A},{id:"celebrations",label:"Selebrasyon",icon:eR.A}];return(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-lg shadow-lg",children:(0,r.jsx)(eU.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Family Gallery"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Mga memories ug moments sa inyong pamilya ug tindahan"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-blue-600 dark:text-blue-400",children:"Total Photos"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-900 dark:text-blue-300",children:em.totalPhotos})]}),(0,r.jsx)(d.A,{className:"h-5 w-5 text-blue-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-green-600 dark:text-green-400",children:"Total Views"}),(0,r.jsx)("p",{className:"text-lg font-bold text-green-900 dark:text-green-300",children:em.totalViews})]}),(0,r.jsx)(X.A,{className:"h-5 w-5 text-green-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-3 rounded-lg border border-red-200 dark:border-red-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-red-600 dark:text-red-400",children:"Total Likes"}),(0,r.jsx)("p",{className:"text-lg font-bold text-red-900 dark:text-red-300",children:em.totalLikes})]}),(0,r.jsx)(e3.A,{className:"h-5 w-5 text-red-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-amber-50 to-yellow-100 dark:from-amber-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-amber-700 dark:text-amber-400",children:"Selected"}),(0,r.jsx)("p",{className:"text-lg font-bold text-amber-900 dark:text-amber-300",children:U.length})]}),(0,r.jsx)(_.A,{className:"h-5 w-5 text-amber-600"})]})})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[U.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>{U.length>0&&confirm("Sigurado ka ba nga i-delete ang ".concat(U.length," ka photos?"))&&(m(o.filter(e=>!U.includes(e.id))),B([]))},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(er.A,{className:"h-4 w-4"}),"Delete Selected (",U.length,")"]}),(0,r.jsxs)("button",{onClick:()=>B([]),className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)($.A,{className:"h-4 w-4"}),"Clear Selection"]})]}),(0,r.jsxs)("button",{onClick:()=>Y(!G),className:"btn-outline flex items-center gap-2 text-sm",disabled:G,children:[(0,r.jsx)(M.A,{className:"h-4 w-4 ".concat(G?"animate-spin":"")}),"Refresh"]}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"btn-primary flex items-center gap-2",children:[(0,r.jsx)(E.A,{className:"h-4 w-4"}),"Add Photo"]})]})]}),(0,r.jsxs)("div",{className:"card p-6 border-l-4 border-l-amber-600",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-amber-600"}),"Search & Filters"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>H(!q),className:"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors",children:[(0,r.jsx)(V.A,{className:"h-4 w-4"}),q?"Hide Filters":"Show Filters"]}),(0,r.jsxs)("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:[(0,r.jsx)("button",{onClick:()=>O("grid"),className:"px-3 py-1 text-xs font-medium rounded-md transition-all ".concat("grid"===z?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:(0,r.jsx)(en.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>O("list"),className:"px-3 py-1 text-xs font-medium rounded-md transition-all ".concat("list"===z?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:(0,r.jsx)(eo.A,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search photos, descriptions, tags...",value:A,onChange:e=>S(e.target.value),className:"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm"})]}),(0,r.jsx)("select",{value:D,onChange:e=>L(e.target.value),className:"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm",children:ex.map(e=>(0,r.jsx)("option",{value:e.id,children:e.label},e.id))}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:T,onChange:e=>R(e.target.value),className:"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"date",children:"Sort by Date"}),(0,r.jsx)("option",{value:"likes",children:"Sort by Likes"}),(0,r.jsx)("option",{value:"views",children:"Sort by Views"}),(0,r.jsx)("option",{value:"title",children:"Sort by Title"})]}),(0,r.jsx)("button",{onClick:()=>F("asc"===I?"desc":"asc"),className:"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Sort ".concat("asc"===I?"Descending":"Ascending"),children:"asc"===I?(0,r.jsx)(es.A,{className:"h-4 w-4"}):(0,r.jsx)(el.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsx)("button",{onClick:()=>{U.length===Q.length?B([]):B(Q.map(e=>e.id))},className:"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm",children:U.length===Q.length?"Deselect All":"Select All"})})]}),q&&(0,r.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:ex.slice(1).map(e=>{let a=e.icon,t=em.categoryCounts[e.id]||0;return(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("button",{onClick:()=>L(e.id),className:"w-full p-3 rounded-lg border-2 transition-all ".concat(D===e.id?"border-amber-600 bg-amber-50 dark:bg-amber-900/20":"border-gray-200 dark:border-gray-700 hover:border-amber-300"),children:[(0,r.jsx)(a,{className:"h-6 w-6 mx-auto mb-2 ".concat(D===e.id?"text-amber-600":"text-gray-500")}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.label}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[t," photos"]})]})},e.id)})})})]}),(0,r.jsxs)("div",{className:"card overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-amber-600"}),"Photo Gallery (",Q.length,")"]}),U.length>0&&(0,r.jsxs)("span",{className:"text-xs bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 px-2 py-1 rounded-full",children:[U.length," selected"]})]})}),"grid"===z?(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Q.map((e,a)=>{var t;return(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("div",{className:"absolute top-2 left-2 z-10",children:(0,r.jsx)("input",{type:"checkbox",checked:U.includes(e.id),onChange:()=>ei(e.id),className:"w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"})}),(0,r.jsx)("div",{className:"absolute top-2 right-2 z-10",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("family"===e.category?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400":"store"===e.category?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"events"===e.category?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"products"===e.category?"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400":"customers"===e.category?"bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400":"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"),children:null==(t=ex.find(a=>a.id===e.category))?void 0:t.label})}),(0,r.jsxs)("div",{className:"card overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1",children:[(0,r.jsxs)("div",{className:"relative aspect-video bg-gray-200 dark:bg-gray-700 cursor-pointer",onClick:()=>ec(a),children:[(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-16 w-16 text-gray-400"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("button",{onClick:e=>{e.stopPropagation(),ec(a)},className:"bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(X.A,{className:"h-4 w-4 inline mr-1"}),"View"]}),(0,r.jsxs)("button",{onClick:a=>{a.stopPropagation(),h(e)},className:"bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 inline mr-1"}),"Details"]})]})}),(0,r.jsxs)("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs flex items-center gap-1",children:[(0,r.jsx)(X.A,{className:"h-3 w-3"}),e.views]})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white mb-1 line-clamp-1",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2",children:e.description}),e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mb-3",children:[e.tags.slice(0,3).map(e=>(0,r.jsxs)("span",{className:"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md",children:["#",e]},e)),e.tags.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",e.tags.length-3," more"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsx)(P.A,{className:"h-3 w-3"}),new Date(e.date).toLocaleDateString("en-PH")]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>ee(e.id),className:"flex items-center space-x-1 text-sm transition-colors ".concat(e.isLiked?"text-red-500":"text-gray-500 dark:text-gray-400 hover:text-red-500"),children:[(0,r.jsx)(e3.A,{className:"h-4 w-4 ".concat(e.isLiked?"fill-current":"")}),(0,r.jsx)("span",{children:e.likes})]}),(0,r.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(e6.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",children:(0,r.jsx)(eE.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-32 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:[(0,r.jsxs)("button",{onClick:()=>h(e),className:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center gap-2",children:[(0,r.jsx)(ed.A,{className:"h-3 w-3"}),"Edit"]}),(0,r.jsxs)("button",{onClick:()=>et(e.id),className:"w-full text-left px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center gap-2",children:[(0,r.jsx)(er.A,{className:"h-3 w-3"}),"Delete"]})]})]})]})]})]})]})]},e.id)})})}):(0,r.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:Q.map((e,a)=>{var t;return(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.includes(e.id),onChange:()=>ei(e.id),className:"mt-1 w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"}),(0,r.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center cursor-pointer",onClick:()=>ec(a),children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(k.A,{className:"h-3 w-3"}),new Date(e.date).toLocaleDateString("en-PH")]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(X.A,{className:"h-3 w-3"}),e.views," views"]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(e3.A,{className:"h-3 w-3"}),e.likes," likes"]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("family"===e.category?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400":"store"===e.category?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"events"===e.category?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"products"===e.category?"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400":"customers"===e.category?"bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400":"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"),children:null==(t=ex.find(a=>a.id===e.category))?void 0:t.label})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>ec(a),className:"text-gray-500 dark:text-gray-400 hover:text-amber-600 transition-colors",children:(0,r.jsx)(X.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>ee(e.id),className:"transition-colors ".concat(e.isLiked?"text-red-500":"text-gray-500 dark:text-gray-400 hover:text-red-500"),children:(0,r.jsx)(e3.A,{className:"h-4 w-4 ".concat(e.isLiked?"fill-current":"")})}),(0,r.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(e6.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>et(e.id),className:"text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors",children:(0,r.jsx)(er.A,{className:"h-4 w-4"})})]})]})})]})},e.id)})}),0===Q.length&&(0,r.jsxs)("div",{className:"p-16 text-center",children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(d.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Walang Photos na Nakita"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",children:"Try adjusting your search terms or filter criteria to find the photos you're looking for."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,r.jsx)("button",{onClick:()=>{S(""),L("all")},className:"btn-outline",children:"Clear Filters"}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"btn-primary",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Add First Photo"]})]})]})]}),x&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-3 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-xl shadow-lg",children:(0,r.jsx)(eM.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Add New Photo"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Upload a new photo to your family gallery"})]})]}),(0,r.jsx)("button",{onClick:()=>g(!1),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)($.A,{className:"h-5 w-5 text-gray-500"})})]})}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),J.file&&J.title&&(Y(!0),setTimeout(()=>{m([{id:Date.now().toString(),url:URL.createObjectURL(J.file),title:J.title,description:J.description,date:new Date().toISOString().split("T")[0]||"",likes:0,isLiked:!1,category:J.category,tags:J.tags.split(",").map(e=>e.trim()).filter(e=>e),views:0,isPrivate:J.isPrivate,uploadedBy:"Admin",fileSize:"".concat((J.file.size/1048576).toFixed(1)," MB"),dimensions:"1920x1080",location:J.location},...o]),Z({title:"",description:"",file:null,category:"family",tags:"",isPrivate:!1,location:""}),g(!1),Y(!1)},1500))},className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Photo File"}),(0,r.jsx)("div",{onDragOver:e=>{e.preventDefault()},onDrop:e=>{e.preventDefault();let a=e.dataTransfer.files[0];a&&a.type.startsWith("image/")&&Z({...J,file:a})},onClick:()=>{var e;return null==(e=K.current)?void 0:e.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-amber-400 dark:hover:border-amber-500 transition-colors",children:J.file?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-amber-600 mx-auto"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:J.file.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(J.file.size/1048576).toFixed(1)," MB"]}),(0,r.jsx)("button",{type:"button",onClick:e=>{e.stopPropagation(),Z({...J,file:null})},className:"text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",children:"Remove file"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eM.A,{className:"h-12 w-12 text-gray-400 mx-auto"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Click to upload or drag and drop"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)("input",{ref:K,type:"file",accept:"image/*",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];t&&Z({...J,file:t})},className:"hidden",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Title"}),(0,r.jsx)("input",{type:"text",value:J.title,onChange:e=>Z({...J,title:e.target.value}),placeholder:"Enter photo title...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,r.jsx)("select",{value:J.category,onChange:e=>Z({...J,category:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700",children:ex.slice(1).map(e=>(0,r.jsx)("option",{value:e.id,children:e.label},e.id))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Description"}),(0,r.jsx)("textarea",{value:J.description,onChange:e=>Z({...J,description:e.target.value}),rows:4,placeholder:"Describe your photo...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tags (comma separated)"}),(0,r.jsx)("input",{type:"text",value:J.tags,onChange:e=>Z({...J,tags:e.target.value}),placeholder:"family, celebration, milestone...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Location (optional)"}),(0,r.jsx)("input",{type:"text",value:J.location,onChange:e=>Z({...J,location:e.target.value}),placeholder:"Cebu City, Philippines...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"isPrivate",checked:J.isPrivate,onChange:e=>Z({...J,isPrivate:e.target.checked}),className:"w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"}),(0,r.jsx)("label",{htmlFor:"isPrivate",className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Make this photo private (only visible to family members)"})]}),(0,r.jsxs)("div",{className:"flex space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("button",{type:"button",onClick:()=>g(!1),className:"flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 font-medium transition-colors",disabled:G,children:"Cancel"}),(0,r.jsx)("button",{type:"submit",className:"flex-1 bg-gradient-to-r from-amber-600 to-yellow-700 hover:from-amber-700 hover:to-yellow-800 text-white px-6 py-3 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 shadow-lg",disabled:G,children:G?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(M.A,{className:"h-4 w-4 animate-spin"}),"Uploading..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eM.A,{className:"h-4 w-4"}),"Upload Photo"]})})]})]})]})}),b&&Q[f]&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"relative w-full h-full flex items-center justify-center p-4",children:[(0,r.jsx)("button",{onClick:()=>{p(!1),v(!1)},className:"absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:(0,r.jsx)($.A,{className:"h-6 w-6"})}),Q.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>{let e=0===f?Q.length-1:f-1;y(e);let a=Q[e];a&&ea(a.id)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:(0,r.jsx)(C.A,{className:"h-8 w-8"})}),(0,r.jsx)("button",{onClick:()=>{let e=(f+1)%Q.length;y(e);let a=Q[e];a&&ea(a.id)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:(0,r.jsx)(w.A,{className:"h-8 w-8"})})]}),(0,r.jsxs)("div",{className:"absolute top-4 left-4 z-10 flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>{v(!j)},className:"p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:j?(0,r.jsx)(e7.A,{className:"h-5 w-5"}):(0,r.jsx)(e9.A,{className:"h-5 w-5"})}),(0,r.jsxs)("span",{className:"text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full",children:[f+1," / ",Q.length]})]}),(0,r.jsx)("div",{className:"max-w-4xl max-h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center",style:{minWidth:"600px",minHeight:"400px"},children:[(0,r.jsx)(d.A,{className:"h-32 w-32 text-gray-400"}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:Q[f].title}),(0,r.jsx)("p",{className:"text-sm opacity-90 mb-3",children:Q[f].description}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(k.A,{className:"h-4 w-4"}),new Date(Q[f].date).toLocaleDateString("en-PH")]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(X.A,{className:"h-4 w-4"}),(null==(e=Q[f])?void 0:e.views)||0]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(e3.A,{className:"h-4 w-4"}),(null==(a=Q[f])?void 0:a.likes)||0]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>{let e=Q[f];e&&ee(e.id)},className:"p-2 rounded-full transition-all ".concat((null==(t=Q[f])?void 0:t.isLiked)?"bg-red-500 text-white":"bg-white bg-opacity-20 text-white hover:bg-opacity-30"),children:(0,r.jsx)(e3.A,{className:"h-4 w-4 ".concat((null==(s=Q[f])?void 0:s.isLiked)?"fill-current":"")})}),(0,r.jsx)("button",{className:"p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all",children:(0,r.jsx)(e6.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all",children:(0,r.jsx)(W.A,{className:"h-4 w-4"})})]})]})]})]})})]})}),u&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl shadow-lg ".concat("family"===u.category?"bg-blue-500":"store"===u.category?"bg-green-500":"events"===u.category?"bg-yellow-500":"products"===u.category?"bg-amber-600":"customers"===u.category?"bg-pink-500":"bg-orange-500"),children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Photo Details"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:u.title})]})]}),(0,r.jsx)("button",{onClick:()=>h(null),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)($.A,{className:"h-5 w-5 text-gray-500"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"aspect-video bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-24 w-24 text-gray-400"})}),(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("button",{onClick:()=>ee(u.id),className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-all ".concat(u.isLiked?"bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-red-50 hover:text-red-600"),children:[(0,r.jsx)(e3.A,{className:"h-4 w-4 ".concat(u.isLiked?"fill-current":"")}),u.likes]}),(0,r.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all",children:[(0,r.jsx)(e6.A,{className:"h-4 w-4"}),"Share"]}),(0,r.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all",children:[(0,r.jsx)(W.A,{className:"h-4 w-4"}),"Download"]})]})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Information"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Title"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:u.title})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Description"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:u.description||"No description provided"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Category"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:null==(n=ex.find(e=>e.id===u.category))?void 0:n.label})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:new Date(u.date).toLocaleDateString("en-PH")})]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Statistics"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)(X.A,{className:"h-5 w-5 text-blue-500 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:u.views}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Views"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)(e3.A,{className:"h-5 w-5 text-red-500 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:u.likes}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Likes"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)(e6.A,{className:"h-5 w-5 text-green-500 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"0"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Shares"})]})]})]}),u.tags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:u.tags.map(e=>(0,r.jsxs)("span",{className:"inline-flex px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-400 text-sm rounded-full",children:["#",e]},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Technical Details"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"File Size:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:u.fileSize})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Dimensions:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:u.dimensions})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Uploaded by:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:u.uploadedBy})]}),u.location&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Location:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:u.location})]})]})]})]})]})})]})})]})}let ae={calendar:"Kalendaryo",today:"Karon nga Adlaw",events:"Mga Panghitabo",schedule:"Iskedyul",moonPhases:"Mga Hugis sa Bulan",moonPhase:"Hugis sa Bulan",newMoon:"Bag-ong Bulan",waxingCrescent:"Nagdako nga Sungay",firstQuarter:"Una nga Bahin",waxingGibbous:"Nagdako nga Bula",fullMoon:"Puno nga Bulan",waningGibbous:"Nagliit nga Bula",lastQuarter:"Katapusan nga Bahin",waningCrescent:"Nagliit nga Sungay",newMoonDesc:"Ang bulan dili makita gikan sa yuta",waxingCrescentDesc:"Nipis nga sungay sa bulan sa tuo nga bahin",firstQuarterDesc:"Katunga sa bulan nag-hayag sa tuo nga bahin",waxingGibbousDesc:"Sobra sa katunga sa bulan nag-hayag",fullMoonDesc:"Tibuok nga bulan nag-hayag ug makita",waningGibbousDesc:"Sobra sa katunga nag-hayag, nagliit na",lastQuarterDesc:"Katunga sa bulan nag-hayag sa wala nga bahin",waningCrescentDesc:"Nipis nga sungay sa bulan sa wala nga bahin",addEvent:"Dugang Event",manage:"Pagdumala",upcoming:"Umaabot na",legend:"Giya",age:"Edad",illumination:"Kahayag",days:"mga adlaw",next:"Sunod",cancel:"Kanselar",description:"Deskripsyon",location:"Lugar",time:"Oras",date:"Petsa",title:"Titulo",type:"Klase",attendees:"Mga Apil",more:"pa",delivery:"Delivery",meeting:"Meeting",reminder:"Pahinumdom",holiday:"Holiday",personal:"Personal"},aa=()=>[{name:ae.newMoon,emoji:"\uD83C\uDF11",icon:"new-moon",illumination:0,description:ae.newMoonDesc},{name:ae.waxingCrescent,emoji:"\uD83C\uDF12",icon:"waxing-crescent",illumination:25,description:ae.waxingCrescentDesc},{name:ae.firstQuarter,emoji:"\uD83C\uDF13",icon:"first-quarter",illumination:50,description:ae.firstQuarterDesc},{name:ae.waxingGibbous,emoji:"\uD83C\uDF14",icon:"waxing-gibbous",illumination:75,description:ae.waxingGibbousDesc},{name:ae.fullMoon,emoji:"\uD83C\uDF15",icon:"full-moon",illumination:100,description:ae.fullMoonDesc},{name:ae.waningGibbous,emoji:"\uD83C\uDF16",icon:"waning-gibbous",illumination:75,description:ae.waningGibbousDesc},{name:ae.lastQuarter,emoji:"\uD83C\uDF17",icon:"last-quarter",illumination:50,description:ae.lastQuarterDesc},{name:ae.waningCrescent,emoji:"\uD83C\uDF18",icon:"waning-crescent",illumination:25,description:ae.waningCrescentDesc}],at=e=>{let a=new Date(2e3,0,6,18,14),t=(e.getTime()-a.getTime())/864e5%29.53058867,r=t<0?t+29.53058867:t,s=Math.round((1-Math.cos(r/29.53058867*2*Math.PI))*50),l=aa(),n=0;n=Math.max(0,Math.min(n=r<1.84566?0:r<5.53699?1:r<9.22831?2:r<12.91963?3:r<16.61096?4:r<20.30228?5:r<23.99361?6:7,l.length-1));let o=new Date(e.getTime()+(14.76529-r+29.53058867)%29.53058867*864e5),i=new Date(e.getTime()+(29.53058867-r)%29.53058867*864e5),d=l[n];if(!d){let e=l[0];if(!e)throw Error("No moon phases available");return{phase:e,age:Math.round(10*r)/10,illumination:s,nextFullMoon:o,nextNewMoon:i}}return{phase:d,age:Math.round(10*r)/10,illumination:s,nextFullMoon:o,nextNewMoon:i}},ar=e=>{let{phase:a,size:t=16,className:s=""}=e;return(0,r.jsx)("span",{className:"moon-phase-icon ".concat(s),style:{width:t,height:t,fontSize:t,display:"inline-block",lineHeight:1},title:"".concat(a.name," - ").concat(a.description),children:a.emoji})},as=e=>{let{moonData:a,className:t=""}=e;return(0,r.jsxs)("div",{className:"absolute z-10 p-3 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 min-w-48 ".concat(t),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(ar,{phase:a.phase,size:20}),(0,r.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:a.phase.name})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,r.jsx)("p",{children:a.phase.description}),(0,r.jsxs)("p",{children:[ae.age,": ",a.age," ",ae.days]}),(0,r.jsxs)("p",{children:[ae.illumination,": ",a.illumination,"%"]}),(0,r.jsxs)("p",{className:"text-xs pt-1 border-t border-gray-200 dark:border-gray-600",children:[ae.next," ",ae.fullMoon,": ",a.nextFullMoon.toLocaleDateString("tl-PH")]})]})]})};function al(){let[e,a]=(0,l.useState)(new Date),[t,s]=(0,l.useState)(null),[n,o]=(0,l.useState)(!1),[d,c]=(0,l.useState)(!0),[m,g]=(0,l.useState)(null),[u,h]=(0,l.useState)([{id:"1",title:"Supplier Delivery",description:"Weekly grocery delivery from main supplier",date:"2024-01-22",time:"09:00",type:"delivery",location:"Store Front"},{id:"2",title:"Monthly Inventory Check",description:"Complete inventory count and stock verification",date:"2024-01-25",time:"14:00",type:"reminder"},{id:"3",title:"Community Meeting",description:"Barangay business owners meeting",date:"2024-01-28",time:"16:00",type:"meeting",location:"Barangay Hall",attendees:["Maria Santos","Juan Dela Cruz","Ana Reyes"]},{id:"4",title:"New Year Holiday",description:"Store closed for New Year celebration",date:"2024-01-01",time:"00:00",type:"holiday"}]),[b,p]=(0,l.useState)({title:"",description:"",date:"",time:"",type:"reminder",location:""}),f=e=>{let a=e.toISOString().split("T")[0];return u.filter(e=>e.date===a)},y=e=>{switch(e){case"delivery":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"meeting":return"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400";case"reminder":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";case"holiday":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"personal":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},j=e=>{a(a=>{let t=new Date(a);return"prev"===e?t.setMonth(a.getMonth()-1):t.setMonth(a.getMonth()+1),t})},v=(e=>{let a=e.getFullYear(),t=e.getMonth(),r=new Date(a,t,1),s=new Date(a,t+1,0).getDate(),l=r.getDay(),n=[];for(let e=0;e<l;e++)n.push(null);for(let e=1;e<=s;e++)n.push(new Date(a,t,e));return n})(e),N=new Date;return(0,r.jsxs)("div",{className:"space-y-6 bisaya-calendar",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white cultural-accent bisaya-text",children:ae.calendar}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1 bisaya-text",children:[ae.manage," sa inyong store ",ae.events," ug ",ae.schedule," uban sa lunar phases"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>c(!d),className:"flex items-center px-3 py-2 rounded-lg border transition-all duration-200 hover:scale-105 ".concat(d?"bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/30 dark:border-blue-600 dark:text-blue-400 shadow-md":"border-gray-300 text-gray-600 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-slate-700"),children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),ae.moonPhases]}),(0,r.jsxs)("button",{onClick:()=>o(!0),className:"btn-primary flex items-center hover:scale-105 shadow-lg",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2"}),ae.addEvent]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[["Enero","Pebrero","Marso","Abril","Mayo","Hunyo","Hulyo","Agosto","Septyembre","Oktubre","Nobyembre","Disyembre"][e.getMonth()]," ",e.getFullYear()]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>j("prev"),className:"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700",children:(0,r.jsx)(C.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>a(new Date),className:"px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 shadow-sm",children:ae.today}),(0,r.jsx)("button",{onClick:()=>j("next"),className:"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-7 gap-1",children:[["Dom","Lun","Mar","Miy","Huw","Biy","Sab"].map(e=>(0,r.jsx)("div",{className:"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400",children:e},e)),v.map((e,a)=>{if(!e)return(0,r.jsx)("div",{className:"p-3 h-28"},a);let l=f(e),n=e.toDateString()===N.toDateString(),o=(null==t?void 0:t.toDateString())===e.toDateString(),i=at(e);return(0,r.jsxs)("div",{onClick:()=>s(e),className:"calendar-day-cell p-2 h-28 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 relative ".concat(n?"bg-green-50 dark:bg-green-900/20 ring-1 ring-green-300 dark:ring-green-600":""," ").concat(o?"ring-2 ring-green-500 bg-green-100 dark:bg-green-900/30":""),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium ".concat(n?"text-green-600 dark:text-green-400":"text-gray-900 dark:text-white"),children:e.getDate()}),d&&(0,r.jsx)("div",{className:"moon-phase-container relative",onMouseEnter:a=>{let t=a.currentTarget.getBoundingClientRect();g({date:e,moonData:i,position:{x:t.left,y:t.top}})},onMouseLeave:()=>g(null),children:(0,r.jsx)(ar,{phase:i.phase,size:14,className:"opacity-80 hover:opacity-100 transition-opacity duration-200"})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[l.slice(0,d?1:2).map(e=>(0,r.jsx)("div",{className:"text-xs px-1 py-0.5 rounded truncate ".concat(y(e.type)),children:e.title},e.id)),l.length>(d?1:2)&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",l.length-(d?1:2)," ",ae.more]})]})]},a)})]})]}),d&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 mr-2 text-blue-500"}),ae.moonPhase," Karon nga Adlaw"]}),(()=>{let e=at(N);return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)(ar,{phase:e.phase,size:48,className:"mx-auto"})}),(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:e.phase.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.phase.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:ae.age}),(0,r.jsxs)("div",{className:"text-gray-600 dark:text-gray-400",children:[e.age," ",ae.days]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:ae.illumination}),(0,r.jsxs)("div",{className:"text-gray-600 dark:text-gray-400",children:[e.illumination,"%"]})]})]})]})})()]}),(0,r.jsxs)("div",{className:"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text",children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83C\uDF19"}),ae.upcoming," nga Bulan ",ae.events]}),(()=>{let e=at(N);return(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-xl",children:"\uD83C\uDF15"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[ae.next," ",ae.fullMoon]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.nextFullMoon.toLocaleDateString("tl-PH",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})}),(0,r.jsx)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-xl",children:"\uD83C\uDF11"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[ae.next," ",ae.newMoon]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.nextNewMoon.toLocaleDateString("tl-PH",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})})]})})()]}),(0,r.jsxs)("div",{className:"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text",children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDCD6"}),ae.legend," sa ",ae.moonPhases]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-2",children:aa().map((e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:scale-102",children:[(0,r.jsx)(ar,{phase:e,size:18}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.illumination,"% ",ae.illumination]})]})]},a))})]})]}),m&&(0,r.jsx)("div",{className:"fixed pointer-events-none z-50",style:{left:m.position.x,top:m.position.y-10,transform:"translateY(-100%)"},children:(0,r.jsx)(as,{moonData:m.moonData})}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDCC5"}),ae.upcoming," nga ",ae.events]}),(0,r.jsx)("div",{className:"space-y-3",children:u.filter(e=>new Date(e.date)>=N).sort((e,a)=>new Date(e.date).getTime()-new Date(a.date).getTime()).slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(y(e.type)),children:(0,r.jsx)(k.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(P.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[new Date(e.date).toLocaleDateString()," at ",e.time]})]}),e.location&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(eY.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:e.location})]}),e.attendees&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(i.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[e.attendees.length," ",ae.attendees]})]})]})]})]},e.id))})]}),n&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md shadow-2xl animate-fade-in-up",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(E.A,{className:"h-5 w-5 mr-2 text-green-500"}),"Dugang Bag-ong ",ae.events]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),b.title&&b.date&&b.time&&(h([...u,{id:Date.now().toString(),...b}]),p({title:"",description:"",date:"",time:"",type:"reminder",location:""}),o(!1))},className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[ae.title," sa ",ae.events]}),(0,r.jsx)("input",{type:"text",value:b.title,onChange:e=>p({...b,title:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",placeholder:"I-type ang titulo sa event...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:ae.description}),(0,r.jsx)("textarea",{value:b.description,onChange:e=>p({...b,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",placeholder:"Detalye sa event..."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:ae.date}),(0,r.jsx)("input",{type:"date",value:b.date,onChange:e=>p({...b,date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:ae.time}),(0,r.jsx)("input",{type:"time",value:b.time,onChange:e=>p({...b,time:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[ae.type," sa ",ae.events]}),(0,r.jsxs)("select",{value:b.type,onChange:e=>p({...b,type:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",children:[(0,r.jsx)("option",{value:"reminder",children:ae.reminder}),(0,r.jsx)("option",{value:"delivery",children:ae.delivery}),(0,r.jsx)("option",{value:"meeting",children:ae.meeting}),(0,r.jsx)("option",{value:"holiday",children:ae.holiday}),(0,r.jsx)("option",{value:"personal",children:ae.personal})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[ae.location," (Optional)"]}),(0,r.jsx)("input",{type:"text",value:b.location,onChange:e=>p({...b,location:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",placeholder:"Asa ang event..."})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>o(!1),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 hover:scale-105",children:ae.cancel}),(0,r.jsx)("button",{type:"submit",className:"flex-1 btn-primary hover:scale-105 shadow-lg",children:ae.addEvent})]})]})]})})]})}var an=t(4213),ao=t(5525),ai=t(9022),ad=t(5339),ac=t(6517),am=t(4449),ax=t(6561),ag=t(5448);function au(){let[e,a]=(0,l.useState)(""),[t,s]=(0,l.useState)("all"),[n,i]=(0,l.useState)("7days"),[d,m]=(0,l.useState)([]),[x,u]=(0,l.useState)(!1),[h,b]=(0,l.useState)(null),[p,f]=(0,l.useState)(!1),[y,v]=(0,l.useState)("list"),[k,C]=(0,l.useState)("timestamp"),[A,D]=(0,l.useState)("desc"),[L,T]=(0,l.useState)(!1),[E,I]=(0,l.useState)(new Date),[F,z]=(0,l.useState)("online"),[U,B]=(0,l.useState)({types:[],priorities:[],statuses:[],dateRange:"7days",users:[],categories:[]}),q=(0,l.useMemo)(()=>[{id:"1",type:"product",action:"Product Added",description:'Added "Lucky Me Pancit Canton" to product list',user:"Admin",timestamp:"2024-01-20T10:30:00Z",priority:"medium",status:"success",category:"Inventory Management",tags:["product","inventory","add"],details:{productName:"Lucky Me Pancit Canton",price:15,sku:"LMC001",quantity:50,supplier:"Lucky Me Foods"}},{id:"2",type:"debt",action:"Debt Recorded",description:"New debt record for Juan Dela Cruz",user:"Admin",timestamp:"2024-01-20T09:15:00Z",priority:"high",status:"warning",category:"Customer Management",tags:["debt","customer","record"],details:{customer:"Juan Dela Cruz",amount:45,dueDate:"2024-02-20",contactNumber:"09*********",address:"Barangay San Jose, Cebu City"}},{id:"3",type:"payment",action:"Payment Received",description:"Payment received from Maria Santos",user:"Admin",timestamp:"2024-01-19T16:45:00Z",priority:"medium",status:"success",category:"Financial Transaction",tags:["payment","customer","income"],details:{customer:"Maria Santos",amount:120,paymentMethod:"Cash",previousBalance:200,newBalance:80}},{id:"4",type:"product",action:"Stock Updated",description:"Updated stock quantity for Coca-Cola",user:"Admin",timestamp:"2024-01-19T14:20:00Z",priority:"low",status:"success",category:"Inventory Management",tags:["product","stock","update"],details:{productName:"Coca-Cola",oldStock:25,newStock:50,reason:"New delivery received",supplier:"Coca-Cola Bottlers Philippines"}},{id:"5",type:"login",action:"User Login",description:"Admin user logged into the system",user:"Admin",timestamp:"2024-01-19T08:00:00Z",priority:"low",status:"info",category:"Security",tags:["login","security","access"],ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",details:{ipAddress:"*************",location:"Cebu City, Philippines",device:"Desktop - Windows 10",sessionDuration:"4 hours 30 minutes"}},{id:"6",type:"system",action:"Backup Created",description:"Automatic database backup completed",user:"System",timestamp:"2024-01-19T02:00:00Z",priority:"medium",status:"success",category:"System Maintenance",tags:["backup","database","maintenance"],details:{backupSize:"2.5MB",backupType:"Full Backup",location:"Cloud Storage",duration:"45 seconds",tablesBackedUp:8}},{id:"7",type:"debt",action:"Debt Updated",description:"Updated debt record for Ana Reyes",user:"Admin",timestamp:"2024-01-18T15:30:00Z",priority:"high",status:"warning",category:"Customer Management",tags:["debt","customer","update"],details:{customer:"Ana Reyes",oldAmount:75,newAmount:100,reason:"Additional purchase",dueDate:"2024-02-18",contactNumber:"09987654321"}},{id:"8",type:"product",action:"Product Deleted",description:'Removed "Expired Milk" from product list',user:"Admin",timestamp:"2024-01-18T11:10:00Z",priority:"high",status:"error",category:"Inventory Management",tags:["product","delete","expired"],details:{productName:"Expired Milk",reason:"Product expired",expiryDate:"2024-01-15",quantityRemoved:12,loss:180}},{id:"9",type:"security",action:"Failed Login Attempt",description:"Multiple failed login attempts detected",user:"Unknown",timestamp:"2024-01-18T03:45:00Z",priority:"critical",status:"error",category:"Security Alert",tags:["security","login","failed"],ipAddress:"*************",details:{attempts:5,ipAddress:"*************",location:"Unknown",blocked:!0,duration:"24 hours"}},{id:"10",type:"notification",action:"Low Stock Alert",description:"Stock level below minimum threshold for multiple products",user:"System",timestamp:"2024-01-17T18:00:00Z",priority:"high",status:"warning",category:"Inventory Alert",tags:["stock","alert","inventory"],details:{affectedProducts:["Rice 25kg","Cooking Oil 1L","Sugar 1kg"],minimumThreshold:10,currentStock:[5,3,7],recommendedOrder:[50,20,30]}}],[]);(0,l.useEffect)(()=>{let e;return L&&(e=setInterval(()=>{I(new Date),f(!0),setTimeout(()=>f(!1),1e3)},3e4)),()=>{e&&clearInterval(e)}},[L]),(0,l.useEffect)(()=>{let e=()=>z("online"),a=()=>z("offline");return window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a)}},[]);let H=e=>{switch(e){case"product":return(0,r.jsx)(o.A,{className:"h-4 w-4"});case"debt":return(0,r.jsx)(S.A,{className:"h-4 w-4 text-red-500"});case"payment":return(0,r.jsx)(S.A,{className:"h-4 w-4 text-green-500"});case"login":return(0,r.jsx)(g.A,{className:"h-4 w-4"});case"system":return(0,r.jsx)(an.A,{className:"h-4 w-4"});case"security":return(0,r.jsx)(ao.A,{className:"h-4 w-4"});case"backup":return(0,r.jsx)(ai.A,{className:"h-4 w-4"});case"notification":return(0,r.jsx)(O.A,{className:"h-4 w-4"});default:return(0,r.jsx)(_.A,{className:"h-4 w-4"})}},V=e=>{switch(e){case"critical":return(0,r.jsx)(ad.A,{className:"h-4 w-4 text-red-600"});case"high":return(0,r.jsx)(R.A,{className:"h-4 w-4 text-orange-500"});case"medium":return(0,r.jsx)(P.A,{className:"h-4 w-4 text-yellow-500"});case"low":return(0,r.jsx)(eR.A,{className:"h-4 w-4 text-blue-500"});default:return(0,r.jsx)(P.A,{className:"h-4 w-4 text-gray-500"})}},Y=e=>{switch(e){case"product":return"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800";case"debt":return"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800";case"payment":return"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800";case"login":return"bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 border border-purple-200 dark:border-purple-800";case"system":default:return"bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800";case"security":return"bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800";case"backup":return"bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800";case"notification":return"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800"}},K=e=>{switch(e){case"success":return"bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800";case"warning":return"bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800";case"error":return"bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800";case"info":return"bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800";default:return"bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800"}},J=e=>{switch(e){case"critical":return"bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700";case"high":return"bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-700";case"medium":return"bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-700";case"low":return"bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700";default:return"bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400 dark:border-gray-700"}},Z=(0,l.useMemo)(()=>{let a=q.filter(a=>{let r=""===e||a.description.toLowerCase().includes(e.toLowerCase())||a.action.toLowerCase().includes(e.toLowerCase())||a.user.toLowerCase().includes(e.toLowerCase())||a.category.toLowerCase().includes(e.toLowerCase())||a.tags&&a.tags.some(a=>a.toLowerCase().includes(e.toLowerCase())),s="all"===t||a.type===t,l=0===U.types.length||U.types.includes(a.type),o=0===U.priorities.length||U.priorities.includes(a.priority),i=0===U.statuses.length||U.statuses.includes(a.status),d=0===U.users.length||U.users.includes(a.user),c=0===U.categories.length||U.categories.includes(a.category),m=new Date(a.timestamp),x=new Date,g=!0;switch(n){case"24hours":g=x.getTime()-m.getTime()<=864e5;break;case"7days":g=x.getTime()-m.getTime()<=6048e5;break;case"30days":g=x.getTime()-m.getTime()<=2592e6;break;case"90days":g=x.getTime()-m.getTime()<=7776e6;break;case"all":g=!0}return r&&s&&l&&o&&i&&d&&c&&g});return a.sort((e,a)=>{let t=0;switch(k){case"timestamp":t=new Date(e.timestamp).getTime()-new Date(a.timestamp).getTime();break;case"type":t=e.type.localeCompare(a.type);break;case"priority":let r={critical:4,high:3,medium:2,low:1};t=(r[e.priority]||0)-(r[a.priority]||0)}return"desc"===A?-t:t}),a},[q,e,t,U,n,k,A]),Q=[...new Set(q.map(e=>e.priority))],es=[...new Set(q.map(e=>e.status))],el=[...new Set(q.map(e=>e.user))],en=e=>{let a=new Date(e),t=Math.floor((new Date().getTime()-a.getTime())/6e4),r=Math.floor(t/60),s=Math.floor(r/24);return t<1?"Just now":t<60?"".concat(t," minute").concat(t>1?"s":""," ago"):r<24?"".concat(r," hour").concat(r>1?"s":""," ago"):s<7?"".concat(s," day").concat(s>1?"s":""," ago"):a.toLocaleDateString("en-PH",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},eo=e=>{m(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},ei=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Z,a=new Blob([[["Timestamp","Type","Action","Description","User","Priority","Status","Category"],...e.map(e=>[e.timestamp,e.type,e.action,e.description,e.user,e.priority,e.status,e.category])].map(e=>e.join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(a),r=document.createElement("a");r.href=t,r.download="revantad-store-history-".concat(new Date().toISOString().split("T")[0],".csv"),r.click(),window.URL.revokeObjectURL(t)},ed=(0,l.useMemo)(()=>{let e={total:Z.length,byType:{},byPriority:{},byStatus:{},byUser:{},todayCount:0,weekCount:0},a=new Date,t=new Date(a.getFullYear(),a.getMonth(),a.getDate()),r=new Date(a.getTime()-6048e5);return Z.forEach(a=>{e.byType[a.type]=(e.byType[a.type]||0)+1,e.byPriority[a.priority]=(e.byPriority[a.priority]||0)+1,e.byStatus[a.status]=(e.byStatus[a.status]||0)+1,e.byUser[a.user]=(e.byUser[a.user]||0)+1;let s=new Date(a.timestamp);s>=t&&e.todayCount++,s>=r&&e.weekCount++}),e},[Z]);return(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg",children:(0,r.jsx)(_.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Activity History"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Comprehensive tracking sa lahat ng activities sa inyong tindahan - makita ang lahat ng nangyari"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-blue-600 dark:text-blue-400",children:"Total Activities"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-900 dark:text-blue-300",children:ed.total})]}),(0,r.jsx)(j.A,{className:"h-5 w-5 text-blue-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-green-600 dark:text-green-400",children:"Today"}),(0,r.jsx)("p",{className:"text-lg font-bold text-green-900 dark:text-green-300",children:ed.todayCount})]}),(0,r.jsx)(G.A,{className:"h-5 w-5 text-green-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-yellow-600 dark:text-yellow-400",children:"This Week"}),(0,r.jsx)("p",{className:"text-lg font-bold text-yellow-900 dark:text-yellow-300",children:ed.weekCount})]}),(0,r.jsx)(P.A,{className:"h-5 w-5 text-yellow-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-purple-600 dark:text-purple-400",children:"Selected"}),(0,r.jsx)("p",{className:"text-lg font-bold text-purple-900 dark:text-purple-300",children:d.length})]}),(0,r.jsx)(ea.A,{className:"h-5 w-5 text-purple-500"})]})})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium ".concat("online"===F?"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"),children:["online"===F?(0,r.jsx)(ac.A,{className:"h-4 w-4"}):(0,r.jsx)(am.A,{className:"h-4 w-4"}),"online"===F?"Online":"Offline"]}),(0,r.jsxs)("button",{onClick:()=>T(!L),className:"flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(L?"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400":"bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"),children:[(0,r.jsx)(ax.A,{className:"h-4 w-4 ".concat(L?"animate-pulse":"")}),"Auto Refresh"]}),(0,r.jsxs)("button",{onClick:()=>f(!p),className:"btn-outline flex items-center gap-2 text-sm",disabled:p,children:[(0,r.jsx)(M.A,{className:"h-4 w-4 ".concat(p?"animate-spin":"")}),"Refresh"]}),d.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>{ei(Z.filter(e=>d.includes(e.id)))},className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(W.A,{className:"h-4 w-4"}),"Export Selected"]}),(0,r.jsxs)("button",{onClick:()=>{d.length>0&&m([])},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(er.A,{className:"h-4 w-4"}),"Delete Selected"]})]}),(0,r.jsxs)("button",{onClick:()=>ei(),className:"btn-primary flex items-center gap-2 text-sm",children:[(0,r.jsx)(W.A,{className:"h-4 w-4"}),"Export All"]})]})]}),(0,r.jsxs)("div",{className:"card p-6 border-l-4 border-l-green-500",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-green-500"}),"Search & Filters"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>u(!x),className:"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors",children:[x?(0,r.jsx)(ee.A,{className:"h-4 w-4"}):(0,r.jsx)(w.A,{className:"h-4 w-4"}),"Advanced Filters"]}),(0,r.jsx)("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:["list","timeline","grid"].map(e=>(0,r.jsx)("button",{onClick:()=>v(e),className:"px-3 py-1 text-xs font-medium rounded-md transition-all ".concat(y===e?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search activities, users, descriptions...",value:e,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm"})]}),(0,r.jsxs)("select",{value:t,onChange:e=>s(e.target.value),className:"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"all",children:"All Types"}),(0,r.jsx)("option",{value:"product",children:"Product Activities"}),(0,r.jsx)("option",{value:"debt",children:"Debt Activities"}),(0,r.jsx)("option",{value:"payment",children:"Payment Activities"}),(0,r.jsx)("option",{value:"login",children:"Login Activities"}),(0,r.jsx)("option",{value:"system",children:"System Activities"}),(0,r.jsx)("option",{value:"security",children:"Security Activities"}),(0,r.jsx)("option",{value:"notification",children:"Notifications"})]}),(0,r.jsxs)("select",{value:n,onChange:e=>i(e.target.value),className:"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"24hours",children:"Last 24 hours"}),(0,r.jsx)("option",{value:"7days",children:"Last 7 days"}),(0,r.jsx)("option",{value:"30days",children:"Last 30 days"}),(0,r.jsx)("option",{value:"90days",children:"Last 90 days"}),(0,r.jsx)("option",{value:"all",children:"All time"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:k,onChange:e=>C(e.target.value),className:"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"timestamp",children:"Sort by Time"}),(0,r.jsx)("option",{value:"type",children:"Sort by Type"}),(0,r.jsx)("option",{value:"priority",children:"Sort by Priority"})]}),(0,r.jsx)("button",{onClick:()=>D("asc"===A?"desc":"asc"),className:"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Sort ".concat("asc"===A?"Descending":"Ascending"),children:"asc"===A?"↑":"↓"})]})]}),x&&(0,r.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Priority Levels"}),(0,r.jsx)("div",{className:"space-y-2",children:Q.map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.priorities.includes(e),onChange:a=>{a.target.checked?B(a=>({...a,priorities:[...a.priorities,e]})):B(a=>({...a,priorities:a.priorities.filter(a=>a!==e)}))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize flex items-center gap-1",children:[V(e),e]})]},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status Types"}),(0,r.jsx)("div",{className:"space-y-2",children:es.map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.statuses.includes(e),onChange:a=>{a.target.checked?B(a=>({...a,statuses:[...a.statuses,e]})):B(a=>({...a,statuses:a.statuses.filter(a=>a!==e)}))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 rounded-full text-xs font-medium ".concat(K(e)),children:e})]},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Users"}),(0,r.jsx)("div",{className:"space-y-2",children:el.map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.users.includes(e),onChange:a=>{a.target.checked?B(a=>({...a,users:[...a.users,e]})):B(a=>({...a,users:a.users.filter(a=>a!==e)}))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),e]})]},e))})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("button",{onClick:()=>{B({types:[],priorities:[],statuses:[],dateRange:"7days",users:[],categories:[]}),a(""),s("all"),i("7days")},className:"text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1 transition-colors",children:[(0,r.jsx)($.A,{className:"h-4 w-4"}),"Clear All Filters"]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-blue-500"}),"Activity Distribution"]}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(ed.byType).map(e=>{let[a,t]=e,s=(t/ed.total*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"p-1.5 rounded-lg ".concat(Y(a)),children:H(a)}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(s,"%")}})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white w-8",children:t}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-10",children:[s,"%"]})]})]},a)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)(ad.A,{className:"h-5 w-5 text-orange-500"}),"Priority Analysis"]}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(ed.byPriority).map(e=>{let[a,t]=e,s=(t/ed.total*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[V(a),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat("critical"===a?"bg-red-500":"high"===a?"bg-orange-500":"medium"===a?"bg-yellow-500":"bg-blue-500"),style:{width:"".concat(s,"%")}})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white w-8",children:t}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-10",children:[s,"%"]})]})]},a)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-green-500"}),"User Activity"]}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(ed.byUser).map(e=>{let[a,t]=e,s=(t/ed.total*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-xs font-bold",children:a.charAt(0).toUpperCase()})}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(s,"%")}})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white w-8",children:t}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-10",children:[s,"%"]})]})]},a)})})]})]}),(0,r.jsxs)("div",{className:"card overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(eK.A,{className:"h-5 w-5 text-green-500"}),"Activity Timeline (",Z.length,")"]}),Z.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>{d.length===Z.length?m([]):m(Z.map(e=>e.id))},className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors",children:[d.length===Z.length?(0,r.jsx)(ea.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(et.A,{className:"h-4 w-4"}),"Select All"]}),d.length>0&&(0,r.jsxs)("span",{className:"text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded-full",children:[d.length," selected"]})]})]}),p&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 animate-spin"}),"Loading..."]})]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto main-content-scroll",children:Z.map((e,a)=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group ".concat(d.includes(e.id)?"bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500":""),style:{animationDelay:"".concat(50*a,"ms")},children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("button",{onClick:()=>eo(e.id),className:"mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:d.includes(e.id)?(0,r.jsx)(ea.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(et.A,{className:"h-4 w-4 text-gray-400 hover:text-gray-600"})}),(0,r.jsx)("div",{className:"p-3 rounded-xl shadow-sm ".concat(Y(e.type)," transition-all group-hover:scale-105"),children:H(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-4 mb-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:e.action}),(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ".concat(J(e.priority)),children:[V(e.priority),e.priority]}),(0,r.jsx)("span",{className:"inline-flex px-2 py-1 rounded-full text-xs font-medium ".concat(K(e.status)),children:e.status})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed",children:e.description}),e.tags&&e.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:e.tags.map(e=>(0,r.jsxs)("span",{className:"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md",children:["#",e]},e))})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400 font-medium",children:en(e.timestamp)})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),e.user]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(N.A,{className:"h-3 w-3"}),e.category]}),e.ipAddress&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(ao.A,{className:"h-3 w-3"}),e.ipAddress]})]}),e.details&&(0,r.jsxs)("button",{onClick:()=>b(e),className:"text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 font-medium flex items-center gap-1 transition-colors",children:[(0,r.jsx)(X.A,{className:"h-3 w-3"}),"View Details"]})]})]})]})},e.id))}),0===Z.length&&(0,r.jsxs)("div",{className:"p-16 text-center",children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(_.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Walang Activities na Nakita"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",children:"Try i-adjust ang inyong search terms o filter criteria para makita ang activities na inyong gipangita."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,r.jsx)("button",{onClick:()=>{a(""),s("all"),B({types:[],priorities:[],statuses:[],dateRange:"7days",users:[],categories:[]})},className:"btn-outline",children:"Clear Filters"}),(0,r.jsxs)("button",{onClick:()=>f(!p),className:"btn-primary",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Refresh Data"]})]})]})]}),h&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl ".concat(Y(h.type)),children:H(h.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Activity Details"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:h.action})]})]}),(0,r.jsx)("button",{onClick:()=>b(null),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)($.A,{className:"h-5 w-5 text-gray-500"})})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Activity Type"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ".concat(Y(h.type)),children:[H(h.type),h.type]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Priority Level"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ".concat(J(h.priority)),children:[V(h.priority),h.priority]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Status"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsx)("span",{className:"inline-flex px-3 py-1 rounded-full text-sm font-medium ".concat(K(h.status)),children:h.status})})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"User"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),h.user]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Category"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),h.category]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Timestamp"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1",children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),new Date(h.timestamp).toLocaleString("en-PH")]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Description"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:h.description})]}),h.tags&&h.tags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:h.tags.map(e=>(0,r.jsxs)("span",{className:"inline-flex px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 text-sm rounded-full",children:["#",e]},e))})]}),h.details&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Additional Details"}),(0,r.jsx)("div",{className:"mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:(0,r.jsx)("pre",{className:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap font-mono",children:JSON.stringify(h.details,null,2)})})]}),(h.ipAddress||h.userAgent)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Security Information"}),(0,r.jsxs)("div",{className:"mt-2 space-y-2",children:[h.ipAddress&&(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(ao.A,{className:"h-4 w-4 text-orange-500"}),(0,r.jsx)("span",{className:"font-medium",children:"IP Address:"}),h.ipAddress]}),h.userAgent&&(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:"User Agent:"}),(0,r.jsx)("span",{className:"truncate",children:h.userAgent})]})]})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50",children:(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:()=>b(null),className:"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:"Close"}),(0,r.jsxs)("button",{onClick:()=>{ei([h]),b(null)},className:"btn-primary flex items-center gap-2",children:[(0,r.jsx)(W.A,{className:"h-4 w-4"}),"Export This Activity"]})]})})]})}),(0,r.jsx)("div",{className:"card p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-t-2 border-t-green-500",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full animate-pulse ".concat(L?"bg-green-500":"bg-gray-400")}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:L?"Real-time Updates Active":"Real-time Updates Paused"}),p&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:"Updating..."})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Last updated: ",E.toLocaleTimeString("en-PH")]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Next update: ",L?"30s":"Manual"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(ag.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Total: ",Z.length," activities"]})]})]})]})})]})}var ah=t(5937),ab=t(3127),ap=t(8883),af=t(6767),ay=t(4869),aj=t(4940),av=t(9803),ak=t(8749),aN=t(5196),aw=t(4738),aC=t(2417),aA=t(3500),aS=t(2970),aD=t(5880),a_=t(4229),aP=t(408);function aM(){let[e,a]=(0,l.useState)("store"),[t,s]=(0,l.useState)(!1),n=(0,l.useRef)(null),{settings:o,updateSettings:i,saveSettings:d,isLoading:c,hasUnsavedChanges:u}=(0,aP.t)(),h=[{id:"store",label:"Store Info",icon:ah.A},{id:"profile",label:"Profile",icon:g.A},{id:"notifications",label:"Notifications",icon:O.A},{id:"security",label:"Security",icon:ao.A},{id:"appearance",label:"Appearance",icon:ab.A},{id:"backup",label:"Backup",icon:an.A}],b=e=>i("store",e),p=e=>i("profile",e),f=e=>i("notifications",e),y=e=>i("security",e),j=e=>i("appearance",e),v=e=>i("backup",e),k=async()=>{try{await d(),alert("Settings saved successfully!")}catch(e){console.error("Error saving settings:",e),alert("Error saving settings. Please try again.")}},N=()=>{console.warn("Exporting data..."),alert("Data export started. You will receive an email when ready.")},w=()=>{console.warn("Importing data..."),alert("Please select a backup file to import.")},C=(e,a)=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){let e=new FileReader;e.onload=e=>{var t;let r=null==(t=e.target)?void 0:t.result;"logo"===a?b({branding:{...o.store.branding,logo:r}}):"avatar"===a&&p({avatar:r})},e.readAsDataURL(r)}},A=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Personal Information"]}),(0,r.jsxs)("div",{className:"flex items-start space-x-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,r.jsx)("div",{className:"w-24 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center bg-gray-50 dark:bg-slate-700 overflow-hidden",children:o.profile.avatar?(0,r.jsx)(ec.default,{src:o.profile.avatar,alt:"Profile Avatar",width:96,height:96,className:"w-full h-full object-cover"}):(0,r.jsx)(g.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=e=>{e&&e.target&&C(e,"avatar")},e.click()},className:"btn-outline text-sm px-3 py-1",children:"Upload Photo"}),o.profile.avatar&&(0,r.jsx)("button",{onClick:()=>p({avatar:null}),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]})]}),(0,r.jsxs)("div",{className:"flex-1 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,r.jsx)("input",{type:"text",value:o.profile.firstName,onChange:e=>p({firstName:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter first name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,r.jsx)("input",{type:"text",value:o.profile.lastName,onChange:e=>p({lastName:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter last name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ap.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:o.profile.email,onChange:e=>p({email:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eW.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:o.profile.phone,onChange:e=>p({phone:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Role"}),(0,r.jsxs)("select",{value:o.profile.role,onChange:e=>p({role:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"Store Owner",children:"Store Owner"}),(0,r.jsx)("option",{value:"Manager",children:"Manager"}),(0,r.jsx)("option",{value:"Cashier",children:"Cashier"}),(0,r.jsx)("option",{value:"Staff",children:"Staff"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Date of Birth"}),(0,r.jsx)("input",{type:"date",value:o.profile.dateOfBirth,onChange:e=>p({dateOfBirth:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Bio"}),(0,r.jsx)("textarea",{value:o.profile.bio,onChange:e=>p({bio:e.target.value}),rows:3,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Tell us about yourself..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eY.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("textarea",{value:o.profile.address,onChange:e=>p({address:e.target.value}),rows:2,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter your address"})]})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Emergency Contact"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Contact Name"}),(0,r.jsx)("input",{type:"text",value:o.profile.emergencyContact.name,onChange:e=>p({emergencyContact:{...o.profile.emergencyContact,name:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Emergency contact name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eW.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:o.profile.emergencyContact.phone,onChange:e=>p({emergencyContact:{...o.profile.emergencyContact,phone:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Relationship"}),(0,r.jsxs)("select",{value:o.profile.emergencyContact.relationship,onChange:e=>p({emergencyContact:{...o.profile.emergencyContact,relationship:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"Family",children:"Family"}),(0,r.jsx)("option",{value:"Friend",children:"Friend"}),(0,r.jsx)("option",{value:"Colleague",children:"Colleague"}),(0,r.jsx)("option",{value:"Other",children:"Other"})]})]})]})]})]}),S=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(O.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Alert Preferences"]}),(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(o.notifications).filter(e=>{let[a]=e;return!["channels","customRules","templates"].includes(a)}).map(e=>{let[a,t]=e;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:a.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["lowStock"===a&&"Get notified when products are running low","newDebt"===a&&"Alert when new customer debt is recorded","paymentReceived"===a&&"Notification for debt payments","dailyReport"===a&&"Daily business summary report","weeklyReport"===a&&"Weekly business analytics report","emailNotifications"===a&&"Receive notifications via email","smsNotifications"===a&&"Receive notifications via SMS","pushNotifications"===a&&"Receive push notifications in browser"]})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer ml-4",children:[(0,r.jsx)("input",{type:"checkbox",checked:t,onChange:e=>f({[a]:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]},a)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ap.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Delivery Channels"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ap.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:o.notifications.channels.email,onChange:e=>f({channels:{...o.notifications.channels,email:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"SMS Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(af.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:o.notifications.channels.sms,onChange:e=>f({channels:{...o.notifications.channels,sms:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Webhook URL (Optional)"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ay.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"url",value:o.notifications.channels.webhook,onChange:e=>f({channels:{...o.notifications.channels,webhook:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"https://your-webhook-url.com/notifications"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Send notifications to external systems via webhook"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(R.A,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Custom Rules"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[o.notifications.customRules.map(e=>(0,r.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.enabled?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"),children:e.enabled?"Active":"Inactive"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700 p-1",children:(0,r.jsx)(aj.A,{className:"h-4 w-4"})}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:e.enabled,onChange:a=>{f({customRules:o.notifications.customRules.map(t=>t.id===e.id?{...t,enabled:a.target.checked}:t)})},className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Condition:"})," ",e.condition]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Action:"})," ",e.action]})]})]},e.id)),(0,r.jsxs)("button",{className:"w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-green-500 hover:text-green-600 transition-colors flex items-center justify-center space-x-2",children:[(0,r.jsx)(E.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Add Custom Rule"})]})]})]})]}),_=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(av.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Password Management"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:t?"text":"password",value:o.security.currentPassword,onChange:e=>y({currentPassword:e.target.value}),className:"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter current password"}),(0,r.jsx)("button",{type:"button",onClick:()=>s(!t),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:t?(0,r.jsx)(ak.A,{className:"h-5 w-5"}):(0,r.jsx)(X.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"New Password"}),(0,r.jsx)("input",{type:t?"text":"password",value:o.security.newPassword,onChange:e=>y({newPassword:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter new password"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Confirm New Password"}),(0,r.jsx)("input",{type:t?"text":"password",value:o.security.confirmPassword,onChange:e=>y({confirmPassword:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Confirm new password"})]})]}),(0,r.jsx)("button",{className:"btn-primary w-fit",children:"Update Password"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(af.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Two-Factor Authentication"]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Enable 2FA"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Add an extra layer of security to your account"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:o.security.twoFactorAuth,onChange:e=>y({twoFactorAuth:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]}),o.security.twoFactorAuth&&(0,r.jsxs)("div",{className:"mt-4 p-4 border border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsx)(aN.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsx)("span",{className:"font-medium text-green-800 dark:text-green-300",children:"2FA is enabled"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"btn-outline text-sm",children:"View Recovery Codes"}),(0,r.jsx)("button",{className:"btn-outline text-sm text-red-600 border-red-300 hover:bg-red-50",children:"Disable 2FA"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(P.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Session Management"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Session Timeout (minutes)"}),(0,r.jsxs)("select",{value:o.security.sessionTimeout,onChange:e=>y({sessionTimeout:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"15",children:"15 minutes"}),(0,r.jsx)("option",{value:"30",children:"30 minutes"}),(0,r.jsx)("option",{value:"60",children:"1 hour"}),(0,r.jsx)("option",{value:"120",children:"2 hours"}),(0,r.jsx)("option",{value:"480",children:"8 hours"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Password Expiry (days)"}),(0,r.jsxs)("select",{value:o.security.passwordExpiry,onChange:e=>y({passwordExpiry:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"30",children:"30 days"}),(0,r.jsx)("option",{value:"60",children:"60 days"}),(0,r.jsx)("option",{value:"90",children:"90 days"}),(0,r.jsx)("option",{value:"180",children:"180 days"}),(0,r.jsx)("option",{value:"365",children:"1 year"}),(0,r.jsx)("option",{value:"0",children:"Never"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Max Login Attempts"}),(0,r.jsxs)("select",{value:o.security.loginAttempts,onChange:e=>y({loginAttempts:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"3",children:"3 attempts"}),(0,r.jsx)("option",{value:"5",children:"5 attempts"}),(0,r.jsx)("option",{value:"10",children:"10 attempts"}),(0,r.jsx)("option",{value:"0",children:"Unlimited"})]})]})]})]})]}),L=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(aw.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Theme Selection"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"light",name:"Light",icon:m.A,preview:"bg-white border-gray-200"},{id:"dark",name:"Dark",icon:x.A,preview:"bg-slate-800 border-slate-600"},{id:"auto",name:"Auto",icon:aw.A,preview:"bg-gradient-to-r from-white to-slate-800 border-gray-400"}].map(e=>{let a=e.icon;return(0,r.jsxs)("label",{className:"cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",name:"theme",value:e.id,checked:o.appearance.theme===e.id,onChange:e=>j({theme:e.target.value}),className:"sr-only"}),(0,r.jsxs)("div",{className:"p-4 border-2 rounded-lg transition-all ".concat(o.appearance.theme===e.id?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:[(0,r.jsx)("div",{className:"w-full h-20 rounded-md mb-3 border ".concat(e.preview)}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(a,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:e.name})]})]})]},e.id)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ab.A,{className:"h-5 w-5 mr-2 text-pink-600"}),"Color Scheme"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.entries(o.appearance.colorScheme).map(e=>{let[a,t]=e;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize",children:[a," Color"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"color",value:t,onChange:e=>j({colorScheme:{...o.appearance.colorScheme,[a]:e.target.value}}),className:"w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>j({colorScheme:{...o.appearance.colorScheme,[a]:e.target.value}}),className:"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"#000000"})]})]},a)})}),(0,r.jsxs)("div",{className:"mt-6 flex space-x-3",children:[(0,r.jsx)("button",{className:"btn-outline",children:"Reset to Default"}),(0,r.jsx)("button",{className:"btn-outline",children:"Preview Changes"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(aC.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Layout Preferences"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Sidebar Position"}),(0,r.jsxs)("select",{value:o.appearance.layout.sidebarPosition,onChange:e=>j({layout:{...o.appearance.layout,sidebarPosition:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"left",children:"Left"}),(0,r.jsx)("option",{value:"right",children:"Right"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"UI Density"}),(0,r.jsxs)("select",{value:o.appearance.layout.density,onChange:e=>j({layout:{...o.appearance.layout,density:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"compact",children:"Compact"}),(0,r.jsx)("option",{value:"comfortable",children:"Comfortable"}),(0,r.jsx)("option",{value:"spacious",children:"Spacious"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:o.appearance.layout.showAnimations,onChange:e=>j({layout:{...o.appearance.layout,showAnimations:e.target.checked}}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Enable animations"})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:o.appearance.layout.compactMode,onChange:e=>j({layout:{...o.appearance.layout,compactMode:e.target.checked}}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Compact mode"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(aA.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Typography"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Font Family"}),(0,r.jsxs)("select",{value:o.appearance.typography.fontFamily,onChange:e=>j({typography:{...o.appearance.typography,fontFamily:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"Inter",children:"Inter"}),(0,r.jsx)("option",{value:"Roboto",children:"Roboto"}),(0,r.jsx)("option",{value:"Open Sans",children:"Open Sans"}),(0,r.jsx)("option",{value:"Poppins",children:"Poppins"}),(0,r.jsx)("option",{value:"Lato",children:"Lato"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Font Size"}),(0,r.jsxs)("select",{value:o.appearance.typography.fontSize,onChange:e=>j({typography:{...o.appearance.typography,fontSize:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"small",children:"Small"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"large",children:"Large"}),(0,r.jsx)("option",{value:"extra-large",children:"Extra Large"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Font Weight"}),(0,r.jsxs)("select",{value:o.appearance.typography.fontWeight,onChange:e=>j({typography:{...o.appearance.typography,fontWeight:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"light",children:"Light"}),(0,r.jsx)("option",{value:"normal",children:"Normal"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"semibold",children:"Semibold"}),(0,r.jsx)("option",{value:"bold",children:"Bold"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Preview"}),(0,r.jsx)("div",{className:"text-gray-700 dark:text-gray-300",style:{fontFamily:o.appearance.typography.fontFamily,fontSize:"small"===o.appearance.typography.fontSize?"14px":"medium"===o.appearance.typography.fontSize?"16px":"large"===o.appearance.typography.fontSize?"18px":"20px",fontWeight:o.appearance.typography.fontWeight},children:"The quick brown fox jumps over the lazy dog. This is how your text will appear with the selected typography settings."})]})]})]}),T=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(an.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Backup Configuration"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Auto Backup"}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:o.backup.autoBackup,onChange:e=>v({autoBackup:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700 dark:text-gray-300",children:"Enable automatic backups"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Backup Frequency"}),(0,r.jsxs)("select",{value:o.backup.backupFrequency,onChange:e=>v({backupFrequency:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",disabled:!o.backup.autoBackup,children:[(0,r.jsx)("option",{value:"hourly",children:"Every Hour"}),(0,r.jsx)("option",{value:"daily",children:"Daily"}),(0,r.jsx)("option",{value:"weekly",children:"Weekly"}),(0,r.jsx)("option",{value:"monthly",children:"Monthly"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Retention Period (Days)"}),(0,r.jsx)("input",{type:"number",min:"1",max:"365",value:o.backup.retentionDays,onChange:e=>v({retentionDays:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Next Backup"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-slate-700 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:o.backup.autoBackup?"Tomorrow at 2:00 AM":"Manual backup only"})})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(aS.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Cloud Storage"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Storage Provider"}),(0,r.jsxs)("select",{value:o.backup.cloudStorage.provider,onChange:e=>v({cloudStorage:{...o.backup.cloudStorage,provider:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"local",children:"Local Storage"}),(0,r.jsx)("option",{value:"aws",children:"Amazon S3"}),(0,r.jsx)("option",{value:"google",children:"Google Cloud"}),(0,r.jsx)("option",{value:"azure",children:"Azure Blob"}),(0,r.jsx)("option",{value:"dropbox",children:"Dropbox"})]})]}),"local"!==o.backup.cloudStorage.provider&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Bucket/Container Name"}),(0,r.jsx)("input",{type:"text",value:o.backup.cloudStorage.bucket,onChange:e=>v({cloudStorage:{...o.backup.cloudStorage,bucket:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"my-backup-bucket"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Access Key"}),(0,r.jsx)("input",{type:"text",value:o.backup.cloudStorage.accessKey,onChange:e=>v({cloudStorage:{...o.backup.cloudStorage,accessKey:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Your access key"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Secret Key"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:t?"text":"password",value:o.backup.cloudStorage.secretKey,onChange:e=>v({cloudStorage:{...o.backup.cloudStorage,secretKey:e.target.value}}),className:"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Your secret key"}),(0,r.jsx)("button",{type:"button",onClick:()=>s(!t),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:t?(0,r.jsx)(ak.A,{className:"h-5 w-5"}):(0,r.jsx)(X.A,{className:"h-5 w-5"})})]})]})]})]}),"local"!==o.backup.cloudStorage.provider&&(0,r.jsxs)("div",{className:"mt-4 flex space-x-3",children:[(0,r.jsx)("button",{className:"btn-outline",children:"Test Connection"}),(0,r.jsx)("button",{className:"btn-primary",children:"Save Configuration"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ai.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Backup History"]}),(0,r.jsx)("div",{className:"space-y-3",children:o.backup.backupHistory.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("completed"===e.status?"bg-green-500":"failed"===e.status?"bg-red-500":"bg-yellow-500")}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[e.type.charAt(0).toUpperCase()+e.type.slice(1)," Backup"]}),(0,r.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[new Date(e.timestamp).toLocaleString()," • ",e.size]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700 p-1",title:"Download",children:(0,r.jsx)(W.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-700 p-1",title:"Restore",children:(0,r.jsx)(M.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-red-600 hover:text-red-700 p-1",title:"Delete",children:(0,r.jsx)(er.A,{className:"h-4 w-4"})})]})]},e.id))}),(0,r.jsxs)("div",{className:"mt-4 flex justify-between items-center",children:[(0,r.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-700",children:"View All Backups"}),(0,r.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700",children:"Clear Old Backups"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(aD.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Manual Operations"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("button",{onClick:N,className:"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors",children:[(0,r.jsx)(W.A,{className:"h-5 w-5 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:"Create Backup"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Export all data now"})]})]}),(0,r.jsxs)("button",{onClick:w,className:"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors",children:[(0,r.jsx)(eM.A,{className:"h-5 w-5 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:"Restore Data"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Import from backup"})]})]})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-800 dark:text-yellow-300",children:"Important"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-400 mt-1",children:"Always verify your backups before relying on them. Test restore procedures regularly to ensure data integrity."})]})]})}),(0,r.jsx)("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Last Backup"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:new Date(o.backup.lastBackup).toLocaleString()})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Status"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(aN.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,r.jsx)("span",{className:"text-xs text-green-600",children:"Completed"})]})]})]})})]})]}),I=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ah.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Basic Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Name *"}),(0,r.jsx)("input",{type:"text",value:o.store.name,onChange:e=>b({name:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter store name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Website"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ay.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"url",value:o.store.website,onChange:e=>b({website:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"https://yourstore.com"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eW.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:o.store.phone,onChange:e=>b({phone:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ap.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:o.store.email,onChange:e=>b({email:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"<EMAIL>"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eY.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("textarea",{value:o.store.address,onChange:e=>b({address:e.target.value}),rows:3,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter complete store address"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(P.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Business Hours & Operating Days"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Opening Time"}),(0,r.jsx)("input",{type:"time",value:o.store.businessHours.open,onChange:e=>b({businessHours:{...o.store.businessHours,open:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Closing Time"}),(0,r.jsx)("input",{type:"time",value:o.store.businessHours.close,onChange:e=>b({businessHours:{...o.store.businessHours,close:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Operating Days"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3",children:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:o.store.operatingDays.includes(e),onChange:a=>{b({operatingDays:a.target.checked?[...o.store.operatingDays,e]:o.store.operatingDays.filter(a=>a!==e)})},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300 capitalize",children:e.slice(0,3)})]},e))})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ab.A,{className:"h-5 w-5 mr-2 text-pink-600"}),"Store Branding"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Logo"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-20 h-20 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center bg-gray-50 dark:bg-slate-700",children:o.store.branding.logo?(0,r.jsx)(ec.default,{src:o.store.branding.logo,alt:"Store Logo",width:80,height:80,className:"w-full h-full object-cover rounded-lg"}):(0,r.jsx)(eU.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("button",{onClick:()=>{var e;return null==(e=n.current)?void 0:e.click()},className:"btn-outline text-sm px-4 py-2",children:"Upload Logo"}),o.store.branding.logo&&(0,r.jsx)("button",{onClick:()=>b({branding:{...o.store.branding,logo:null}}),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]})]}),(0,r.jsx)("input",{ref:n,type:"file",accept:"image/*",onChange:e=>C(e,"logo"),className:"hidden"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Slogan"}),(0,r.jsx)("input",{type:"text",value:o.store.branding.slogan,onChange:e=>b({branding:{...o.store.branding,slogan:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Your Neighborhood Store"})]})]})]})]});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[u&&(0,r.jsxs)("span",{className:"text-sm text-yellow-600 dark:text-yellow-400 flex items-center",children:[(0,r.jsx)(D.A,{className:"h-4 w-4 mr-1"}),"Unsaved changes"]}),(0,r.jsxs)("button",{onClick:k,disabled:c||!u,className:"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[c?(0,r.jsx)(M.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(a_.A,{className:"h-4 w-4 mr-2"}),c?"Saving...":"Save Changes"]})]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("nav",{className:"flex space-x-8 px-6",children:h.map(t=>{let s=t.icon;return(0,r.jsxs)("button",{onClick:()=>a(t.id),className:"flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(e===t.id?"border-green-500 text-green-600 dark:text-green-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,r.jsx)(s,{className:"h-5 w-5 mr-2"}),t.label]},t.id)})})}),(0,r.jsx)("div",{className:"p-6",children:(()=>{switch(e){case"store":return I();case"profile":return A();case"notifications":return S();case"security":return _();case"appearance":return L();case"backup":return T();default:return(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("p",{className:"text-gray-500 dark:text-gray-400",children:[e.charAt(0).toUpperCase()+e.slice(1)," settings coming soon..."]})})}})()})]})]})}var aL=t(6408),aT=t(760),aR=t(3311),aE=t(1154),aI=t(9588),aF=t(133),az=t(1981),aO=t(4311),aU=t(2486);function aB(e){let{context:a="dashboard"}=e,{resolvedTheme:t}=(0,s.D)(),[n,o]=(0,l.useState)(!1),[i,d]=(0,l.useState)(!1),[c,m]=(0,l.useState)([{id:"1",type:"ai",content:"Hello! I'm your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?",timestamp:new Date}]),[x,u]=(0,l.useState)(""),[h,b]=(0,l.useState)(!1),[p,f]=(0,l.useState)(!1),[j,v]=(0,l.useState)(null),[k,N]=(0,l.useState)(null),[w,C]=(0,l.useState)(0),[A,S]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e;return p?e=setInterval(()=>{C(e=>e+1)},1e3):C(0),()=>clearInterval(e)},[p]),(0,l.useEffect)(()=>()=>{k&&k.getTracks().forEach(e=>e.stop())},[k]);let[D,_]=(0,l.useState)(null),P=(0,l.useRef)(null),M=(0,l.useRef)(null),L=()=>{var e;null==(e=P.current)||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{L()},[c]),(0,l.useEffect)(()=>{n&&!i&&M.current&&M.current.focus()},[n,i]);let T=async()=>{if(!x.trim()||h)return;let e={id:Date.now().toString(),type:"user",content:x.trim(),timestamp:new Date};m(a=>[...a,e]),u(""),b(!0);let t={id:"typing",type:"ai",content:"",timestamp:new Date,isTyping:!0};m(e=>[...e,t]);try{let t=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e.content,context:a})}),r=await t.json();if(m(e=>e.filter(e=>"typing"!==e.id)),r.success){let e={id:(Date.now()+1).toString(),type:"ai",content:r.response,timestamp:new Date};m(a=>[...a,e])}else throw Error(r.error||"Failed to get AI response")}catch(a){m(e=>e.filter(e=>"typing"!==e.id));let e={id:(Date.now()+1).toString(),type:"ai",content:"Sorry, I encountered an error. Please try again later.",timestamp:new Date};m(a=>[...a,e])}finally{b(!1)}},R=async()=>{try{S(!0);let e=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,sampleRate:44100}});N(e);let a=new MediaRecorder(e,{mimeType:"audio/webm;codecs=opus"}),t=[];a.ondataavailable=e=>{e.data.size>0&&t.push(e.data)},a.onstop=async()=>{let a=new Blob(t,{type:"audio/webm;codecs=opus"});await I(a),e.getTracks().forEach(e=>e.stop()),N(null),S(!1)},v(a),a.start(),f(!0),S(!1)}catch(e){console.error("Error starting recording:",e),S(!1),alert("Unable to access microphone. Please check your permissions.")}},E=()=>{j&&p&&(j.stop(),f(!1))},I=async e=>{try{S(!0);let a=new FileReader;a.readAsDataURL(e),a.onloadend=async()=>{try{let e=a.result,t=await fetch("/api/speech-to-text",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({audioData:e,language:"en-US",sampleRate:44100})}),r=await t.json();if(r.success&&r.transcript)u(r.transcript),r.confidence&&r.confidence>.9?console.warn("\uD83C\uDFAF Excellent transcription quality: ".concat((100*r.confidence).toFixed(1),"%")):r.confidence&&r.confidence>.8?console.warn("✅ Good transcription quality: ".concat((100*r.confidence).toFixed(1),"%")):r.confidence&&r.confidence>.7&&console.warn("⚠️ Moderate transcription quality: ".concat((100*r.confidence).toFixed(1),"%")),r.alternatives&&r.alternatives.length>0&&console.warn("\uD83D\uDCDD Alternative transcriptions available:",r.alternatives),console.warn("\uD83D\uDD0A Voice message successfully transcribed by Google Speech-to-Text");else throw Error(r.error||"Google Speech-to-Text failed to transcribe audio")}catch(e){console.error("Speech-to-text API error:",e),u("Sorry, I couldn't understand that. Please try speaking more clearly or type your message.")}finally{S(!1)}},a.onerror=()=>{console.error("Error reading audio file"),S(!1),alert("Error reading audio file. Please try again.")}}catch(e){console.error("Error processing audio:",e),S(!1),alert("Error processing voice recording. Please try again.")}},F=async(e,a)=>{try{await navigator.clipboard.writeText(e),_(a),setTimeout(()=>_(null),2e3)}catch(e){console.error("Failed to copy message:",e)}},z=e=>e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(aL.P.div,{className:"fixed bottom-6 right-6 z-50 ".concat(n?"hidden":"block"),initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:300,damping:25,delay:.1},children:(0,r.jsxs)(aL.P.button,{onClick:()=>o(!0),className:"relative group",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,r.jsxs)("div",{className:"relative w-16 h-16 rounded-2xl shadow-xl transition-all duration-300 overflow-hidden",style:{background:"dark"===t?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",border:"dark"===t?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(226, 232, 240, 0.8)",boxShadow:"dark"===t?"0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)":"0 10px 25px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05)"},children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:"linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%)"}}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"p-2 rounded-xl transition-all duration-300 group-hover:scale-110",style:{background:"linear-gradient(135deg, #d97706 0%, #059669 100%)",boxShadow:"0 4px 12px rgba(217, 119, 6, 0.3)"},children:(0,r.jsx)(y.A,{className:"w-6 h-6 text-white"})})}),(0,r.jsx)("div",{className:"absolute top-2 right-2",children:(0,r.jsx)("div",{className:"w-3 h-3 bg-emerald-500 rounded-full animate-pulse shadow-sm",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-emerald-500 rounded-full animate-ping opacity-75"})})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-amber-600 rounded-full flex items-center justify-center shadow-lg",children:(0,r.jsx)("span",{className:"text-xs font-bold text-white",children:"!"})})]}),(0,r.jsxs)("div",{className:"absolute right-full mr-4 top-1/2 transform -translate-y-1/2 px-4 py-2 rounded-xl text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap shadow-lg",style:{backgroundColor:"dark"===t?"#0f172a":"#ffffff",color:"dark"===t?"#f1f5f9":"#0f172a",border:"dark"===t?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(226, 232, 240, 0.8)",boxShadow:"dark"===t?"0 8px 25px rgba(0, 0, 0, 0.4)":"0 8px 25px rgba(0, 0, 0, 0.15)"},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(aR.A,{className:"w-4 h-4 text-emerald-500"}),(0,r.jsx)("span",{children:"AI Assistant"})]}),(0,r.jsx)("div",{className:"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0",style:{borderTop:"6px solid transparent",borderBottom:"6px solid transparent",borderLeft:"6px solid ".concat("dark"===t?"#0f172a":"#ffffff")}})]})]})}),(0,r.jsx)(aT.N,{children:n&&(0,r.jsxs)(aL.P.div,{className:"fixed bottom-6 right-6 z-50 rounded-2xl shadow-2xl overflow-hidden backdrop-blur-md flex flex-col ".concat(i?"w-80 h-[60px]":"w-[420px] h-[550px]"),style:{backgroundColor:"dark"===t?"rgba(15, 23, 42, 0.95)":"rgba(255, 255, 255, 0.95)",border:"dark"===t?"1px solid rgba(217, 119, 6, 0.3)":"1px solid rgba(16, 185, 129, 0.3)",boxShadow:"dark"===t?"0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(217, 119, 6, 0.2)":"0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.2)"},initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{duration:.3,ease:"easeOut"},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 flex-shrink-0 ".concat(i?"py-2 border-0":"py-3 border-b"),style:{background:"dark"===t?"linear-gradient(135deg, rgba(120, 53, 15, 0.9) 0%, rgba(6, 78, 59, 0.8) 100%)":"linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%)",borderColor:"dark"===t?"rgba(217, 119, 6, 0.3)":"rgba(16, 185, 129, 0.3)",height:i?"60px":"auto",minHeight:"60px"},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"rounded-xl ".concat(i?"p-1.5":"p-2"),style:{background:"linear-gradient(135deg, #d97706 0%, #059669 100%)",boxShadow:"0 4px 12px rgba(217, 119, 6, 0.3)"},children:(0,r.jsx)(y.A,{className:"text-white ".concat(i?"w-3.5 h-3.5":"w-4 h-4")})}),!i&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm",style:{color:"dark"===t?"#f1f5f9":"#0f172a"},children:"AI Assistant"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-pulse"}),(0,r.jsx)("p",{className:"text-xs",style:{color:"dark"===t?"#94a3b8":"#64748b"},children:"Online"})]})]}),i&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm",style:{color:"dark"===t?"#f1f5f9":"#0f172a"},children:"AI Assistant"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center ".concat(i?"space-x-0.5":"space-x-1"),children:[!i&&(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)(aL.P.button,{onClick:p?E:R,disabled:A,className:"p-2 rounded-lg transition-all duration-200 relative ".concat(p?"bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-900/30":A?"bg-emerald-50 dark:bg-emerald-900/20":"hover:bg-amber-50 dark:hover:bg-amber-900/20"," ").concat(A?"cursor-not-allowed opacity-70":""),title:A?"Processing with Google Speech-to-Text...":p?"Recording voice message - Click to stop":"Voice Recording powered by Google",whileHover:A?{}:{scale:1.05},whileTap:A?{}:{scale:.95},children:[A?(0,r.jsx)(aE.A,{className:"w-4 h-4 animate-spin",style:{color:"dark"===t?"#10b981":"#059669"}}):p?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(et.A,{className:"w-4 h-4 fill-current",style:{color:"dark"===t?"#d97706":"#b45309"}}),(0,r.jsx)(aL.P.div,{className:"absolute -inset-1 rounded-full border-2 border-amber-500",animate:{scale:[1,1.3,1],opacity:[.7,.3,.7]},transition:{duration:1,repeat:1/0,ease:"easeInOut"}})]}):(0,r.jsx)(aI.A,{className:"w-4 h-4",style:{color:"dark"===t?"#10b981":"#059669"}}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 rounded-full transition-all duration-200 ".concat(p?"bg-amber-600 animate-pulse shadow-sm":A?"bg-emerald-500 shadow-sm":"bg-amber-400 dark:bg-amber-600")})]}),(0,r.jsxs)("div",{className:"absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-3 py-2 rounded-lg text-xs font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap shadow-lg z-10",style:{backgroundColor:"dark"===t?"#0f172a":"#ffffff",color:"dark"===t?"#f1f5f9":"#0f172a",border:"dark"===t?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(226, 232, 240, 0.8)",boxShadow:"dark"===t?"0 8px 25px rgba(0, 0, 0, 0.4)":"0 8px 25px rgba(0, 0, 0, 0.15)"},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(p?"bg-amber-600 animate-pulse":A?"bg-emerald-500":"bg-amber-400")}),(0,r.jsx)("span",{children:A?"Google Speech-to-Text Processing...":p?"Recording ".concat(Math.floor(w/60),":").concat((w%60).toString().padStart(2,"0")):"Voice Recording (Google Powered)"})]}),(0,r.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"6px solid transparent",borderRight:"6px solid transparent",borderTop:"6px solid ".concat("dark"===t?"#0f172a":"#ffffff")}})]})]}),!i&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("button",{onClick:()=>{m([{id:"1",type:"ai",content:"Hello! I'm your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?",timestamp:new Date}])},className:"p-2 rounded-lg transition-all duration-200 hover:bg-amber-50 dark:hover:bg-amber-900/20",title:"Clear chat",children:(0,r.jsx)(aF.A,{className:"w-4 h-4",style:{color:"dark"===t?"#94a3b8":"#64748b"}})})}),(0,r.jsx)("button",{onClick:()=>d(!i),className:"rounded-lg transition-all duration-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 ".concat(i?"p-1.5":"p-2"),title:i?"Maximize":"Minimize",children:i?(0,r.jsx)(az.A,{className:"".concat(i?"w-3.5 h-3.5":"w-4 h-4"),style:{color:"dark"===t?"#94a3b8":"#64748b"}}):(0,r.jsx)(aO.A,{className:"".concat(i?"w-3.5 h-3.5":"w-4 h-4"),style:{color:"dark"===t?"#94a3b8":"#64748b"}})}),(0,r.jsx)("button",{onClick:()=>o(!1),className:"rounded-lg transition-all duration-200 hover:bg-amber-100 dark:hover:bg-amber-900/20 ".concat(i?"p-1.5":"p-2"),title:"Close chat",children:(0,r.jsx)($.A,{className:"hover:text-amber-600 ".concat(i?"w-3.5 h-3.5":"w-4 h-4"),style:{color:"dark"===t?"#94a3b8":"#64748b"}})})]})]}),!i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-3 space-y-3 h-[380px] scrollbar-thin scrollbar-thumb-amber-300 dark:scrollbar-thumb-amber-600 scrollbar-track-transparent",children:[c.map(e=>e.isTyping?(0,r.jsx)(aL.P.div,{className:"flex justify-start",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("div",{className:"p-2 rounded-full",style:{backgroundColor:"dark"===t?"#059669":"#10b981"},children:(0,r.jsx)(y.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{className:"p-3 rounded-2xl rounded-tl-md flex items-center space-x-2",style:{backgroundColor:"dark"===t?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)"},children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)(aL.P.div,{className:"w-2 h-2 rounded-full",style:{backgroundColor:"dark"===t?"#059669":"#10b981"},animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:0}}),(0,r.jsx)(aL.P.div,{className:"w-2 h-2 rounded-full",style:{backgroundColor:"dark"===t?"#059669":"#10b981"},animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:.2}}),(0,r.jsx)(aL.P.div,{className:"w-2 h-2 rounded-full",style:{backgroundColor:"dark"===t?"#059669":"#10b981"},animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:.4}})]}),(0,r.jsx)("span",{className:"text-sm",style:{color:"dark"===t?"#f8fafc":"#111827"},children:"AI is thinking..."})]})]})},e.id):(0,r.jsx)(aL.P.div,{className:"flex ".concat("user"===e.type?"justify-end":"justify-start"," group"),initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},transition:{duration:.3,type:"spring",stiffness:200},children:(0,r.jsxs)("div",{className:"flex items-start space-x-2 max-w-[80%] ".concat("user"===e.type?"flex-row-reverse space-x-reverse":""),children:[(0,r.jsxs)(aL.P.div,{className:"p-2 rounded-full flex-shrink-0 relative overflow-hidden",style:{backgroundColor:"user"===e.type?"dark"===t?"#22c55e":"#16a34a":"dark"===t?"#059669":"#10b981"},whileHover:{scale:1.1},children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)",transform:"translateX(-100%)",animation:"shimmer 1.5s infinite"}}),"user"===e.type?(0,r.jsx)(g.A,{className:"w-4 h-4 text-white relative z-10"}):(0,r.jsx)(y.A,{className:"w-4 h-4 text-white relative z-10"})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)(aL.P.div,{className:"p-3 rounded-2xl ".concat("user"===e.type?"rounded-tr-md":"rounded-tl-md"," relative overflow-hidden"),style:{backgroundColor:"user"===e.type?"dark"===t?"rgba(34, 197, 94, 0.2)":"rgba(34, 197, 94, 0.1)":"dark"===t?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",color:"dark"===t?"#f8fafc":"#111827",border:"1px solid ".concat("user"===e.type?"dark"===t?"rgba(34, 197, 94, 0.3)":"rgba(34, 197, 94, 0.2)":"dark"===t?"rgba(139, 92, 246, 0.3)":"rgba(139, 92, 246, 0.2)")},whileHover:{scale:1.02},children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap leading-relaxed",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("p",{className:"text-xs opacity-70",style:{color:"dark"===t?"#94a3b8":"#64748b"},children:z(e.timestamp)}),"ai"===e.type&&(0,r.jsx)("button",{onClick:()=>F(e.content,e.id),className:"opacity-0 group-hover:opacity-100 p-1 rounded transition-all duration-200 hover:bg-emerald-100 dark:hover:bg-emerald-900/20",title:"Copy message",children:D===e.id?(0,r.jsx)(aN.A,{className:"w-3 h-3 text-emerald-500"}):(0,r.jsx)(eT.A,{className:"w-3 h-3",style:{color:"dark"===t?"#94a3b8":"#64748b"}})})]})]})})]})},e.id)),(0,r.jsx)("div",{ref:P})]}),(0,r.jsxs)("div",{className:"p-4 border-t",style:{borderColor:"dark"===t?"rgba(148, 163, 184, 0.2)":"rgba(226, 232, 240, 0.8)",background:"dark"===t?"rgba(15, 23, 42, 0.8)":"rgba(248, 250, 252, 0.8)"},children:[(p||A)&&(0,r.jsx)(aL.P.div,{className:"mb-3 p-3 rounded-lg border",style:{backgroundColor:p?"dark"===t?"rgba(239, 68, 68, 0.1)":"rgba(254, 226, 226, 0.8)":"dark"===t?"rgba(251, 191, 36, 0.1)":"rgba(254, 243, 199, 0.8)",borderColor:p?"rgba(239, 68, 68, 0.3)":"rgba(251, 191, 36, 0.3)"},initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[p?(0,r.jsx)(aL.P.div,{className:"w-3 h-3 bg-amber-600 rounded-full",animate:{scale:[1,1.2,1]},transition:{duration:1,repeat:1/0}}):(0,r.jsx)(aE.A,{className:"w-4 h-4 animate-spin text-emerald-500"}),(0,r.jsx)("span",{className:"text-sm font-medium",style:{color:p?"dark"===t?"#d97706":"#b45309":"dark"===t?"#10b981":"#059669"},children:p?"\uD83C\uDF99️ Recording... ".concat(Math.floor(w/60),":").concat((w%60).toString().padStart(2,"0")):"\uD83D\uDD04 Google Speech-to-Text Processing..."})]}),p&&(0,r.jsx)("button",{onClick:E,className:"px-3 py-1 text-xs rounded-lg bg-amber-600 hover:bg-amber-700 text-white transition-colors duration-200",children:"Stop"})]})}),(0,r.jsxs)("div",{className:"flex items-end space-x-3",children:[(0,r.jsx)("div",{className:"flex-1 relative",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("textarea",{ref:M,value:x,onChange:e=>{u(e.target.value);let a=e.target;a.style.height="auto";let t=Math.min(Math.max(a.scrollHeight,44),120);a.style.height=t+"px"},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),T())},placeholder:"Ask me anything about your store...",disabled:h,rows:1,className:"w-full px-4 py-3 pr-12 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none overflow-y-auto text-sm",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",borderColor:"dark"===t?"rgba(148, 163, 184, 0.3)":"rgba(226, 232, 240, 0.8)",color:"dark"===t?"#f1f5f9":"#0f172a",minHeight:"44px",maxHeight:"120px",lineHeight:"1.5"}}),(0,r.jsx)("div",{className:"absolute bottom-2 right-12 text-xs px-2 py-1 rounded",style:{color:"dark"===t?"#64748b":"#94a3b8",backgroundColor:"dark"===t?"rgba(30, 41, 59, 0.8)":"rgba(255, 255, 255, 0.8)"},children:x.length})]})}),(0,r.jsx)(aL.P.button,{onClick:T,disabled:!x.trim()||h,className:"p-3 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-emerald-500 flex-shrink-0",style:{background:!x.trim()||h?"dark"===t?"rgba(71, 85, 105, 0.6)":"rgba(156, 163, 175, 0.6)":"linear-gradient(135deg, #d97706 0%, #059669 100%)",boxShadow:!x.trim()||h?"none":"0 4px 12px rgba(217, 119, 6, 0.3)",height:"44px",width:"44px"},whileHover:!x.trim()||h?{}:{scale:1.05},whileTap:!x.trim()||h?{}:{scale:.95},children:(0,r.jsx)("div",{className:"flex items-center justify-center",children:h?(0,r.jsx)(aE.A,{className:"w-4 h-4 text-white animate-spin"}):(0,r.jsx)(aU.A,{className:"w-4 h-4 text-white"})})})]}),!x&&(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:["Sales trends","Inventory tips","Debt analysis"].map(e=>(0,r.jsx)("button",{onClick:()=>u("Show me ".concat(e.toLowerCase())),className:"px-3 py-1 text-xs rounded-lg transition-colors duration-200",style:{backgroundColor:"rgba(16, 185, 129, 0.1)",color:"dark"===t?"#34d399":"#059669",border:"1px solid rgba(16, 185, 129, 0.2)"},children:e},e))}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-xs opacity-60",children:[(0,r.jsx)(aI.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{style:{color:"dark"===t?"#94a3b8":"#64748b"},children:"Voice powered by Google Speech-to-Text"})]})]})]})]})]})})]})}var aq=t(463),aH=t(9947),aG=t(9376);let aV=[{icon:G.A,title:"Sales Analysis",prompt:"Analyze my current sales performance and suggest improvements"},{icon:o.A,title:"Inventory Management",prompt:"Help me optimize my inventory levels and identify slow-moving products"},{icon:I.A,title:"Debt Management",prompt:"Provide strategies for managing customer debts effectively"},{icon:j.A,title:"Business Insights",prompt:"Give me insights on how to grow my sari-sari store business"},{icon:aq.A,title:"Marketing Ideas",prompt:"Suggest marketing strategies to attract more customers"},{icon:aH.A,title:"General Help",prompt:"What can you help me with regarding my store management?"}];function aW(){let{resolvedTheme:e}=(0,s.D)(),[a,t]=(0,l.useState)([{id:"1",type:"ai",content:"Welcome to AI Support! I'm here to help you manage your Revantad Store more effectively. I can assist with business analytics, inventory management, customer relationships, financial planning, and much more. How can I help you today?",timestamp:new Date}]),[n,o]=(0,l.useState)(""),[i,d]=(0,l.useState)(!1),c=(0,l.useRef)(null),m=(0,l.useRef)(null),x=()=>{var e;null==(e=c.current)||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{x()},[a]),(0,l.useEffect)(()=>{m.current&&m.current.focus()},[]);let u=async e=>{let a=e||n.trim();if(!a||i)return;let r={id:Date.now().toString(),type:"user",content:a,timestamp:new Date};t(e=>[...e,r]),o(""),d(!0);try{let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a,context:"ai-support"})}),r=await e.json();if(r.success){let e={id:(Date.now()+1).toString(),type:"ai",content:r.response,timestamp:new Date};t(a=>[...a,e])}else throw Error(r.error||"Failed to get AI response")}catch(a){let e={id:(Date.now()+1).toString(),type:"ai",content:"Sorry, I encountered an error. Please try again later.",timestamp:new Date};t(a=>[...a,e])}finally{d(!1)}},h=e=>e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0});return(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(aL.P.div,{className:"p-3 rounded-xl mr-3",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:"0 4px 8px rgba(139, 92, 246, 0.3)"},whileHover:{scale:1.05},children:(0,r.jsx)(aG.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold",style:{color:"dark"===e?"#f8fafc":"#111827"},children:"AI Business Assistant"}),(0,r.jsx)("p",{className:"text-sm opacity-80",style:{color:"dark"===e?"#cbd5e1":"#64748b"},children:"Intelligent support for your sari-sari store operations"})]})]})}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"dark"===e?"#f8fafc":"#111827"},children:[(0,r.jsx)(R.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Quick Actions"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:aV.map((a,t)=>{let s=a.icon;return(0,r.jsxs)(aL.P.button,{onClick:()=>u(a.prompt),className:"p-5 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 text-left",style:{backgroundColor:"dark"===e?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",borderColor:"dark"===e?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",boxShadow:"dark"===e?"0 4px 6px rgba(0, 0, 0, 0.1)":"0 4px 6px rgba(0, 0, 0, 0.05)"},whileHover:{scale:1.03,boxShadow:"dark"===e?"0 8px 25px rgba(139, 92, 246, 0.2)":"0 8px 25px rgba(139, 92, 246, 0.15)"},whileTap:{scale:.98},disabled:i,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)(aL.P.div,{className:"p-2 rounded-lg mr-3",style:{backgroundColor:"dark"===e?"rgba(139, 92, 246, 0.2)":"rgba(139, 92, 246, 0.1)",border:"1px solid ".concat("dark"===e?"rgba(139, 92, 246, 0.3)":"rgba(139, 92, 246, 0.2)")},whileHover:{scale:1.1,rotate:5},children:(0,r.jsx)(s,{className:"w-5 h-5 text-purple-500"})}),(0,r.jsx)("span",{className:"font-semibold text-sm",style:{color:"dark"===e?"#f8fafc":"#111827"},children:a.title})]}),(0,r.jsx)("p",{className:"text-xs leading-relaxed",style:{color:"dark"===e?"#cbd5e1":"#64748b"},children:a.prompt})]},t)})})]}),(0,r.jsx)("div",{className:"flex-1 flex flex-col",children:(0,r.jsxs)("div",{className:"flex-1 rounded-xl border p-4 mb-4 overflow-hidden",style:{backgroundColor:"dark"===e?"rgba(30, 41, 59, 0.5)":"rgba(255, 255, 255, 0.8)",borderColor:"dark"===e?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",boxShadow:"dark"===e?"0 4px 6px rgba(0, 0, 0, 0.1)":"0 4px 6px rgba(0, 0, 0, 0.05)"},children:[(0,r.jsxs)("div",{className:"h-96 overflow-y-auto space-y-4 mb-4",children:[a.map(a=>(0,r.jsx)("div",{className:"flex ".concat("user"===a.type?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3 max-w-[80%] ".concat("user"===a.type?"flex-row-reverse space-x-reverse":""),children:[(0,r.jsx)("div",{className:"p-2 rounded-full flex-shrink-0",style:{backgroundColor:"user"===a.type?"dark"===e?"#22c55e":"#16a34a":"dark"===e?"#8b5cf6":"#7c3aed"},children:"user"===a.type?(0,r.jsx)(g.A,{className:"w-4 h-4 text-white"}):(0,r.jsx)(y.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{className:"p-4 rounded-2xl ".concat("user"===a.type?"rounded-tr-md":"rounded-tl-md"),style:{backgroundColor:"user"===a.type?"dark"===e?"rgba(34, 197, 94, 0.2)":"rgba(34, 197, 94, 0.1)":"dark"===e?"rgba(71, 85, 105, 0.4)":"rgba(243, 244, 246, 0.9)",color:"dark"===e?"#f8fafc":"#111827"},children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap leading-relaxed",children:a.content}),(0,r.jsx)("p",{className:"text-xs mt-2 opacity-70",style:{color:"dark"===e?"#94a3b8":"#64748b"},children:h(a.timestamp)})]})]})},a.id)),i&&(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full",style:{backgroundColor:"dark"===e?"#8b5cf6":"#7c3aed"},children:(0,r.jsx)(y.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{className:"p-4 rounded-2xl rounded-tl-md flex items-center space-x-3",style:{backgroundColor:"dark"===e?"rgba(71, 85, 105, 0.4)":"rgba(243, 244, 246, 0.9)"},children:[(0,r.jsx)(aE.A,{className:"w-4 h-4 animate-spin",style:{color:"dark"===e?"#8b5cf6":"#7c3aed"}}),(0,r.jsx)("span",{className:"text-sm",style:{color:"dark"===e?"#f8fafc":"#111827"},children:"Analyzing your request..."})]})]})}),(0,r.jsx)("div",{ref:c})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{ref:m,type:"text",value:n,onChange:e=>o(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"Ask me anything about your store management...",disabled:i,className:"flex-1 p-4 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500",style:{backgroundColor:"dark"===e?"#334155":"#ffffff",borderColor:"dark"===e?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",color:"dark"===e?"#f8fafc":"#111827"}}),(0,r.jsx)("button",{onClick:()=>u(),disabled:!n.trim()||i,className:"p-4 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:"0 4px 8px rgba(139, 92, 246, 0.3)"},children:(0,r.jsx)(aU.A,{className:"w-5 h-5 text-white"})})]})]})})]})}var aY=t(5695);function aK(e){let{children:a}=e,{isAuthenticated:t,isLoading:s}=(0,p.A)(),n=(0,aY.useRouter)();return((0,l.useEffect)(()=>{s||t||n.push("/login")},[t,s,n]),s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Loading Revantad Store"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Please wait while we prepare your dashboard..."})]})}):t?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-red-600 dark:text-red-400 font-bold text-2xl",children:"!"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Redirecting to login page..."})]})})}function aJ(){let[e,a]=(0,l.useState)("dashboard"),{resolvedTheme:t}=(0,s.D)(),[n,o]=(0,l.useState)({totalProducts:0,totalDebts:0,totalDebtAmount:0,lowStockItems:0,recentProducts:[],recentDebts:[]});(0,l.useEffect)(()=>{i()},[]);let i=async()=>{try{let e=await fetch("/api/products"),a=(await e.json()).products||[],t=await fetch("/api/debts"),r=(await t.json()).debts||[],s=r.reduce((e,a)=>e+a.total_amount,0),l=a.filter(e=>e.stock_quantity<10).length;o({totalProducts:a.length,totalDebts:r.length,totalDebtAmount:s,lowStockItems:l,recentProducts:a.slice(0,5),recentDebts:r.slice(0,5)})}catch(e){console.error("Error fetching stats:",e)}};return(0,r.jsx)(aK,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300",style:{backgroundColor:"dark"===t?"#0f172a":"#f9fafb"},children:[(0,r.jsx)(f,{activeSection:e,setActiveSection:a}),(0,r.jsxs)("div",{className:"flex pt-16",children:[(0,r.jsx)(A,{activeSection:e,setActiveSection:a}),(0,r.jsx)("main",{className:"flex-1 transition-colors duration-300 main-content-scroll",style:{backgroundColor:"dark"===t?"#0f172a":"#ffffff",height:"calc(100vh - 4rem)",overflowY:"auto",overflowX:"hidden"},children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold transition-colors duration-300",style:{color:"dark"===t?"#f8fafc":"#1f2937"},children:(()=>{switch(e){case"dashboard":default:return"Dashboard";case"products":return"Product Lists";case"debts":return"Customer Debt Management";case"family-gallery":return"Family Gallery";case"api-graphing":return"API Graphing & Visuals";case"history":return"History";case"calendar":return"Calendar";case"settings":return"Settings";case"ai-support":return"AI Support"}})()}),(0,r.jsx)("p",{className:"mt-2 transition-colors duration-300",style:{color:"dark"===t?"#cbd5e1":"#6b7280"},children:(()=>{switch(e){case"dashboard":default:return"Overview of your Revantad Store";case"products":return"Manage your product lists with CRUD operations";case"debts":return"Track customer debt and payments";case"family-gallery":return"Manage family photos and memories";case"api-graphing":return"Visual analytics and business insights";case"history":return"View transaction and activity history";case"calendar":return"Manage events and schedules";case"settings":return"Configure your store settings";case"ai-support":return"Get intelligent assistance for your store management"}})()})]}),(()=>{switch(console.warn("\uD83C\uDFAF Rendering content for section:",e),e){case"products":return console.warn("\uD83D\uDCE6 Rendering ProductsSection"),(0,r.jsx)(eO,{onStatsUpdate:i});case"debts":return console.warn("\uD83D\uDCB3 Rendering DebtsSection"),(0,r.jsx)(e2,{onStatsUpdate:i});case"family-gallery":return console.warn("\uD83D\uDDBC️ Rendering FamilyGallery"),(0,r.jsx)(e8,{});case"api-graphing":return console.warn("\uD83D\uDCCA Rendering APIGraphing"),(0,r.jsx)(Q,{stats:n});case"history":return console.warn("\uD83D\uDCDC Rendering History"),(0,r.jsx)(au,{});case"calendar":return console.warn("\uD83D\uDCC5 Rendering Calendar"),(0,r.jsx)(al,{});case"settings":return console.warn("⚙️ Rendering Settings"),(0,r.jsx)(aM,{});case"ai-support":return console.warn("\uD83E\uDD16 Rendering AISupport"),(0,r.jsx)(aW,{});default:return console.warn("\uD83C\uDFE0 Rendering DashboardStats (default)"),(0,r.jsx)(B,{stats:n,onSectionChange:a})}})()]})})]}),(0,r.jsx)(aB,{context:e})]})})}t(7280)},7280:(e,a,t)=>{"use strict";t.d(a,{ThemeProvider:()=>l});var r=t(5155),s=t(1362);function l(e){let{children:a,...t}=e;return(0,r.jsx)(s.N,{...t,children:a})}},8606:(e,a,t)=>{Promise.resolve().then(t.bind(t,6071))}},e=>{var a=a=>e(e.s=a);e.O(0,[142,583,441,684,358],()=>a(8606)),_N_E=e.O()}]);