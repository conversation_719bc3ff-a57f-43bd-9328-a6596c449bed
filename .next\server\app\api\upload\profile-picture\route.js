(()=>{var e={};e.id=242,e.ids=[242],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53404:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>l,POST:()=>p});var o=t(96559),i=t(48088),u=t(37719),a=t(32190),n=t(71870);async function p(e){try{let r=(await e.formData()).get("file");if(!r)return a.NextResponse.json({error:"No file uploaded"},{status:400});if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(r.type))return a.NextResponse.json({error:"Invalid file type. Only JPEG, PNG, and WebP are allowed."},{status:400});if(r.size>5242880)return a.NextResponse.json({error:"File too large. Maximum size is 5MB."},{status:400});let t=await r.arrayBuffer(),s=Buffer.from(t),o=Date.now(),i=Math.random().toString(36).substring(2,15),u=`customer_profiles/profile_${o}_${i}`,p=await new Promise((e,r)=>{n.v2.uploader.upload_stream({resource_type:"image",public_id:u,folder:"customer_profiles",transformation:[{width:400,height:400,crop:"fill",gravity:"face"},{quality:"auto",fetch_format:"auto"}]},(t,s)=>{t?r(t):e(s)}).end(s)});return a.NextResponse.json({success:!0,url:p.secure_url,public_id:p.public_id,filename:p.public_id})}catch(e){return console.error("Error uploading file to Cloudinary:",e),a.NextResponse.json({error:"Failed to upload file to cloud storage"},{status:500})}}async function l(e){try{let{searchParams:r}=new URL(e.url),t=r.get("public_id");if(!t)return a.NextResponse.json({error:"Public ID is required"},{status:400});let s=await n.v2.uploader.destroy(t);if("ok"===s.result)return a.NextResponse.json({success:!0,message:"Profile picture deleted successfully from cloud storage"});return a.NextResponse.json({error:"Failed to delete image from cloud storage"},{status:400})}catch(e){return console.error("Error deleting file from Cloudinary:",e),a.NextResponse.json({error:"Failed to delete file from cloud storage"},{status:500})}}n.v2.config({cloud_name:process.env.CLOUDINARY_CLOUD_NAME||"",api_key:process.env.CLOUDINARY_API_KEY||"",api_secret:process.env.CLOUDINARY_API_SECRET||""});let c=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/upload/profile-picture/route",pathname:"/api/upload/profile-picture",filename:"route",bundlePath:"app/api/upload/profile-picture/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\profile-picture\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:f,serverHooks:x}=c;function g(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:f})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,870],()=>t(53404));module.exports=s})();