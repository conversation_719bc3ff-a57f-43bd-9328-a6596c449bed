(()=>{var e={};e.id=146,e.ids=[146],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,t,r)=>{"use strict";r.d(t,{$W:()=>i});var s=r(72289),o=r(35282);let n=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),a=function(){try{return n.parse(process.env)}catch(e){if(e instanceof o.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===a.NODE_ENV,isProduction:"production"===a.NODE_ENV,isTest:"test"===a.NODE_ENV,database:{url:a.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:a.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:a.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:a.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:a.CLOUDINARY_API_KEY,apiSecret:a.CLOUDINARY_API_SECRET},ai:{geminiApiKey:a.GEMINI_API_KEY},auth:{secret:a.NEXTAUTH_SECRET,url:a.NEXTAUTH_URL},debug:a.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:c,NEXT_PUBLIC_SUPABASE_ANON_KEY:l,SUPABASE_SERVICE_ROLE_KEY:p,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:R,NEXTAUTH_URL:A,DEBUG:N,GEMINI_API_KEY:f}=a},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},53171:(e,t,r)=>{"use strict";r.d(t,{FB:()=>u,Rv:()=>l,Y8:()=>_,ie:()=>p,r6:()=>a,sv:()=>c,u3:()=>d,yj:()=>i});var s=r(32190);let o={OK:200,BAD_REQUEST:400,CONFLICT:409,INTERNAL_SERVER_ERROR:500};class n extends Error{constructor(e,t=o.INTERNAL_SERVER_ERROR,r){super(e),this.message=e,this.statusCode=t,this.code=r,this.name="ApiError"}}function a(e,t,r=o.OK){let n={success:!0,data:e};return t&&(n.message=t),s.NextResponse.json(n,{status:r})}function i(e,t=o.INTERNAL_SERVER_ERROR,r){return s.NextResponse.json({success:!1,error:e,code:r},{status:t})}function u(e){return async(...t)=>{try{return await e(...t)}catch(e){if(console.error("API Error:",e),e instanceof n)return i(e.message,e.statusCode,e.code);if(e instanceof Error)return i(e.message);return i("An unexpected error occurred")}}}async function c(e,t){try{let r=await e.json();return t(r)}catch{throw new n("Invalid request body",o.BAD_REQUEST)}}function l(e,t){let r=t.filter(t=>void 0===e[t]||null===e[t]||""===e[t]);if(r.length>0)throw new n(`Missing required fields: ${r.join(", ")}`,o.BAD_REQUEST)}function p(e){if(console.error("Database error:",e),"23505"===e.code)throw new n("Resource already exists",o.CONFLICT);if("23503"===e.code)throw new n("Referenced resource not found",o.BAD_REQUEST);if("23502"===e.code)throw new n("Required field is missing",o.BAD_REQUEST);throw new n("Database operation failed",o.INTERNAL_SERVER_ERROR)}function d(e,t={}){let{maxLimit:r=100}=t,s=Math.max(1,parseInt(e.get("page")||"1",10)),o=Math.min(r,Math.max(1,parseInt(e.get("limit")||"10",10)));return{page:s,limit:o,offset:(s-1)*o}}let E={"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"};function _(){return new s.NextResponse(null,{status:200,headers:E})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(66437),o=r(34960);let n=(0,s.UU)(o.$W.database.url,o.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66546:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>d,serverHooks:()=>R,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{GET:()=>l,OPTIONS:()=>c,POST:()=>p});var o=r(96559),n=r(48088),a=r(37719),i=r(53171),u=r(56621);async function c(){return(0,i.Y8)()}let l=(0,i.FB)(async e=>{let{searchParams:t}=new URL(e.url),{page:r,limit:s,offset:o}=(0,i.u3)(t),n=t.get("category"),a=t.get("search"),c="true"===t.get("lowStock"),l=u.N.from("products").select("*",{count:"exact"}).order("created_at",{ascending:!1}).range(o,o+s-1);n&&(l=l.eq("category",n)),a&&(l=l.ilike("name",`%${a}%`)),c&&(l=l.lt("stock_quantity",10));let{data:p,error:d,count:E}=await l;return d&&(0,i.ie)(d),(0,i.r6)({products:p||[],pagination:{page:r,limit:s,total:E||0,totalPages:Math.ceil((E||0)/s)}})}),p=(0,i.FB)(async e=>{let t=await (0,i.sv)(e,e=>((0,i.Rv)(e,["name","net_weight","price","category"]),{name:String(e.name).trim(),image_url:e.image_url?String(e.image_url).trim():null,net_weight:String(e.net_weight).trim(),price:parseFloat(e.price),stock_quantity:parseInt(e.stock_quantity)||0,category:String(e.category).trim()}));if(t.price<0)return(0,i.yj)("Price must be a positive number",400);if(t.stock_quantity<0)return(0,i.yj)("Stock quantity must be a positive number",400);let{data:r,error:s}=await u.N.from("products").insert([t]).select().single();return s&&(0,i.ie)(s),(0,i.r6)(r,"Product created successfully",201)}),d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:_,serverHooks:R}=d;function A(){return(0,a.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:_})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,289,437],()=>r(66546));module.exports=s})();