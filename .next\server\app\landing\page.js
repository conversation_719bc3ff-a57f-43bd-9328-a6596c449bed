(()=>{var e={};e.id=18,e.ids=[18],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9887:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),a=r(26001);let i=(0,r(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]]);var n=r(41312),o=r(53411),l=r(99891),d=r(70334),c=r(64398),m=r(85814),h=r.n(m);function p(){let e=[{icon:i,title:"Product Lists Management",description:"Complete CRUD operations for your product inventory with image uploads and categorization."},{icon:n.A,title:"Customer Debt Tracking",description:"Efficiently manage customer credit (utang) with detailed records and payment tracking."},{icon:o.A,title:"Business Analytics",description:"Visual charts and reports to understand your business performance and trends."},{icon:l.A,title:"Secure & Reliable",description:"Built with modern security practices and reliable cloud infrastructure."}];return(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-slate-900",children:[(0,s.jsx)("nav",{className:"fixed top-0 w-full z-50 glass-effect",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 hero-gradient rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:"R"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gradient",children:"Revantad Store"})]}),(0,s.jsxs)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.2},className:"flex space-x-3",children:[(0,s.jsxs)(h(),{href:"/login",className:"btn-primary",children:["Admin Login",(0,s.jsx)(d.A,{className:"ml-2 h-4 w-4"})]}),(0,s.jsx)(h(),{href:"/admin",className:"btn-outline bg-white/10 border-white/30 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700",children:"Dashboard"})]})]})})}),(0,s.jsx)("section",{className:"pt-20 pb-16 hero-gradient",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-white",children:[(0,s.jsxs)("h1",{className:"text-5xl lg:text-6xl font-bold font-display mb-6 leading-tight",children:["Modernize Your",(0,s.jsx)("span",{className:"block text-yellow-300",children:"Sari-Sari Store"})]}),(0,s.jsx)("p",{className:"text-xl mb-8 text-green-100 leading-relaxed",children:"Professional admin dashboard for managing products, customer debt, and business analytics. Built specifically for Filipino entrepreneurs."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsxs)(h(),{href:"/login",className:"btn-secondary inline-flex items-center justify-center",children:["Start Managing",(0,s.jsx)(d.A,{className:"ml-2 h-5 w-5"})]}),(0,s.jsx)(h(),{href:"/admin",className:"btn-outline bg-white/10 border-white/30 text-white hover:bg-white hover:text-green-600",children:"View Dashboard"})]})]}),(0,s.jsxs)(a.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.2},className:"relative",children:[(0,s.jsx)("div",{className:"relative z-10 bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/20 rounded-lg",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:"Total Products"}),(0,s.jsx)("span",{className:"text-yellow-300 font-bold text-2xl",children:"247"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/20 rounded-lg",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:"Customer Debts"}),(0,s.jsx)("span",{className:"text-yellow-300 font-bold text-2xl",children:"₱12,450"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/20 rounded-lg",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:"Monthly Revenue"}),(0,s.jsx)("span",{className:"text-yellow-300 font-bold text-2xl",children:"₱45,200"})]})]})}),(0,s.jsx)("div",{className:"absolute -top-4 -right-4 w-72 h-72 bg-yellow-400/20 rounded-full blur-3xl"}),(0,s.jsx)("div",{className:"absolute -bottom-4 -left-4 w-72 h-72 bg-green-400/20 rounded-full blur-3xl"})]})]})})}),(0,s.jsx)("section",{className:"py-20 bg-gray-50 dark:bg-slate-800",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-16",children:[(0,s.jsxs)("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:["Everything You Need to ",(0,s.jsx)("span",{className:"text-gradient",children:"Succeed"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Comprehensive tools designed specifically for Filipino sari-sari store owners"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.map((e,t)=>(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},className:"card p-6 text-center hover:shadow-lg transition-all duration-300",children:[(0,s.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(e.icon,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:e.description})]},e.title))})]})}),(0,s.jsx)("section",{className:"py-20 bg-white dark:bg-slate-900",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-16",children:[(0,s.jsxs)("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:["Trusted by ",(0,s.jsx)("span",{className:"text-gradient",children:"Store Owners"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300",children:"See what our users say about Revantad Store"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:[{name:"Maria Santos",role:"Store Owner",content:"Revantad Store has transformed how I manage my sari-sari store. The debt tracking feature is a game-changer!",rating:5},{name:"Juan Dela Cruz",role:"Business Owner",content:"The product management system is so easy to use. I can update my inventory anywhere, anytime.",rating:5},{name:"Ana Reyes",role:"Entrepreneur",content:"Professional, reliable, and perfect for small businesses. Highly recommended!",rating:5}].map((e,t)=>(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},className:"card p-6",children:[(0,s.jsx)("div",{className:"flex items-center mb-4",children:[...Array(e.rating)].map((e,t)=>(0,s.jsx)(c.A,{className:"h-5 w-5 text-yellow-400 fill-current"},t))}),(0,s.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-4 italic",children:["“",e.content,"”"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold text-gray-900 dark:text-white",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.role})]})]},e.name))})]})}),(0,s.jsx)("section",{className:"py-20 hero-gradient",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"text-white",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold mb-6",children:"Ready to Transform Your Business?"}),(0,s.jsx)("p",{className:"text-xl mb-8 text-green-100",children:"Join hundreds of store owners who have modernized their operations with Revantad Store"}),(0,s.jsxs)(h(),{href:"/login",className:"btn-secondary inline-flex items-center text-lg px-8 py-4",children:["Get Started Today",(0,s.jsx)(d.A,{className:"ml-2 h-5 w-5"})]})]})})}),(0,s.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 hero-gradient rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:"R"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gradient",children:"Revantad Store"})]}),(0,s.jsx)("p",{className:"text-gray-400 mb-4",children:"Professional admin dashboard for modern sari-sari stores"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"\xa9 2024 Revantad Store. All rights reserved."})]})})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14012:(e,t,r)=>{Promise.resolve().then(r.bind(r,69500)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,19864))},14056:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\src\\\\app\\\\landing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx","default")},14138:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(60687),a=r(10218);function i({children:e,...t}){return(0,s.jsx)(a.N,{...t,children:e})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19864:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx","SettingsProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx","useSettings")},22362:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>o,t:()=>l});var s=r(60687),a=r(43210);let i=(0,a.createContext)(void 0),n={store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",website:"https://revantadstore.com",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"},operatingDays:["monday","tuesday","wednesday","thursday","friday","saturday"],businessRegistration:{registrationNumber:"REG-2024-001",taxId:"TAX-*********",businessType:"Retail",registrationDate:"2024-01-01"},locations:[{id:1,name:"Main Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",isMain:!0}],branding:{logo:null,primaryColor:"#22c55e",secondaryColor:"#facc15",slogan:"Your Neighborhood Store"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner",avatar:null,bio:"Experienced store owner managing Revantad Store operations.",dateOfBirth:"1990-01-01",address:"123 Barangay Street, Manila, Philippines",emergencyContact:{name:"Emergency Contact",phone:"+63 ************",relationship:"Family"},preferences:{language:"en",timezone:"Asia/Manila",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"}},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,channels:{email:"<EMAIL>",sms:"+63 ************",webhook:""},customRules:[{id:1,name:"Critical Stock Alert",condition:"stock < 5",action:"email + sms",enabled:!0}],templates:{lowStock:"Product {{productName}} is running low ({{currentStock}} remaining)",newDebt:"New debt recorded for {{customerName}}: ₱{{amount}}",paymentReceived:"Payment received from {{customerName}}: ₱{{amount}}"}},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5",currentPassword:"",newPassword:"",confirmPassword:"",apiKeys:[{id:1,name:"Main API Key",key:"sk_live_***************",created:"2024-01-01",lastUsed:"2024-01-20",permissions:["read","write"]}],loginHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",ip:"***********",device:"Chrome on Windows",location:"Manila, Philippines",success:!0}],passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!0}},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US",colorScheme:{primary:"#22c55e",secondary:"#facc15",accent:"#3b82f6",background:"#ffffff",surface:"#f8fafc"},layout:{sidebarPosition:"left",density:"comfortable",showAnimations:!0,compactMode:!1},typography:{fontFamily:"Inter",fontSize:"medium",fontWeight:"normal"}},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z",cloudStorage:{provider:"local",bucket:"",accessKey:"",secretKey:""},backupHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",size:"2.5 MB",status:"completed",type:"automatic"},{id:2,timestamp:"2024-01-19T10:30:00Z",size:"2.4 MB",status:"completed",type:"automatic"}],verification:{enabled:!0,lastVerified:"2024-01-20T10:35:00Z",status:"verified"}}};function o({children:e}){let[t,r]=(0,a.useState)(n),[o,l]=(0,a.useState)(!1),[d,c]=(0,a.useState)(!1),m=async()=>{l(!0);try{await new Promise(e=>setTimeout(e,1e3)),localStorage.setItem("revantad-settings",JSON.stringify(t)),c(!1),"dark"===t.appearance.theme?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),console.warn("Settings saved successfully:",t)}catch(e){throw console.error("Error saving settings:",e),e}finally{l(!1)}};return(0,s.jsx)(i.Provider,{value:{settings:t,updateSettings:(e,t)=>{r(r=>({...r,[e]:{...r[e],...t}})),c(!0)},saveSettings:m,resetSettings:e=>{e?r(t=>({...t,[e]:n[e]})):r(n),c(!0)},isLoading:o,hasUnsavedChanges:d},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},25654:(e,t,r)=>{Promise.resolve().then(r.bind(r,9887))},27107:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx","useAuth");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx","AuthProvider")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32270:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["landing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,14056)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/landing/page",pathname:"/landing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},41179:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},53411:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},61135:()=>{},62606:(e,t,r)=>{Promise.resolve().then(r.bind(r,14056))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>o});var s=r(60687),a=r(43210);let i=(0,a.createContext)(void 0);function n(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function o({children:e}){let[t,r]=(0,a.useState)(null),[n,o]=(0,a.useState)(!0),l=async(e,t)=>{o(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==t)return o(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return r(e),localStorage.setItem("revantad_user",JSON.stringify(e)),o(!1),!0}}catch(e){return console.error("Login error:",e),o(!1),!1}};return(0,s.jsx)(i.Provider,{value:{user:t,login:l,logout:()=>{r(null),localStorage.removeItem("revantad_user")},isLoading:n,isAuthenticated:!!t},children:e})}},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},69500:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx","ThemeProvider")},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72156:(e,t,r)=>{Promise.resolve().then(r.bind(r,14138)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,22362))},79551:e=>{"use strict";e.exports=require("url")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>m});var s=r(37413),a=r(35759),i=r.n(a),n=r(12552),o=r.n(n);r(61135);var l=r(69500),d=r(29131),c=r(19864);let m={title:"Revantad Store - Professional Admin Dashboard",description:"Modern admin dashboard for managing your Revantad Store with product lists, customer debt tracking, and business analytics",keywords:"sari-sari store, admin dashboard, product management, customer debt, business analytics, Philippines"};function h({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"light",enableSystem:!1,storageKey:"revantad-theme",themes:["light","dark"],children:(0,s.jsx)(d.AuthProvider,{children:(0,s.jsx)(c.SettingsProvider,{children:e})})})})})}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,215,658,201],()=>r(32270));module.exports=s})();