(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>d,AuthProvider:()=>n});var r=a(5155),s=a(2115);let l=(0,s.createContext)(void 0);function d(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function n(e){let{children:t}=e,[a,d]=(0,s.useState)(null),[n,i]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=localStorage.getItem("revantad_user");if(e)try{let t=JSON.parse(e);d(t)}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("revantad_user")}i(!1)},[]);let c=async(e,t)=>{i(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==t)return i(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return d(e),localStorage.setItem("revantad_user",JSON.stringify(e)),i(!1),!0}}catch(e){return console.error("Login error:",e),i(!1),!1}};return(0,r.jsx)(l.Provider,{value:{user:a,login:c,logout:()=>{d(null),localStorage.removeItem("revantad_user")},isLoading:n,isAuthenticated:!!a},children:t})}},2138:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2308:(e,t,a)=>{Promise.resolve().then(a.bind(a,6303))},2657:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},5937:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]])},6303:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(5155),s=a(6874),l=a.n(s),d=a(5695),n=a(2115),i=a(6408),c=a(8883);let o=(0,a(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var m=a(8749),x=a(2657),h=a(2138),u=a(5937),g=a(283);function y(){let[e,t]=(0,n.useState)(""),[a,s]=(0,n.useState)(""),[y,p]=(0,n.useState)(!1),[v,f]=(0,n.useState)(""),[b,j]=(0,n.useState)(!1),{login:N,isAuthenticated:k,isLoading:w}=(0,g.A)(),A=(0,d.useRouter)();(0,n.useEffect)(()=>{k&&!w&&A.push("/admin")},[k,w,A]);let S=async t=>{if(t.preventDefault(),f(""),j(!0),!e||!a){f("Please fill in all fields"),j(!1);return}await N(e,a)?A.push("/admin"):f("Invalid email or password"),j(!1)};return w?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen flex",children:[(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)(l(),{href:"/landing",className:"inline-flex items-center space-x-2 mb-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 hero-gradient rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"R"})}),(0,r.jsx)("span",{className:"text-2xl font-bold text-gradient",children:"Revantad Store"})]}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Welcome Back"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Sign in to your admin dashboard"})]}),(0,r.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-400 mb-2",children:"Demo Credentials"}),(0,r.jsxs)("div",{className:"text-sm text-green-700 dark:text-green-300 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Password:"})," admin123"]})]})]}),(0,r.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[v&&(0,r.jsx)(i.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:v})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,r.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-800 dark:text-white",placeholder:"Enter your email",required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,r.jsx)("input",{id:"password",type:y?"text":"password",value:a,onChange:e=>s(e.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-800 dark:text-white",placeholder:"Enter your password",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>p(!y),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:y?(0,r.jsx)(m.A,{className:"h-5 w-5"}):(0,r.jsx)(x.A,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("button",{type:"submit",disabled:b,className:"w-full btn-primary flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:b?(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,r.jsxs)(r.Fragment,{children:["Sign In",(0,r.jsx)(h.A,{className:"ml-2 h-4 w-4"})]})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",(0,r.jsx)(l(),{href:"/landing",className:"text-green-600 dark:text-green-400 hover:underline",children:"Contact Administrator"})]})})]})}),(0,r.jsx)("div",{className:"hidden lg:flex lg:flex-1 bg-gradient-to-br from-green-500 via-green-600 to-yellow-400 items-center justify-center p-12",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.2},className:"text-center text-white",children:[(0,r.jsx)(u.A,{className:"h-24 w-24 mx-auto mb-6 opacity-90"}),(0,r.jsx)("h2",{className:"text-4xl font-bold mb-4",children:"Manage Your Store"}),(0,r.jsx)("p",{className:"text-xl text-green-100 max-w-md",children:"Professional admin dashboard for your Revantad Store with advanced analytics and management tools."}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"247"}),(0,r.jsx)("div",{className:"text-green-100",children:"Products"})]}),(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"₱45K"}),(0,r.jsx)("div",{className:"text-green-100",children:"Revenue"})]})]})]})})]})}},8749:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[142,441,684,358],()=>t(2308)),_N_E=e.O()}]);