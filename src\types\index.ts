// Shared TypeScript types and interfaces for the application

// Import database types
import type { Product, CustomerDebt } from '@/lib/supabase'

// Re-export database types
export type { Product, CustomerDebt }

// Common UI component props
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// Modal component props
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
}

// Loading states
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Form validation states
export interface FormState<T = unknown> extends LoadingState {
  data: T | null
  isDirty: boolean
  isValid: boolean
}

// Dashboard statistics
export interface DashboardStats {
  totalProducts: number
  totalDebts: number
  totalDebtAmount: number
  lowStockItems: number
  recentProducts: Product[]
  recentDebts: CustomerDebt[]
}

// Navigation items
export interface NavigationItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  tooltip?: string
  badge?: number
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system'

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Pagination types
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Search and filter types
export interface SearchFilters {
  query?: string
  category?: string
  dateFrom?: string
  dateTo?: string
  status?: string
}

// Component size variants
export type ComponentSize = 'sm' | 'md' | 'lg' | 'xl'
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error'

// Event handler types
export type EventHandler<T = Event> = (event: T) => void
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
