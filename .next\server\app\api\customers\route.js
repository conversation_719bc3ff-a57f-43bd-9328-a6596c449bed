(()=>{var e={};e.id=941,e.ids=[941],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,r,t)=>{"use strict";t.d(r,{$W:()=>i});var s=t(72289),o=t(35282);let n=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),a=function(){try{return n.parse(process.env)}catch(e){if(e instanceof o.G){let r=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${r.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===a.NODE_ENV,isProduction:"production"===a.NODE_ENV,isTest:"test"===a.NODE_ENV,database:{url:a.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:a.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:a.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:a.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:a.CLOUDINARY_API_KEY,apiSecret:a.CLOUDINARY_API_SECRET},ai:{geminiApiKey:a.GEMINI_API_KEY},auth:{secret:a.NEXTAUTH_SECRET,url:a.NEXTAUTH_URL},debug:a.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:l,NEXT_PUBLIC_SUPABASE_ANON_KEY:c,SUPABASE_SERVICE_ROLE_KEY:p,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:m,NEXTAUTH_URL:R,DEBUG:f,GEMINI_API_KEY:N}=a},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},53171:(e,r,t)=>{"use strict";t.d(r,{FB:()=>u,Rv:()=>c,Y8:()=>_,ie:()=>p,r6:()=>a,sv:()=>l,u3:()=>d,yj:()=>i});var s=t(32190);let o={OK:200,BAD_REQUEST:400,CONFLICT:409,INTERNAL_SERVER_ERROR:500};class n extends Error{constructor(e,r=o.INTERNAL_SERVER_ERROR,t){super(e),this.message=e,this.statusCode=r,this.code=t,this.name="ApiError"}}function a(e,r,t=o.OK){let n={success:!0,data:e};return r&&(n.message=r),s.NextResponse.json(n,{status:t})}function i(e,r=o.INTERNAL_SERVER_ERROR,t){return s.NextResponse.json({success:!1,error:e,code:t},{status:r})}function u(e){return async(...r)=>{try{return await e(...r)}catch(e){if(console.error("API Error:",e),e instanceof n)return i(e.message,e.statusCode,e.code);if(e instanceof Error)return i(e.message);return i("An unexpected error occurred")}}}async function l(e,r){try{let t=await e.json();return r(t)}catch{throw new n("Invalid request body",o.BAD_REQUEST)}}function c(e,r){let t=r.filter(r=>void 0===e[r]||null===e[r]||""===e[r]);if(t.length>0)throw new n(`Missing required fields: ${t.join(", ")}`,o.BAD_REQUEST)}function p(e){if(console.error("Database error:",e),"23505"===e.code)throw new n("Resource already exists",o.CONFLICT);if("23503"===e.code)throw new n("Referenced resource not found",o.BAD_REQUEST);if("23502"===e.code)throw new n("Required field is missing",o.BAD_REQUEST);throw new n("Database operation failed",o.INTERNAL_SERVER_ERROR)}function d(e,r={}){let{maxLimit:t=100}=r,s=Math.max(1,parseInt(e.get("page")||"1",10)),o=Math.min(t,Math.max(1,parseInt(e.get("limit")||"10",10)));return{page:s,limit:o,offset:(s-1)*o}}let E={"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"};function _(){return new s.NextResponse(null,{status:200,headers:E})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(66437),o=t(34960);let n=(0,s.UU)(o.$W.database.url,o.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77713:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>E,serverHooks:()=>R,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p,OPTIONS:()=>c,POST:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(53171),l=t(56621);async function c(){return(0,u.Y8)()}let p=(0,u.FB)(async e=>{let{searchParams:r}=new URL(e.url),{page:t,limit:s,offset:o}=(0,u.u3)(r),n=r.get("search"),a=l.N.from("customers").select("*",{count:"exact"}).order("created_at",{ascending:!1}).range(o,o+s-1);n&&(a=a.or(`customer_name.ilike.%${n}%,customer_family_name.ilike.%${n}%,phone_number.ilike.%${n}%`));let{data:i,error:c,count:p}=await a;return c?(0,u.ie)(c):(0,u.r6)({customers:i,pagination:{page:t,limit:s,total:p||0,totalPages:Math.ceil((p||0)/s)}})});async function d(e){try{let{customer_name:r,customer_family_name:t,profile_picture_url:s,profile_picture_public_id:o,phone_number:n,address:a,notes:u}=await e.json();if(!r||!t)return i.NextResponse.json({error:"Customer name and family name are required"},{status:400});let{data:c}=await l.N.from("customers").select("id").eq("customer_name",r).eq("customer_family_name",t).single();if(c){let{data:e,error:r}=await l.N.from("customers").update({profile_picture_url:s||null,profile_picture_public_id:o||null,phone_number:n||null,address:a||null,notes:u||null}).eq("id",c.id).select().single();if(r)return i.NextResponse.json({error:r.message},{status:500});return i.NextResponse.json({customer:e},{status:200})}{let{data:e,error:c}=await l.N.from("customers").insert([{customer_name:r,customer_family_name:t,profile_picture_url:s||null,profile_picture_public_id:o||null,phone_number:n||null,address:a||null,notes:u||null}]).select().single();if(c)return i.NextResponse.json({error:c.message},{status:500});return i.NextResponse.json({customer:e},{status:201})}}catch(e){return console.error("Error managing customer:",e),i.NextResponse.json({error:"Failed to manage customer record"},{status:500})}}let E=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/customers/route",pathname:"/api/customers",filename:"route",bundlePath:"app/api/customers/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\customers\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:m,serverHooks:R}=E;function f(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:m})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,289,437],()=>t(77713));module.exports=s})();