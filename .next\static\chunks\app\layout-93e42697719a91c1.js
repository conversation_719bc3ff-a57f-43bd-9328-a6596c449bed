(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>i});var a=r(5155),n=r(2115);let s=(0,n.createContext)(void 0);function o(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i(e){let{children:t}=e,[r,o]=(0,n.useState)(null),[i,l]=(0,n.useState)(!0);(0,n.useEffect)(()=>{let e=localStorage.getItem("revantad_user");if(e)try{let t=JSON.parse(e);o(t)}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("revantad_user")}l(!1)},[]);let c=async(e,t)=>{l(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==t)return l(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return o(e),localStorage.setItem("revantad_user",JSON.stringify(e)),l(!1),!0}}catch(e){return console.error("Login error:",e),l(!1),!1}};return(0,a.jsx)(s.Provider,{value:{user:r,login:c,logout:()=>{o(null),localStorage.removeItem("revantad_user")},isLoading:i,isAuthenticated:!!r},children:t})}},347:()=>{},408:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>i,t:()=>l});var a=r(5155),n=r(2115);let s=(0,n.createContext)(void 0),o={store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",website:"https://revantadstore.com",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"},operatingDays:["monday","tuesday","wednesday","thursday","friday","saturday"],businessRegistration:{registrationNumber:"REG-2024-001",taxId:"TAX-*********",businessType:"Retail",registrationDate:"2024-01-01"},locations:[{id:1,name:"Main Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",isMain:!0}],branding:{logo:null,primaryColor:"#22c55e",secondaryColor:"#facc15",slogan:"Your Neighborhood Store"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner",avatar:null,bio:"Experienced store owner managing Revantad Store operations.",dateOfBirth:"1990-01-01",address:"123 Barangay Street, Manila, Philippines",emergencyContact:{name:"Emergency Contact",phone:"+63 ************",relationship:"Family"},preferences:{language:"en",timezone:"Asia/Manila",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"}},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,channels:{email:"<EMAIL>",sms:"+63 ************",webhook:""},customRules:[{id:1,name:"Critical Stock Alert",condition:"stock < 5",action:"email + sms",enabled:!0}],templates:{lowStock:"Product {{productName}} is running low ({{currentStock}} remaining)",newDebt:"New debt recorded for {{customerName}}: ₱{{amount}}",paymentReceived:"Payment received from {{customerName}}: ₱{{amount}}"}},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5",currentPassword:"",newPassword:"",confirmPassword:"",apiKeys:[{id:1,name:"Main API Key",key:"sk_live_***************",created:"2024-01-01",lastUsed:"2024-01-20",permissions:["read","write"]}],loginHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",ip:"***********",device:"Chrome on Windows",location:"Manila, Philippines",success:!0}],passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!0}},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US",colorScheme:{primary:"#22c55e",secondary:"#facc15",accent:"#3b82f6",background:"#ffffff",surface:"#f8fafc"},layout:{sidebarPosition:"left",density:"comfortable",showAnimations:!0,compactMode:!1},typography:{fontFamily:"Inter",fontSize:"medium",fontWeight:"normal"}},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z",cloudStorage:{provider:"local",bucket:"",accessKey:"",secretKey:""},backupHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",size:"2.5 MB",status:"completed",type:"automatic"},{id:2,timestamp:"2024-01-19T10:30:00Z",size:"2.4 MB",status:"completed",type:"automatic"}],verification:{enabled:!0,lastVerified:"2024-01-20T10:35:00Z",status:"verified"}}};function i(e){let{children:t}=e,[r,i]=(0,n.useState)(o),[l,c]=(0,n.useState)(!1),[m,d]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=localStorage.getItem("revantad-settings");if(e)try{let t=JSON.parse(e);i({...o,...t})}catch(e){console.error("Error loading settings:",e)}},[]);let u=async()=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),localStorage.setItem("revantad-settings",JSON.stringify(r)),d(!1),"dark"===r.appearance.theme?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),console.warn("Settings saved successfully:",r)}catch(e){throw console.error("Error saving settings:",e),e}finally{c(!1)}};return(0,a.jsx)(s.Provider,{value:{settings:r,updateSettings:(e,t)=>{i(r=>({...r,[e]:{...r[e],...t}})),d(!0)},saveSettings:u,resetSettings:e=>{e?i(t=>({...t,[e]:o[e]})):i(o),d(!0)},isLoading:l,hasUnsavedChanges:m},children:t})}function l(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>c,N:()=>m});var a=r(2115),n=(e,t,r,a,n,s,o,i)=>{let l=document.documentElement,c=["light","dark"];function m(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,a=r&&s?n.map(e=>s[e]||e):n;r?(l.classList.remove(...a),l.classList.add(s&&s[t]?s[t]:t)):l.setAttribute(e,t)}),r=t,i&&c.includes(r)&&(l.style.colorScheme=r)}if(a)m(a);else try{let e=localStorage.getItem(t)||r,a=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;m(a)}catch(e){}},s=["light","dark"],o="(prefers-color-scheme: dark)",i=a.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=a.useContext(i))?e:l},m=e=>a.useContext(i)?a.createElement(a.Fragment,null,e.children):a.createElement(u,{...e}),d=["light","dark"],u=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:c="theme",themes:m=d,defaultTheme:u=n?"system":"light",attribute:g="data-theme",value:v,children:b,nonce:S,scriptProps:w}=e,[k,P]=a.useState(()=>p(c,u)),[E,N]=a.useState(()=>"system"===k?f():k),C=v?Object.values(v):m,T=a.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=f());let a=v?v[t]:t,o=r?y(S):null,i=document.documentElement,c=e=>{"class"===e?(i.classList.remove(...C),a&&i.classList.add(a)):e.startsWith("data-")&&(a?i.setAttribute(e,a):i.removeAttribute(e))};if(Array.isArray(g)?g.forEach(c):c(g),l){let e=s.includes(u)?u:null,r=s.includes(t)?t:e;i.style.colorScheme=r}null==o||o()},[S]),_=a.useCallback(e=>{let t="function"==typeof e?e(k):e;P(t);try{localStorage.setItem(c,t)}catch(e){}},[k]),A=a.useCallback(e=>{N(f(e)),"system"===k&&n&&!t&&T("system")},[k,t]);a.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),a.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?P(e.newValue):_(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[_]),a.useEffect(()=>{T(null!=t?t:k)},[t,k]);let M=a.useMemo(()=>({theme:k,setTheme:_,forcedTheme:t,resolvedTheme:"system"===k?E:k,themes:n?[...m,"system"]:m,systemTheme:n?E:void 0}),[k,_,t,E,n,m]);return a.createElement(i.Provider,{value:M},a.createElement(h,{forcedTheme:t,storageKey:c,attribute:g,enableSystem:n,enableColorScheme:l,defaultTheme:u,value:v,themes:m,nonce:S,scriptProps:w}),b)},h=a.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:s,enableSystem:o,enableColorScheme:i,defaultTheme:l,value:c,themes:m,nonce:d,scriptProps:u}=e,h=JSON.stringify([s,r,l,t,m,c,o,i]).slice(1,-1);return a.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(h,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},f=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},3780:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5302,23)),Promise.resolve().then(r.t.bind(r,7469,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,7280)),Promise.resolve().then(r.bind(r,283)),Promise.resolve().then(r.bind(r,408))},5302:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},7280:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var a=r(5155),n=r(1362);function s(e){let{children:t,...r}=e;return(0,a.jsx)(n.N,{...r,children:t})}},7469:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_0de778",variable:"__variable_0de778"}}},e=>{var t=t=>e(e.s=t);e.O(0,[842,690,441,684,358],()=>t(3780)),_N_E=e.O()}]);