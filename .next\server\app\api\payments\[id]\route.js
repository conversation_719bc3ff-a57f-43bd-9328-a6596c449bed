(()=>{var e={};e.id=135,e.ids=[135],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4630:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>N,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>p,PUT:()=>l});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),u=r(56621);async function p(e,{params:t}){try{let{id:e}=await t,{data:r,error:s}=await u.N.from("customer_payments").select("*").eq("id",e).single();if(s)return i.NextResponse.json({error:s.message},{status:500});if(!r)return i.NextResponse.json({error:"Payment record not found"},{status:404});return i.NextResponse.json({payment:r})}catch{return i.NextResponse.json({error:"Failed to fetch payment record"},{status:500})}}async function l(e,{params:t}){try{let{id:r}=await t,{customer_name:s,customer_family_name:a,payment_amount:o,payment_date:n,payment_method:p,notes:l}=await e.json();if(!s||!a||!o||o<=0)return i.NextResponse.json({error:"Missing required fields or invalid payment amount"},{status:400});let{data:c,error:d}=await u.N.from("customer_payments").update({customer_name:s,customer_family_name:a,payment_amount:parseFloat(o),payment_date:n||new Date().toISOString().split("T")[0],payment_method:p||"Cash",notes:l||null}).eq("id",r).select().single();if(d)return i.NextResponse.json({error:d.message},{status:500});return i.NextResponse.json({payment:c})}catch{return i.NextResponse.json({error:"Failed to update payment record"},{status:500})}}async function c(e,{params:t}){try{let{id:e}=await t,{error:r}=await u.N.from("customer_payments").delete().eq("id",e);if(r)return i.NextResponse.json({error:r.message},{status:500});return i.NextResponse.json({message:"Payment record deleted successfully"})}catch{return i.NextResponse.json({error:"Failed to delete payment record"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/payments/[id]/route",pathname:"/api/payments/[id]",filename:"route",bundlePath:"app/api/payments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\payments\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:_,serverHooks:m}=d;function N(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:_})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,t,r)=>{"use strict";r.d(t,{$W:()=>i});var s=r(72289),a=r(35282);let o=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),n=function(){try{return o.parse(process.env)}catch(e){if(e instanceof a.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===n.NODE_ENV,isProduction:"production"===n.NODE_ENV,isTest:"test"===n.NODE_ENV,database:{url:n.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:n.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:n.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:n.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:n.CLOUDINARY_API_KEY,apiSecret:n.CLOUDINARY_API_SECRET},ai:{geminiApiKey:n.GEMINI_API_KEY},auth:{secret:n.NEXTAUTH_SECRET,url:n.NEXTAUTH_URL},debug:n.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:p,NEXT_PUBLIC_SUPABASE_ANON_KEY:l,SUPABASE_SERVICE_ROLE_KEY:c,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:m,NEXTAUTH_URL:N,DEBUG:x,GEMINI_API_KEY:y}=n},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(66437),a=r(34960);let o=(0,s.UU)(a.$W.database.url,a.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,289,437],()=>r(4630));module.exports=s})();