(()=>{var e={};e.id=520,e.ids=[520],e.modules={60:(e,t,r)=>{Promise.resolve().then(r.bind(r,94934))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9788:(e,t,r)=>{Promise.resolve().then(r.bind(r,60464))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},60464:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),a=r(85814),n=r.n(a),i=r(16189),l=r(43210),d=r(26001),o=r(41550);let c=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var x=r(12597),m=r(13861),p=r(70334),h=r(46001),u=r(63213);function g(){let[e,t]=(0,l.useState)(""),[r,a]=(0,l.useState)(""),[g,b]=(0,l.useState)(!1),[f,v]=(0,l.useState)(""),[y,j]=(0,l.useState)(!1),{login:w,isAuthenticated:N,isLoading:k}=(0,u.A)(),P=(0,i.useRouter)(),D=async t=>{if(t.preventDefault(),v(""),j(!0),!e||!r){v("Please fill in all fields"),j(!1);return}await w(e,r)?P.push("/admin"):v("Invalid email or password"),j(!1)};return k?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,s.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen flex",children:[(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900",children:(0,s.jsxs)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)(n(),{href:"/landing",className:"inline-flex items-center space-x-2 mb-6",children:[(0,s.jsx)("div",{className:"w-12 h-12 hero-gradient rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"R"})}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gradient",children:"Revantad Store"})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Welcome Back"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Sign in to your admin dashboard"})]}),(0,s.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-400 mb-2",children:"Demo Credentials"}),(0,s.jsxs)("div",{className:"text-sm text-green-700 dark:text-green-300 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Password:"})," admin123"]})]})]}),(0,s.jsxs)("form",{onSubmit:D,className:"space-y-6",children:[f&&(0,s.jsx)(d.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:f})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-800 dark:text-white",placeholder:"Enter your email",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)("input",{id:"password",type:g?"text":"password",value:r,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-800 dark:text-white",placeholder:"Enter your password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>b(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:g?(0,s.jsx)(x.A,{className:"h-5 w-5"}):(0,s.jsx)(m.A,{className:"h-5 w-5"})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:y,className:"w-full btn-primary flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Sign In",(0,s.jsx)(p.A,{className:"ml-2 h-4 w-4"})]})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/landing",className:"text-green-600 dark:text-green-400 hover:underline",children:"Contact Administrator"})]})})]})}),(0,s.jsx)("div",{className:"hidden lg:flex lg:flex-1 bg-gradient-to-br from-green-500 via-green-600 to-yellow-400 items-center justify-center p-12",children:(0,s.jsxs)(d.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.2},className:"text-center text-white",children:[(0,s.jsx)(h.A,{className:"h-24 w-24 mx-auto mb-6 opacity-90"}),(0,s.jsx)("h2",{className:"text-4xl font-bold mb-4",children:"Manage Your Store"}),(0,s.jsx)("p",{className:"text-xl text-green-100 max-w-md",children:"Professional admin dashboard for your Revantad Store with advanced analytics and management tools."}),(0,s.jsxs)("div",{className:"mt-8 grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:"247"}),(0,s.jsx)("div",{className:"text-green-100",children:"Products"})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:"₱45K"}),(0,s.jsx)("div",{className:"text-green-100",children:"Revenue"})]})]})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},79551:e=>{"use strict";e.exports=require("url")},94934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx","default")},97138:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94934)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,215,658,201,534],()=>r(97138));module.exports=s})();