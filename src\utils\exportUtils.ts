import { Product, CustomerDebt } from '@/lib/supabase'

// Export products to CSV
export function exportProductsToCSV(products: Product[], filename = 'products') {
  const headers = [
    'ID',
    'Name',
    'Category',
    'Price',
    'Stock Quantity',
    'Net Weight',
    'Image URL',
    'Created At',
    'Updated At'
  ]

  const csvContent = [
    headers.join(','),
    ...products.map(product => [
      product.id,
      `"${product.name.replace(/"/g, '""')}"`, // Escape quotes
      `"${product.category}"`,
      product.price,
      product.stock_quantity,
      `"${product.net_weight}"`,
      product.image_url || '',
      product.created_at,
      product.updated_at
    ].join(','))
  ].join('\n')

  downloadFile(csvContent, `${filename}.csv`, 'text/csv')
}

// Export products to JSON
export function exportProductsToJSON(products: Product[], filename = 'products') {
  const jsonContent = JSON.stringify(products, null, 2)
  downloadFile(jsonContent, `${filename}.json`, 'application/json')
}

// Export debts to CSV
export function exportDebtsToCSV(debts: CustomerDebt[], filename = 'customer_debts') {
  const headers = [
    'ID',
    'Customer Name',
    'Total Amount',
    'Product Name',
    'Quantity',
    'Debt Date',
    'Created At',
    'Updated At'
  ]

  const csvContent = [
    headers.join(','),
    ...debts.map(debt => [
      debt.id,
      `"${debt.customer_name.replace(/"/g, '""')}"`,
      debt.total_amount,
      `"${debt.product_name?.replace(/"/g, '""') || ''}"`,
      debt.quantity,
      debt.debt_date || '',
      debt.created_at,
      debt.updated_at
    ].join(','))
  ].join('\n')

  downloadFile(csvContent, `${filename}.csv`, 'text/csv')
}

// Generic download function
function downloadFile(content: string, filename: string, mimeType: string) {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

// Generate product analytics report
export function generateProductAnalyticsReport(products: Product[]) {
  const totalProducts = products.length
  const totalValue = products.reduce((sum, product) => sum + (product.price * product.stock_quantity), 0)
  const lowStockItems = products.filter(product => product.stock_quantity < 10).length
  const outOfStockItems = products.filter(product => product.stock_quantity === 0).length
  
  const categoryStats = products.reduce((stats, product) => {
    if (!stats[product.category]) {
      stats[product.category] = {
        count: 0,
        totalValue: 0,
        averagePrice: 0,
        totalStock: 0
      }
    }
    
    stats[product.category].count++
    stats[product.category].totalValue += product.price * product.stock_quantity
    stats[product.category].totalStock += product.stock_quantity
    
    return stats
  }, {} as Record<string, { count: number; totalValue: number; totalStock: number; averagePrice?: number }>)

  // Calculate average prices
  Object.keys(categoryStats).forEach(category => {
    const categoryProducts = products.filter(p => p.category === category)
    categoryStats[category].averagePrice = 
      categoryProducts.reduce((sum, p) => sum + p.price, 0) / categoryProducts.length
  })

  const report = {
    summary: {
      totalProducts,
      totalValue: totalValue.toFixed(2),
      lowStockItems,
      outOfStockItems,
      averageProductValue: (totalValue / totalProducts).toFixed(2)
    },
    categoryBreakdown: categoryStats,
    generatedAt: new Date().toISOString()
  }

  return report
}

// Export analytics report
export function exportAnalyticsReport(products: Product[], filename = 'product_analytics') {
  const report = generateProductAnalyticsReport(products)
  const jsonContent = JSON.stringify(report, null, 2)
  downloadFile(jsonContent, `${filename}.json`, 'application/json')
}

// Format currency for display
export function formatCurrency(amount: number, currency = '₱') {
  return `${currency}${amount.toFixed(2)}`
}

// Format date for display
export function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Generate stock status
export function getStockStatus(quantity: number) {
  if (quantity === 0) return { status: 'out-of-stock', label: 'Out of Stock', color: 'red' }
  if (quantity < 10) return { status: 'low-stock', label: 'Low Stock', color: 'orange' }
  return { status: 'in-stock', label: 'In Stock', color: 'green' }
}
