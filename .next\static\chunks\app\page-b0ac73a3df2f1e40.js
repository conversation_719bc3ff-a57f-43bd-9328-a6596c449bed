(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(5155),n=s(5695),a=s(2115);function i(){let e=(0,n.useRouter)();return(0,a.useEffect)(()=>{e.push("/landing")},[e]),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gradient mb-2",children:"Revantad Store"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Redirecting to landing page..."})]})})}},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},6799:(e,t,s)=>{Promise.resolve().then(s.bind(s,3792))}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(6799)),_N_E=e.O()}]);