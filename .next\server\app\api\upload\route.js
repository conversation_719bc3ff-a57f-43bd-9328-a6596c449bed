(()=>{var e={};e.id=413,e.ids=[413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5984:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>A,routeModule:()=>d,serverHooks:()=>N,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>E});var o={};t.r(o),t.d(o,{POST:()=>c});var a=t(96559),s=t(48088),i=t(37719),u=t(32190),n=t(71870),p=t(34960);p.$W.cloudinary.apiKey&&p.$W.cloudinary.apiSecret&&n.v2.config({cloud_name:p.$W.cloudinary.cloudName,api_key:p.$W.cloudinary.apiKey,api_secret:p.$W.cloudinary.apiSecret});let l=n.v2;async function c(e){try{let r=(await e.formData()).get("file");if(!r)return u.NextResponse.json({error:"No file provided"},{status:400});let t=await r.arrayBuffer(),o=Buffer.from(t),a=await new Promise((e,r)=>{l.uploader.upload_stream({resource_type:"image",folder:"sari-sari-products",transformation:[{width:500,height:500,crop:"limit"},{quality:"auto"},{format:"auto"}]},(t,o)=>{t?r(t):o?e({secure_url:o.secure_url,public_id:o.public_id}):r(Error("Upload failed"))}).end(o)});return u.NextResponse.json({url:a.secure_url,public_id:a.public_id})}catch(e){return console.error("Upload error:",e),u.NextResponse.json({error:"Failed to upload image"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:_,workUnitAsyncStorage:E,serverHooks:N}=d;function A(){return(0,i.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:E})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34960:(e,r,t)=>{"use strict";t.d(r,{$W:()=>u});var o=t(72289),a=t(35282);let s=o.Ik({NODE_ENV:o.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:o.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:o.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:o.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:o.Yj().optional(),CLOUDINARY_API_KEY:o.Yj().optional(),CLOUDINARY_API_SECRET:o.Yj().optional(),GEMINI_API_KEY:o.Yj().optional(),NEXTAUTH_SECRET:o.Yj().optional(),NEXTAUTH_URL:o.Yj().optional(),DEBUG:o.Yj().transform(e=>"true"===e).default("false")}),i=function(){try{return s.parse(process.env)}catch(e){if(e instanceof a.G){let r=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${r.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),u={isDevelopment:"development"===i.NODE_ENV,isProduction:"production"===i.NODE_ENV,isTest:"test"===i.NODE_ENV,database:{url:i.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:i.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:i.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:i.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:i.CLOUDINARY_API_KEY,apiSecret:i.CLOUDINARY_API_SECRET},ai:{geminiApiKey:i.GEMINI_API_KEY},auth:{secret:i.NEXTAUTH_SECRET,url:i.NEXTAUTH_URL},debug:i.DEBUG},{NODE_ENV:n,NEXT_PUBLIC_SUPABASE_URL:p,NEXT_PUBLIC_SUPABASE_ANON_KEY:l,SUPABASE_SERVICE_ROLE_KEY:c,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:d,CLOUDINARY_API_KEY:_,CLOUDINARY_API_SECRET:E,NEXTAUTH_SECRET:N,NEXTAUTH_URL:A,DEBUG:U,GEMINI_API_KEY:m}=i},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,580,289,870],()=>t(5984));module.exports=o})();