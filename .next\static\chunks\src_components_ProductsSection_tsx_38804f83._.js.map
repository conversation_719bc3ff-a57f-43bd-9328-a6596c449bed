{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductsSection.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  Plus, Edit, Trash2, Search, Package, Filter, SortAsc, SortDesc,\n  Grid, List, Download, AlertTriangle, CheckSquare, Square, Minus,\n  X, ChevronDown, BarChart3, RefreshCw, ZoomIn\n} from 'lucide-react'\nimport Image from 'next/image'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect, useMemo, useCallback } from 'react'\n\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\nimport LoadingSkeleton from './LoadingSkeleton'\nimport ProductModal from './ProductModal'\nimport ProductQuickActions from './ProductQuickActions'\nimport ProductImageZoom, { useImageZoom } from './ProductImageZoom'\nimport { exportProductsToCSV, exportProductsToJSON, exportAnalyticsReport } from '@/utils/exportUtils'\n\ninterface ProductsSectionProps {\n  onStatsUpdate: () => void\n}\n\n// Enhanced filter and sort types\ntype SortOption = 'name' | 'price' | 'stock' | 'category' | 'created_at'\ntype SortDirection = 'asc' | 'desc'\ntype ViewMode = 'grid' | 'list'\n\ninterface FilterOptions {\n  category: string\n  priceRange: { min: number; max: number }\n  stockRange: { min: number; max: number }\n  lowStock: boolean\n  outOfStock: boolean\n}\n\nexport default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n\n  // Advanced features state\n  const [viewMode, setViewMode] = useState<ViewMode>('grid')\n  const [sortBy, setSortBy] = useState<SortOption>('created_at')\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')\n  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set())\n  const [showFilters, setShowFilters] = useState(false)\n  const [filters, setFilters] = useState<FilterOptions>({\n    category: '',\n    priceRange: { min: 0, max: 10000 },\n    stockRange: { min: 0, max: 1000 },\n    lowStock: false,\n    outOfStock: false\n  })\n  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])\n  const [showSuggestions, setShowSuggestions] = useState(false)\n\n  // Image zoom functionality\n  const { zoomImage, openZoom, closeZoom, isZoomOpen } = useImageZoom()\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  // Advanced search suggestions\n  const generateSearchSuggestions = useCallback((term: string) => {\n    if (!term || term.length < 2) {\n      setSearchSuggestions([])\n      return\n    }\n\n    const suggestions = products\n      .filter(product =>\n        product.name.toLowerCase().includes(term.toLowerCase()) ||\n        product.category.toLowerCase().includes(term.toLowerCase())\n      )\n      .map(product => product.name)\n      .slice(0, 5)\n\n    setSearchSuggestions([...new Set(suggestions)])\n  }, [products])\n\n  // Advanced filtering and sorting logic\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      // Text search\n      const matchesSearch = !searchTerm ||\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.category.toLowerCase().includes(searchTerm.toLowerCase())\n\n      // Category filter\n      const matchesCategory = !filters.category || product.category === filters.category\n\n      // Price range filter\n      const matchesPrice = product.price >= filters.priceRange.min &&\n        product.price <= filters.priceRange.max\n\n      // Stock range filter\n      const matchesStock = product.stock_quantity >= filters.stockRange.min &&\n        product.stock_quantity <= filters.stockRange.max\n\n      // Low stock filter\n      const matchesLowStock = !filters.lowStock || product.stock_quantity < 10\n\n      // Out of stock filter\n      const matchesOutOfStock = !filters.outOfStock || product.stock_quantity === 0\n\n      return matchesSearch && matchesCategory && matchesPrice &&\n        matchesStock && matchesLowStock && matchesOutOfStock\n    })\n\n    // Sorting\n    filtered.sort((a, b) => {\n      let aValue: any, bValue: any\n\n      switch (sortBy) {\n        case 'name':\n          aValue = a.name.toLowerCase()\n          bValue = b.name.toLowerCase()\n          break\n        case 'price':\n          aValue = a.price\n          bValue = b.price\n          break\n        case 'stock':\n          aValue = a.stock_quantity\n          bValue = b.stock_quantity\n          break\n        case 'category':\n          aValue = a.category.toLowerCase()\n          bValue = b.category.toLowerCase()\n          break\n        case 'created_at':\n          aValue = new Date(a.created_at).getTime()\n          bValue = new Date(b.created_at).getTime()\n          break\n        default:\n          return 0\n      }\n\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1\n      return 0\n    })\n\n    return filtered\n  }, [products, searchTerm, filters, sortBy, sortDirection])\n\n  const fetchProducts = async () => {\n    try {\n      // Using console.warn for informational messages (ESLint compliant)\n      console.warn('🔄 Fetching products...')\n      const response = await fetch('/api/products')\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const data = await response.json()\n      console.warn('📦 Products API response:', data)\n\n      // Handle new API structure: { success: true, data: { products: [...] } }\n      if (data.success && data.data && data.data.products) {\n        setProducts(data.data.products)\n        console.warn('✅ Products loaded (new structure):', data.data.products.length, 'items')\n      }\n      // Handle old API structure: { products: [...] }\n      else if (data.products) {\n        setProducts(data.products)\n        console.warn('✅ Products loaded (old structure):', data.products.length, 'items')\n      }\n      // Handle direct array\n      else if (Array.isArray(data)) {\n        setProducts(data)\n        console.warn('✅ Products loaded (direct array):', data.length, 'items')\n      }\n      else {\n        console.warn('⚠️ Unexpected API response structure:', data)\n        setProducts([])\n      }\n    } catch (error) {\n      console.error('❌ Error fetching products:', error)\n      setProducts([])\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this product?')) return\n\n    try {\n      const response = await fetch(`/api/products/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error)\n    }\n  }\n\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingProduct(null)\n    fetchProducts()\n    onStatsUpdate()\n  }\n\n  // Bulk operations handlers\n  const handleSelectAll = () => {\n    if (selectedProducts.size === filteredAndSortedProducts.length) {\n      setSelectedProducts(new Set())\n    } else {\n      setSelectedProducts(new Set(filteredAndSortedProducts.map(p => p.id)))\n    }\n  }\n\n  const handleSelectProduct = (productId: string) => {\n    const newSelected = new Set(selectedProducts)\n    if (newSelected.has(productId)) {\n      newSelected.delete(productId)\n    } else {\n      newSelected.add(productId)\n    }\n    setSelectedProducts(newSelected)\n  }\n\n  const handleBulkDelete = async () => {\n    if (selectedProducts.size === 0) return\n\n    const confirmMessage = `Are you sure you want to delete ${selectedProducts.size} product(s)?`\n    if (!confirm(confirmMessage)) return\n\n    try {\n      const deletePromises = Array.from(selectedProducts).map(id =>\n        fetch(`/api/products/${id}`, { method: 'DELETE' })\n      )\n\n      await Promise.all(deletePromises)\n      setSelectedProducts(new Set())\n      fetchProducts()\n      onStatsUpdate()\n    } catch (error) {\n      console.error('Error deleting products:', error)\n    }\n  }\n\n  const handleBulkCategoryUpdate = async (newCategory: string) => {\n    if (selectedProducts.size === 0) return\n\n    try {\n      const updatePromises = Array.from(selectedProducts).map(id => {\n        const product = products.find(p => p.id === id)\n        if (!product) return Promise.resolve()\n\n        return fetch(`/api/products/${id}`, {\n          method: 'PUT',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ ...product, category: newCategory })\n        })\n      })\n\n      await Promise.all(updatePromises)\n      setSelectedProducts(new Set())\n      fetchProducts()\n      onStatsUpdate()\n    } catch (error) {\n      console.error('Error updating product categories:', error)\n    }\n  }\n\n  // Search handlers\n  const handleSearchChange = (value: string) => {\n    setSearchTerm(value)\n    generateSearchSuggestions(value)\n    setShowSuggestions(value.length >= 2)\n  }\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setSearchTerm(suggestion)\n    setShowSuggestions(false)\n  }\n\n  // Sort handlers\n  const handleSort = (option: SortOption) => {\n    if (sortBy === option) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')\n    } else {\n      setSortBy(option)\n      setSortDirection('asc')\n    }\n  }\n\n  // Export handlers\n  const handleExportCSV = () => {\n    exportProductsToCSV(filteredAndSortedProducts, `products_${new Date().toISOString().split('T')[0]}`)\n  }\n\n  const handleExportJSON = () => {\n    exportProductsToJSON(filteredAndSortedProducts, `products_${new Date().toISOString().split('T')[0]}`)\n  }\n\n  const handleExportAnalytics = () => {\n    exportAnalyticsReport(filteredAndSortedProducts, `product_analytics_${new Date().toISOString().split('T')[0]}`)\n  }\n\n\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header Skeleton */}\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex space-x-4\">\n            <div\n              className=\"w-64 h-10 rounded-lg\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',\n                backgroundImage: resolvedTheme === 'dark'\n                  ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'\n                  : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',\n                backgroundSize: '200% 100%',\n                animation: 'shimmer 2s infinite'\n              }}\n            />\n            <div\n              className=\"w-40 h-10 rounded-lg\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',\n                backgroundImage: resolvedTheme === 'dark'\n                  ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'\n                  : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',\n                backgroundSize: '200% 100%',\n                animation: 'shimmer 2s infinite'\n              }}\n            />\n          </div>\n          <div\n            className=\"w-48 h-10 rounded-lg\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',\n              backgroundImage: resolvedTheme === 'dark'\n                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'\n                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',\n              backgroundSize: '200% 100%',\n              animation: 'shimmer 2s infinite'\n            }}\n          />\n        </div>\n\n        {/* Products Grid Skeleton */}\n        <LoadingSkeleton type=\"products\" count={8} />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Enhanced Header with Advanced Controls */}\n      <div className=\"space-y-4\">\n        {/* Top Row - Search and Actions */}\n        <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4\">\n          {/* Search Section */}\n          <div className=\"flex flex-col sm:flex-row gap-3 flex-1\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                }}\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Search products by name or category...\"\n                value={searchTerm}\n                onChange={(e) => handleSearchChange(e.target.value)}\n                onFocus={() => setShowSuggestions(searchTerm.length >= 2)}\n                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}\n                className=\"w-full pl-10 pr-4 py-2.5 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-sm\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}\n              />\n\n              {/* Search Suggestions */}\n              {showSuggestions && searchSuggestions.length > 0 && (\n                <div\n                  className=\"absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg border z-50 max-h-48 overflow-y-auto\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'\n                  }}\n                >\n                  {searchSuggestions.map((suggestion, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleSuggestionClick(suggestion)}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      {suggestion}\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Quick Filters */}\n            <div className=\"flex gap-2\">\n              <button\n                onClick={() => setFilters(prev => ({ ...prev, lowStock: !prev.lowStock }))}\n                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${\n                  filters.lowStock\n                    ? 'bg-orange-100 text-orange-700 border-orange-300'\n                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n                } border`}\n              >\n                <AlertTriangle className=\"h-4 w-4 inline mr-1\" />\n                Low Stock\n              </button>\n              <button\n                onClick={() => setFilters(prev => ({ ...prev, outOfStock: !prev.outOfStock }))}\n                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${\n                  filters.outOfStock\n                    ? 'bg-red-100 text-red-700 border-red-300'\n                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n                } border`}\n              >\n                <X className=\"h-4 w-4 inline mr-1\" />\n                Out of Stock\n              </button>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className={`px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm ${\n                showFilters\n                  ? 'bg-green-100 text-green-700 border-green-300'\n                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n              } border`}\n            >\n              <Filter className=\"h-4 w-4 inline mr-2\" />\n              Filters\n            </button>\n\n            {/* Export Dropdown */}\n            {filteredAndSortedProducts.length > 0 && (\n              <div className=\"relative group\">\n                <button\n                  className=\"px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 border\"\n                >\n                  <Download className=\"h-4 w-4 inline mr-2\" />\n                  Export\n                  <ChevronDown className=\"h-4 w-4 inline ml-1\" />\n                </button>\n\n                {/* Export Dropdown Menu */}\n                <div className=\"absolute right-0 top-full mt-1 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div\n                    className=\"rounded-lg shadow-lg border py-1\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e5e7eb'\n                    }}\n                  >\n                    <button\n                      onClick={handleExportCSV}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <Download className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"text-sm font-medium\">Export as CSV</span>\n                    </button>\n                    <button\n                      onClick={handleExportJSON}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <Download className=\"h-4 w-4 text-blue-600\" />\n                      <span className=\"text-sm font-medium\">Export as JSON</span>\n                    </button>\n                    <div\n                      className=\"my-1 h-px\"\n                      style={{\n                        backgroundColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'\n                      }}\n                    />\n                    <button\n                      onClick={handleExportAnalytics}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <BarChart3 className=\"h-4 w-4 text-purple-600\" />\n                      <span className=\"text-sm font-medium\">Analytics Report</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <button\n              onClick={() => setIsModalOpen(true)}\n              className=\"flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Product\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Filters Panel */}\n        {showFilters && (\n          <div\n            className=\"p-6 rounded-xl border shadow-sm animate-slide-down\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e2e8f0'\n            }}\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {/* Category Filter */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\" style={{\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}>\n                  Category\n                </label>\n                <select\n                  value={filters.category}\n                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}\n                  className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {PRODUCT_CATEGORIES.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Price Range */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\" style={{\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}>\n                  Price Range (₱)\n                </label>\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"number\"\n                    placeholder=\"Min\"\n                    value={filters.priceRange.min}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      priceRange: { ...prev.priceRange, min: Number(e.target.value) || 0 }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                  <input\n                    type=\"number\"\n                    placeholder=\"Max\"\n                    value={filters.priceRange.max}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      priceRange: { ...prev.priceRange, max: Number(e.target.value) || 10000 }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                </div>\n              </div>\n\n              {/* Stock Range */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\" style={{\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}>\n                  Stock Range\n                </label>\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"number\"\n                    placeholder=\"Min\"\n                    value={filters.stockRange.min}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      stockRange: { ...prev.stockRange, min: Number(e.target.value) || 0 }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                  <input\n                    type=\"number\"\n                    placeholder=\"Max\"\n                    value={filters.stockRange.max}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      stockRange: { ...prev.stockRange, max: Number(e.target.value) || 1000 }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                </div>\n              </div>\n\n              {/* Reset Filters */}\n              <div className=\"flex items-end\">\n                <button\n                  onClick={() => setFilters({\n                    category: '',\n                    priceRange: { min: 0, max: 10000 },\n                    stockRange: { min: 0, max: 1000 },\n                    lowStock: false,\n                    outOfStock: false\n                  })}\n                  className=\"w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium\"\n                >\n                  <RefreshCw className=\"h-4 w-4 inline mr-2\" />\n                  Reset Filters\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Toolbar - Sort, View Mode, Bulk Actions */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\">\n          {/* Left Side - Results Info & Bulk Actions */}\n          <div className=\"flex items-center gap-4\">\n            <span className=\"text-sm font-medium\" style={{\n              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n            }}>\n              {filteredAndSortedProducts.length} product{filteredAndSortedProducts.length !== 1 ? 's' : ''} found\n            </span>\n\n            {/* Bulk Selection */}\n            {filteredAndSortedProducts.length > 0 && (\n              <div className=\"flex items-center gap-2\">\n                <button\n                  onClick={handleSelectAll}\n                  className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                  title={selectedProducts.size === filteredAndSortedProducts.length ? 'Deselect All' : 'Select All'}\n                >\n                  {selectedProducts.size === filteredAndSortedProducts.length ? (\n                    <CheckSquare className=\"h-4 w-4 text-green-600\" />\n                  ) : selectedProducts.size > 0 ? (\n                    <Minus className=\"h-4 w-4 text-gray-600\" />\n                  ) : (\n                    <Square className=\"h-4 w-4 text-gray-600\" />\n                  )}\n                </button>\n\n                {selectedProducts.size > 0 && (\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"text-sm text-green-600 font-medium\">\n                      {selectedProducts.size} selected\n                    </span>\n                    <button\n                      onClick={handleBulkDelete}\n                      className=\"px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium\"\n                    >\n                      <Trash2 className=\"h-3 w-3 inline mr-1\" />\n                      Delete\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Right Side - Sort & View Controls */}\n          <div className=\"flex items-center gap-3\">\n            {/* Sort Controls */}\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\" style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}>\n                Sort by:\n              </span>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value as SortOption)}\n                className=\"px-3 py-1.5 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}\n              >\n                <option value=\"created_at\">Date Added</option>\n                <option value=\"name\">Name</option>\n                <option value=\"price\">Price</option>\n                <option value=\"stock\">Stock</option>\n                <option value=\"category\">Category</option>\n              </select>\n              <button\n                onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}\n                className=\"p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                title={`Sort ${sortDirection === 'asc' ? 'Descending' : 'Ascending'}`}\n              >\n                {sortDirection === 'asc' ? (\n                  <SortAsc className=\"h-4 w-4\" style={{\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                  }} />\n                ) : (\n                  <SortDesc className=\"h-4 w-4\" style={{\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                  }} />\n                )}\n              </button>\n            </div>\n\n            {/* View Mode Toggle */}\n            <div className=\"flex rounded-lg border\" style={{\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'\n            }}>\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`p-2 transition-colors ${\n                  viewMode === 'grid'\n                    ? 'bg-green-100 text-green-700'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                title=\"Grid View\"\n              >\n                <Grid className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`p-2 transition-colors ${\n                  viewMode === 'list'\n                    ? 'bg-green-100 text-green-700'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                title=\"List View\"\n              >\n                <List className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Products Grid/List */}\n      <div className={viewMode === 'grid'\n        ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\"\n        : \"space-y-4\"\n      }>\n        {filteredAndSortedProducts.map((product) => (\n          <div\n            key={product.id}\n            className={`relative group rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl ${\n              selectedProducts.has(product.id)\n                ? 'ring-2 ring-green-500 ring-offset-2'\n                : 'hover:scale-[1.02]'\n            } ${viewMode === 'list' ? 'flex items-center' : ''}`}\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n            }}\n          >\n            {/* Selection Checkbox */}\n            <div className=\"absolute top-3 left-3 z-10\">\n              <button\n                onClick={() => handleSelectProduct(product.id)}\n                className={`p-1 rounded-md transition-all duration-200 ${\n                  selectedProducts.has(product.id)\n                    ? 'bg-green-500 text-white'\n                    : 'bg-white/80 text-gray-600 hover:bg-white'\n                } shadow-sm`}\n              >\n                {selectedProducts.has(product.id) ? (\n                  <CheckSquare className=\"h-4 w-4\" />\n                ) : (\n                  <Square className=\"h-4 w-4\" />\n                )}\n              </button>\n            </div>\n\n            {/* Quick Actions Menu */}\n            <div className=\"absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n              <ProductQuickActions\n                product={product}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onDuplicate={(product) => {\n                  // Create a duplicate product\n                  setEditingProduct({\n                    ...product,\n                    id: '',\n                    name: `${product.name} (Copy)`,\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                  })\n                  setIsModalOpen(true)\n                }}\n              />\n            </div>\n\n            {/* Product Image */}\n            <div\n              className={`${viewMode === 'grid' ? 'aspect-square' : 'w-24 h-24 flex-shrink-0'} flex items-center justify-center transition-colors duration-300 relative overflow-hidden cursor-pointer group/image`}\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n              }}\n              onClick={() => openZoom(product.image_url, product.name)}\n            >\n              {product.image_url ? (\n                <>\n                  <Image\n                    src={product.image_url}\n                    alt={product.name}\n                    width={viewMode === 'grid' ? 200 : 96}\n                    height={viewMode === 'grid' ? 200 : 96}\n                    className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                  />\n                  {/* Zoom Overlay */}\n                  <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 flex items-center justify-center\">\n                    <div className=\"bg-white/90 rounded-full p-2\">\n                      <ZoomIn className=\"h-4 w-4 text-gray-700\" />\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <Package\n                  className={`${viewMode === 'grid' ? 'h-16 w-16' : 'h-12 w-12'} transition-colors duration-300`}\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                />\n              )}\n\n              {/* Stock Status Overlay */}\n              {product.stock_quantity === 0 && (\n                <div className=\"absolute inset-0 bg-red-500/20 flex items-center justify-center\">\n                  <span className=\"bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium\">\n                    Out of Stock\n                  </span>\n                </div>\n              )}\n              {product.stock_quantity > 0 && product.stock_quantity < 10 && (\n                <div className=\"absolute top-2 right-2\">\n                  <span className=\"bg-orange-500 text-white px-2 py-1 rounded-md text-xs font-medium\">\n                    Low Stock\n                  </span>\n                </div>\n              )}\n            </div>\n\n            {/* Product Info */}\n            <div className={`${viewMode === 'grid' ? 'p-4' : 'flex-1 p-4'}`}>\n              <div className={`${viewMode === 'list' ? 'flex items-center justify-between' : ''}`}>\n                <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>\n                  <h3\n                    className=\"font-semibold mb-1 transition-colors duration-300 line-clamp-2\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                    }}\n                  >\n                    {product.name}\n                  </h3>\n\n                  <div className={`${viewMode === 'list' ? 'flex items-center gap-4' : 'space-y-2'}`}>\n                    <p\n                      className=\"text-sm transition-colors duration-300\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                      }}\n                    >\n                      {product.category}\n                    </p>\n\n                    <div className={`${viewMode === 'list' ? 'flex items-center gap-4' : 'flex justify-between items-center'}`}>\n                      <span className=\"text-lg font-bold text-green-600\">₱{product.price.toFixed(2)}</span>\n                      <span\n                        className=\"text-sm transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                        }}\n                      >\n                        {product.net_weight}\n                      </span>\n                    </div>\n\n                    <div className={`${viewMode === 'list' ? 'flex items-center gap-2' : 'flex justify-between items-center mb-4'}`}>\n                      <span\n                        className={`text-sm ${\n                          product.stock_quantity === 0\n                            ? 'text-red-600 font-medium'\n                            : product.stock_quantity < 10\n                              ? 'text-orange-600 font-medium'\n                              : ''\n                        }`}\n                        style={{\n                          color: product.stock_quantity >= 10\n                            ? (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')\n                            : undefined\n                        }}\n                      >\n                        Stock: {product.stock_quantity}\n                      </span>\n\n                      {viewMode === 'grid' && (\n                        <div className=\"flex items-center gap-1\">\n                          {product.stock_quantity < 10 && (\n                            <AlertTriangle className=\"h-4 w-4 text-orange-500\" />\n                          )}\n                          <TrendingUp className=\"h-4 w-4 text-green-500\" />\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className={`${viewMode === 'list' ? 'flex gap-2 ml-4' : 'flex gap-2'}`}>\n                  <button\n                    onClick={() => handleEdit(product)}\n                    className=\"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-300 hover:scale-105 font-medium\"\n                    title=\"Edit Product\"\n                  >\n                    <Edit className=\"h-4 w-4 mr-1\" />\n                    {viewMode === 'grid' ? 'Edit' : ''}\n                  </button>\n                  <button\n                    onClick={() => handleDelete(product.id)}\n                    className=\"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-300 hover:scale-105 font-medium\"\n                    title=\"Delete Product\"\n                  >\n                    <Trash2 className=\"h-4 w-4 mr-1\" />\n                    {viewMode === 'grid' ? 'Delete' : ''}\n                  </button>\n                  {viewMode === 'list' && (\n                    <button\n                      className=\"px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 hover:scale-105 font-medium\"\n                      title=\"View Details\"\n                    >\n                      <Eye className=\"h-4 w-4\" />\n                    </button>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredAndSortedProducts.length === 0 && !loading && (\n        <div\n          className=\"text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300\"\n          style={{\n            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',\n            borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n          }}\n        >\n          <div\n            className=\"w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',\n              border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'\n            }}\n          >\n            <Package\n              className=\"h-10 w-10 transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'\n              }}\n            />\n          </div>\n          <h3\n            className=\"text-xl font-semibold mb-3 transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n            }}\n          >\n            {searchTerm || filters.category || filters.lowStock || filters.outOfStock\n              ? 'No products found'\n              : 'No products in your inventory'}\n          </h3>\n          <p\n            className=\"text-sm mb-6 max-w-md mx-auto transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'\n            }}\n          >\n            {searchTerm || filters.category || filters.lowStock || filters.outOfStock\n              ? 'Try adjusting your search terms or filter criteria to find what you\\'re looking for'\n              : 'Get started by adding your first product to build your inventory and start managing your store'}\n          </p>\n          {!searchTerm && !filters.category && !filters.lowStock && !filters.outOfStock && (\n            <button\n              onClick={() => setIsModalOpen(true)}\n              className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl\"\n            >\n              <Plus className=\"h-5 w-5 mr-2\" />\n              Add Your First Product\n            </button>\n          )}\n        </div>\n      )}\n\n      {/* Product Modal */}\n      <ProductModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        product={editingProduct}\n      />\n\n      {/* Image Zoom Modal */}\n      <ProductImageZoom\n        imageUrl={zoomImage?.imageUrl}\n        productName={zoomImage?.productName || ''}\n        isOpen={isZoomOpen}\n        onClose={closeZoom}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;AAoCe,SAAS,gBAAgB,EAAE,aAAa,EAAwB;;IAC7E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,0BAA0B;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,UAAU;QACV,YAAY;YAAE,KAAK;YAAG,KAAK;QAAM;QACjC,YAAY;YAAE,KAAK;YAAG,KAAK;QAAK;QAChC,UAAU;QACV,YAAY;IACd;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,2BAA2B;IAC3B,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE,CAAC;YAC7C,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC5B,qBAAqB,EAAE;gBACvB;YACF;YAEA,MAAM,cAAc,SACjB,MAAM;sFAAC,CAAA,UACN,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW,OACpD,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW;qFAEzD,GAAG;sFAAC,CAAA,UAAW,QAAQ,IAAI;qFAC3B,KAAK,CAAC,GAAG;YAEZ,qBAAqB;mBAAI,IAAI,IAAI;aAAa;QAChD;iEAAG;QAAC;KAAS;IAEb,uCAAuC;IACvC,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8DAAE;YACxC,IAAI,WAAW,SAAS,MAAM;+EAAC,CAAA;oBAC7B,cAAc;oBACd,MAAM,gBAAgB,CAAC,cACrB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;oBAEhE,kBAAkB;oBAClB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;oBAElF,qBAAqB;oBACrB,MAAM,eAAe,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,IAC1D,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG;oBAEzC,qBAAqB;oBACrB,MAAM,eAAe,QAAQ,cAAc,IAAI,QAAQ,UAAU,CAAC,GAAG,IACnE,QAAQ,cAAc,IAAI,QAAQ,UAAU,CAAC,GAAG;oBAElD,mBAAmB;oBACnB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IAAI,QAAQ,cAAc,GAAG;oBAEtE,sBAAsB;oBACtB,MAAM,oBAAoB,CAAC,QAAQ,UAAU,IAAI,QAAQ,cAAc,KAAK;oBAE5E,OAAO,iBAAiB,mBAAmB,gBACzC,gBAAgB,mBAAmB;gBACvC;;YAEA,UAAU;YACV,SAAS,IAAI;sEAAC,CAAC,GAAG;oBAChB,IAAI,QAAa;oBAEjB,OAAQ;wBACN,KAAK;4BACH,SAAS,EAAE,IAAI,CAAC,WAAW;4BAC3B,SAAS,EAAE,IAAI,CAAC,WAAW;4BAC3B;wBACF,KAAK;4BACH,SAAS,EAAE,KAAK;4BAChB,SAAS,EAAE,KAAK;4BAChB;wBACF,KAAK;4BACH,SAAS,EAAE,cAAc;4BACzB,SAAS,EAAE,cAAc;4BACzB;wBACF,KAAK;4BACH,SAAS,EAAE,QAAQ,CAAC,WAAW;4BAC/B,SAAS,EAAE,QAAQ,CAAC,WAAW;4BAC/B;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;4BACvC,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;4BACvC;wBACF;4BACE,OAAO;oBACX;oBAEA,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;oBAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;oBAC3D,OAAO;gBACT;;YAEA,OAAO;QACT;6DAAG;QAAC;QAAU;QAAY;QAAS;QAAQ;KAAc;IAEzD,MAAM,gBAAgB;QACpB,IAAI;YACF,mEAAmE;YACnE,QAAQ,IAAI,CAAC;YACb,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,IAAI,CAAC,6BAA6B;YAE1C,yEAAyE;YACzE,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE;gBACnD,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAC9B,QAAQ,IAAI,CAAC,sCAAsC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAChF,OAEK,IAAI,KAAK,QAAQ,EAAE;gBACtB,YAAY,KAAK,QAAQ;gBACzB,QAAQ,IAAI,CAAC,sCAAsC,KAAK,QAAQ,CAAC,MAAM,EAAE;YAC3E,OAEK,IAAI,MAAM,OAAO,CAAC,OAAO;gBAC5B,YAAY;gBACZ,QAAQ,IAAI,CAAC,qCAAqC,KAAK,MAAM,EAAE;YACjE,OACK;gBACH,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,YAAY,EAAE;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,YAAY,EAAE;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;QAClB;QACA;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,IAAI,KAAK,0BAA0B,MAAM,EAAE;YAC9D,oBAAoB,IAAI;QAC1B,OAAO;YACL,oBAAoB,IAAI,IAAI,0BAA0B,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QACrE;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,YAAY;YAC9B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,oBAAoB;IACtB;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB,IAAI,KAAK,GAAG;QAEjC,MAAM,iBAAiB,CAAC,gCAAgC,EAAE,iBAAiB,IAAI,CAAC,YAAY,CAAC;QAC7F,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA,KACtD,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;oBAAE,QAAQ;gBAAS;YAGlD,MAAM,QAAQ,GAAG,CAAC;YAClB,oBAAoB,IAAI;YACxB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI,iBAAiB,IAAI,KAAK,GAAG;QAEjC,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA;gBACtD,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5C,IAAI,CAAC,SAAS,OAAO,QAAQ,OAAO;gBAEpC,OAAO,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;oBAClC,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,GAAG,OAAO;wBAAE,UAAU;oBAAY;gBAC3D;YACF;YAEA,MAAM,QAAQ,GAAG,CAAC;YAClB,oBAAoB,IAAI;YACxB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,kBAAkB;IAClB,MAAM,qBAAqB,CAAC;QAC1B,cAAc;QACd,0BAA0B;QAC1B,mBAAmB,MAAM,MAAM,IAAI;IACrC;IAEA,MAAM,wBAAwB,CAAC;QAC7B,cAAc;QACd,mBAAmB;IACrB;IAEA,gBAAgB;IAChB,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,QAAQ;YACrB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,UAAU;YACV,iBAAiB;QACnB;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB;QACtB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,2BAA2B,CAAC,SAAS,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IACrG;IAEA,MAAM,mBAAmB;QACvB,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,2BAA2B,CAAC,SAAS,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IACtG;IAEA,MAAM,wBAAwB;QAC5B,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE,2BAA2B,CAAC,kBAAkB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IAChH;IAIA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,iBAAiB,kBAAkB,SAC/B,kEACA;wCACJ,gBAAgB;wCAChB,WAAW;oCACb;;;;;;8CAEF,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,iBAAiB,kBAAkB,SAC/B,kEACA;wCACJ,gBAAgB;wCAChB,WAAW;oCACb;;;;;;;;;;;;sCAGJ,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,iBAAiB,kBAAkB,SAC/B,kEACA;gCACJ,gBAAgB;gCAChB,WAAW;4BACb;;;;;;;;;;;;8BAKJ,6LAAC,wIAAA,CAAA,UAAe;oBAAC,MAAK;oBAAW,OAAO;;;;;;;;;;;;IAG9C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDACL,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;;;;;;0DAEF,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAClD,SAAS,IAAM,mBAAmB,WAAW,MAAM,IAAI;gDACvD,QAAQ,IAAM,WAAW,IAAM,mBAAmB,QAAQ;gDAC1D,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oDACzD,OAAO,kBAAkB,SAAS,YAAY;gDAChD;;;;;;4CAID,mBAAmB,kBAAkB,MAAM,GAAG,mBAC7C,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gDAC3D;0DAEC,kBAAkB,GAAG,CAAC,CAAC,YAAY,sBAClC,6LAAC;wDAEC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAO;4DACL,OAAO,kBAAkB,SAAS,YAAY;wDAChD;kEAEC;uDAPI;;;;;;;;;;;;;;;;kDAef,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,CAAC,KAAK,QAAQ;wDAAC,CAAC;gDACxE,WAAW,CAAC,qEAAqE,EAC/E,QAAQ,QAAQ,GACZ,oDACA,8DACL,OAAO,CAAC;;kEAET,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAGnD,6LAAC;gDACC,SAAS,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,CAAC,KAAK,UAAU;wDAAC,CAAC;gDAC5E,WAAW,CAAC,qEAAqE,EAC/E,QAAQ,UAAU,GACd,2CACA,8DACL,OAAO,CAAC;;kEAET,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;;0CAO3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,CAAC,yEAAyE,EACnF,cACI,iDACA,8DACL,OAAO,CAAC;;0DAET,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;oCAK3C,0BAA0B,MAAM,GAAG,mBAClC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAwB;kEAE5C,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oDAC3D;;sEAEA,6LAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;8EAEA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,6LAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;8EAEA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,YAAY;4DAC1D;;;;;;sEAEF,6LAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;8EAEA,6LAAC,qNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhD,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAOtC,6BACC,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;4CAAiC,OAAO;gDACvD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAAG;;;;;;sDAGH,6LAAC;4CACC,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC1E,WAAU;4CACV,OAAO;gDACL,iBAAiB,kBAAkB,SAAS,YAAY;gDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gDACzD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;;8DAEA,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;wDAAsB,OAAO;kEAAW;uDAA5B;;;;;;;;;;;;;;;;;8CAMnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;4CAAiC,OAAO;gDACvD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAAG;;;;;;sDAGH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;oDAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,YAAY;oEAAE,GAAG,KAAK,UAAU;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAE;4DACrE,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;8DAEF,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;oDAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,YAAY;oEAAE,GAAG,KAAK,UAAU;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAM;4DACzE,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;;;;;;;;;;;;;8CAMN,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;4CAAiC,OAAO;gDACvD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAAG;;;;;;sDAGH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;oDAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,YAAY;oEAAE,GAAG,KAAK,UAAU;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAE;4DACrE,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;8DAEF,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;oDAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,YAAY;oEAAE,GAAG,KAAK,UAAU;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAK;4DACxE,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;;;;;;;;;;;;;8CAMN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,WAAW;gDACxB,UAAU;gDACV,YAAY;oDAAE,KAAK;oDAAG,KAAK;gDAAM;gDACjC,YAAY;oDAAE,KAAK;oDAAG,KAAK;gDAAK;gDAChC,UAAU;gDACV,YAAY;4CACd;wCACA,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;;;;;;kCASvD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;wCAAsB,OAAO;4CAC3C,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;4CACG,0BAA0B,MAAM;4CAAC;4CAAS,0BAA0B,MAAM,KAAK,IAAI,MAAM;4CAAG;;;;;;;oCAI9F,0BAA0B,MAAM,GAAG,mBAClC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO,iBAAiB,IAAI,KAAK,0BAA0B,MAAM,GAAG,iBAAiB;0DAEpF,iBAAiB,IAAI,KAAK,0BAA0B,MAAM,iBACzD,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;2DACrB,iBAAiB,IAAI,GAAG,kBAC1B,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAEjB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;4CAIrB,iBAAiB,IAAI,GAAG,mBACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DACb,iBAAiB,IAAI;4DAAC;;;;;;;kEAEzB,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;0CAUtD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;gDAAsB,OAAO;oDAC3C,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAAG;;;;;;0DAGH,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oDACzD,OAAO,kBAAkB,SAAS,YAAY;gDAChD;;kEAEA,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;0DAE3B,6LAAC;gDACC,SAAS,IAAM,iBAAiB,kBAAkB,QAAQ,SAAS;gDACnE,WAAU;gDACV,OAAO,CAAC,KAAK,EAAE,kBAAkB,QAAQ,eAAe,aAAa;0DAEpE,kBAAkB,sBACjB,6LAAC,iOAAA,CAAA,UAAO;oDAAC,WAAU;oDAAU,OAAO;wDAClC,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;yEAEA,6LAAC,oOAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAU,OAAO;wDACnC,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;;;;;;;;;;;;kDAMN,6LAAC;wCAAI,WAAU;wCAAyB,OAAO;4CAC7C,QAAQ,kBAAkB,SAAS,sBAAsB;wCAC3D;;0DACE,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,sBAAsB,EAChC,aAAa,SACT,gCACA,4CACJ;gDACF,OAAM;0DAEN,cAAA,6LAAC,4MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,sBAAsB,EAChC,aAAa,SACT,gCACA,4CACJ;gDACF,OAAM;0DAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6LAAC;gBAAI,WAAW,aAAa,SACzB,wEACA;0BAED,0BAA0B,GAAG,CAAC,CAAC,wBAC9B,6LAAC;wBAEC,WAAW,CAAC,gGAAgG,EAC1G,iBAAiB,GAAG,CAAC,QAAQ,EAAE,IAC3B,wCACA,qBACL,CAAC,EAAE,aAAa,SAAS,sBAAsB,IAAI;wBACpD,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;;0CAGA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;oCAC7C,WAAW,CAAC,2CAA2C,EACrD,iBAAiB,GAAG,CAAC,QAAQ,EAAE,IAC3B,4BACA,2CACL,UAAU,CAAC;8CAEX,iBAAiB,GAAG,CAAC,QAAQ,EAAE,kBAC9B,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAMxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4IAAA,CAAA,UAAmB;oCAClB,SAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,aAAa,CAAC;wCACZ,6BAA6B;wCAC7B,kBAAkB;4CAChB,GAAG,OAAO;4CACV,IAAI;4CACJ,MAAM,GAAG,QAAQ,IAAI,CAAC,OAAO,CAAC;4CAC9B,YAAY,IAAI,OAAO,WAAW;4CAClC,YAAY,IAAI,OAAO,WAAW;wCACpC;wCACA,eAAe;oCACjB;;;;;;;;;;;0CAKJ,6LAAC;gCACC,WAAW,GAAG,aAAa,SAAS,kBAAkB,0BAA0B,oHAAoH,CAAC;gCACrM,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;gCACA,SAAS,IAAM,SAAS,QAAQ,SAAS,EAAE,QAAQ,IAAI;;oCAEtD,QAAQ,SAAS,iBAChB;;0DACE,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,SAAS;gDACtB,KAAK,QAAQ,IAAI;gDACjB,OAAO,aAAa,SAAS,MAAM;gDACnC,QAAQ,aAAa,SAAS,MAAM;gDACpC,WAAU;;;;;;0DAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;qEAKxB,6LAAC,2MAAA,CAAA,UAAO;wCACN,WAAW,GAAG,aAAa,SAAS,cAAc,YAAY,+BAA+B,CAAC;wCAC9F,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;oCAKH,QAAQ,cAAc,KAAK,mBAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAiE;;;;;;;;;;;oCAKpF,QAAQ,cAAc,GAAG,KAAK,QAAQ,cAAc,GAAG,oBACtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAoE;;;;;;;;;;;;;;;;;0CAQ1F,6LAAC;gCAAI,WAAW,GAAG,aAAa,SAAS,QAAQ,cAAc;0CAC7D,cAAA,6LAAC;oCAAI,WAAW,GAAG,aAAa,SAAS,sCAAsC,IAAI;;sDACjF,6LAAC;4CAAI,WAAW,GAAG,aAAa,SAAS,WAAW,IAAI;;8DACtD,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;oDAChD;8DAEC,QAAQ,IAAI;;;;;;8DAGf,6LAAC;oDAAI,WAAW,GAAG,aAAa,SAAS,4BAA4B,aAAa;;sEAChF,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;sEAEC,QAAQ,QAAQ;;;;;;sEAGnB,6LAAC;4DAAI,WAAW,GAAG,aAAa,SAAS,4BAA4B,qCAAqC;;8EACxG,6LAAC;oEAAK,WAAU;;wEAAmC;wEAAE,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;8EAC3E,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EAEC,QAAQ,UAAU;;;;;;;;;;;;sEAIvB,6LAAC;4DAAI,WAAW,GAAG,aAAa,SAAS,4BAA4B,0CAA0C;;8EAC7G,6LAAC;oEACC,WAAW,CAAC,QAAQ,EAClB,QAAQ,cAAc,KAAK,IACvB,6BACA,QAAQ,cAAc,GAAG,KACvB,gCACA,IACN;oEACF,OAAO;wEACL,OAAO,QAAQ,cAAc,IAAI,KAC5B,kBAAkB,SAAS,YAAY,YACxC;oEACN;;wEACD;wEACS,QAAQ,cAAc;;;;;;;gEAG/B,aAAa,wBACZ,6LAAC;oEAAI,WAAU;;wEACZ,QAAQ,cAAc,GAAG,oBACxB,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;sFAE3B,6LAAC;4EAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQhC,6LAAC;4CAAI,WAAW,GAAG,aAAa,SAAS,oBAAoB,cAAc;;8DACzE,6LAAC;oDACC,SAAS,IAAM,WAAW;oDAC1B,WAAU;oDACV,OAAM;;sEAEN,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,aAAa,SAAS,SAAS;;;;;;;8DAElC,6LAAC;oDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;oDACtC,WAAU;oDACV,OAAM;;sEAEN,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,aAAa,SAAS,WAAW;;;;;;;gDAEnC,aAAa,wBACZ,6LAAC;oDACC,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA3LpB,QAAQ,EAAE;;;;;;;;;;YAqMpB,0BAA0B,MAAM,KAAK,KAAK,CAAC,yBAC1C,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,0BAA0B;oBACtE,aAAa,kBAAkB,SAAS,YAAY;gBACtD;;kCAEA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,2BAA2B;4BACvE,QAAQ,kBAAkB,SAAS,qCAAqC;wBAC1E;kCAEA,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BACN,WAAU;4BACV,OAAO;gCACL,OAAO,kBAAkB,SAAS,YAAY;4BAChD;;;;;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCAEC,cAAc,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,IAAI,QAAQ,UAAU,GACrE,sBACA;;;;;;kCAEN,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCAEC,cAAc,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,IAAI,QAAQ,UAAU,GACrE,wFACA;;;;;;oBAEL,CAAC,cAAc,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,UAAU,kBAC3E,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAQzC,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;0BAIX,6LAAC,yIAAA,CAAA,UAAgB;gBACf,UAAU,WAAW;gBACrB,aAAa,WAAW,eAAe;gBACvC,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB;GApgCwB;;QACI,mJAAA,CAAA,WAAQ;QAyBqB,yIAAA,CAAA,eAAY;;;KA1B7C", "debugId": null}}]}